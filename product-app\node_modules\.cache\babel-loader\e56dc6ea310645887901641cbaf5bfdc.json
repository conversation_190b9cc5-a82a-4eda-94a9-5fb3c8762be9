{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue", "mtime": 1755081172689}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdXNlUm91dGUgfSBmcm9tICd2dWUtcm91dGVyJzsKaW1wb3J0IHsgb25Nb3VudGVkLCByZWFjdGl2ZSwgdG9SZWZzIH0gZnJvbSAndnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNYWpvclByb2plY3RzRGV0YWlsc0NvcHknLAogIGNvbXBvbmVudHM6IHt9LAogIHNldHVwKCkgewogICAgY29uc3Qgcm91dGUgPSB1c2VSb3V0ZSgpOwogICAgY29uc3QgZGF0YSA9IHJlYWN0aXZlKHsKICAgICAgdGl0bGU6IHJvdXRlLnF1ZXJ5LnRpdGxlIHx8ICfor6bmg4UnLAogICAgICBpZDogcm91dGUucXVlcnkuaWQsCiAgICAgIGRldGFpbHM6IHt9CiAgICB9KTsKICAgIG9uTW91bnRlZCgoKSA9PiB7fSk7CiAgICByZXR1cm4gewogICAgICAuLi50b1JlZnMoZGF0YSkKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["useRoute", "onMounted", "reactive", "toRefs", "name", "components", "setup", "route", "data", "title", "query", "id", "details"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MajorProjectsDetailsCopy\">\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'MajorProjectsDetailsCopy',\r\n  components: {\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const data = reactive({\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {}\r\n    })\r\n    onMounted(() => {\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.MajorProjectsDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>\r\n"], "mappings": "AAMA,SAASA,QAAO,QAAS,YAAW;AACpC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAK,QAAS,KAAI;AAChD,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,UAAU,EAAE,CACZ,CAAC;EACDC,KAAIA,CAAA,EAAK;IACP,MAAMC,KAAI,GAAIP,QAAQ,CAAC;IACvB,MAAMQ,IAAG,GAAIN,QAAQ,CAAC;MACpBO,KAAK,EAAEF,KAAK,CAACG,KAAK,CAACD,KAAI,IAAK,IAAI;MAChCE,EAAE,EAAEJ,KAAK,CAACG,KAAK,CAACC,EAAE;MAClBC,OAAO,EAAE,CAAC;IACZ,CAAC;IACDX,SAAS,CAAC,MAAM,CAChB,CAAC;IACD,OAAO;MAAE,GAAGE,MAAM,CAACK,IAAI;IAAE;EAC3B;AACF", "ignoreList": []}]}