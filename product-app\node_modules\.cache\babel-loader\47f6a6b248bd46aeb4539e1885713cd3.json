{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755081173843}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdXNlUm91dGUgfSBmcm9tICd2dWUtcm91dGVyJzsKaW1wb3J0IHsgb25Nb3VudGVkLCByZWFjdGl2ZSwgdG9SZWZzIH0gZnJvbSAndnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdhdHRyYWN0SW52ZXN0bWVudERldGFpbHNDb3B5JywKICBjb21wb25lbnRzOiB7fSwKICBzZXR1cCgpIHsKICAgIGNvbnN0IHJvdXRlID0gdXNlUm91dGUoKTsKICAgIGNvbnN0IGRhdGEgPSByZWFjdGl2ZSh7CiAgICAgIHRpdGxlOiByb3V0ZS5xdWVyeS50aXRsZSB8fCAn6K+m5oOFJywKICAgICAgaWQ6IHJvdXRlLnF1ZXJ5LmlkLAogICAgICBkZXRhaWxzOiB7fQogICAgfSk7CiAgICBvbk1vdW50ZWQoKCkgPT4ge30pOwogICAgcmV0dXJuIHsKICAgICAgLi4udG9SZWZzKGRhdGEpCiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["useRoute", "onMounted", "reactive", "toRefs", "name", "components", "setup", "route", "data", "title", "query", "id", "details"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const data = reactive({\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {}\r\n    })\r\n    onMounted(() => {\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>\r\n"], "mappings": "AAMA,SAASA,QAAO,QAAS,YAAW;AACpC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAK,QAAS,KAAI;AAChD,eAAe;EACbC,IAAI,EAAE,8BAA8B;EACpCC,UAAU,EAAE,CACZ,CAAC;EACDC,KAAIA,CAAA,EAAK;IACP,MAAMC,KAAI,GAAIP,QAAQ,CAAC;IACvB,MAAMQ,IAAG,GAAIN,QAAQ,CAAC;MACpBO,KAAK,EAAEF,KAAK,CAACG,KAAK,CAACD,KAAI,IAAK,IAAI;MAChCE,EAAE,EAAEJ,KAAK,CAACG,KAAK,CAACC,EAAE;MAClBC,OAAO,EAAE,CAAC;IACZ,CAAC;IACDX,SAAS,CAAC,MAAM,CAChB,CAAC;IACD,OAAO;MAAE,GAAGE,MAAM,CAACK,IAAI;IAAE;EAC3B;AACF", "ignoreList": []}]}