{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755082587569}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "inject", "onMounted", "reactive", "toRefs", "name", "components", "_NavBar", "_Sticky", "_<PERSON>ull<PERSON><PERSON>resh", "setup", "route", "$general", "$api", "$appTheme", "$isShowHead", "data", "has<PERSON><PERSON>", "appTheme", "isShowHead", "title", "query", "id", "details", "refreshing", "document", "api", "getInfo", "onRefresh", "setTimeout", "res", "ImportantWork", "info", "error", "console", "formatDate", "dateStr", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getTypeText", "type", "typeMap", "onClickLeft", "history", "back"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" title=\"详情\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n      <div class=\"details-container\">\r\n        <!-- 标题 -->\r\n        <div class=\"title\">{{ details.title || '孟庆斌主席在成都市拜访清华四川能源互联网研究院' }}</div>\r\n\r\n        <!-- 日期和标签 -->\r\n        <div class=\"header-info\">\r\n          <div class=\"date\">{{ formatDate(details.publishDate) || '2025-04-02' }}</div>\r\n          <div class=\"tag\">{{ getTypeText(details.type) || '外出拜访' }}</div>\r\n        </div>\r\n\r\n        <!-- 详情信息 -->\r\n        <div class=\"info-section\">\r\n          <div class=\"info-item\" v-if=\"details.publishDept\">\r\n            <div class=\"label\">地点：</div>\r\n            <div class=\"value\">{{ details.publishDept }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectWay\">\r\n            <div class=\"label\">带队领导姓名及职务：</div>\r\n            <div class=\"value\">{{ details.projectWay }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.companyNumber\">\r\n            <div class=\"label\">企业序号：</div>\r\n            <div class=\"value\">{{ details.companyNumber }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAreaName\">\r\n            <div class=\"label\">拜访企业名称：</div>\r\n            <div class=\"value\">{{ details.projectAreaName }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectSuggestion\">\r\n            <div class=\"label\">洽谈项目名称：</div>\r\n            <div class=\"value\">{{ details.projectSuggestion }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAppeal\">\r\n            <div class=\"label\">洽谈项目价值：</div>\r\n            <div class=\"value\">{{ details.projectAppeal }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.businessName\">\r\n            <div class=\"label\">洽谈情况：</div>\r\n            <div class=\"value content-text\" v-html=\"details.businessName\"></div>\r\n          </div>\r\n\r\n          <!-- 如果没有数据，显示默认内容 -->\r\n          <template v-if=\"!details.publishDept && !details.projectWay && !details.projectAreaName\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">地点：</div>\r\n              <div class=\"value\">北京</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">带队领导姓名及职务：</div>\r\n              <div class=\"value\">其他，市政协副主席，市工商联主席</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">企业序号：</div>\r\n              <div class=\"value\">1</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">拜访企业名称：</div>\r\n              <div class=\"value\">清华四川能源互联网研究院</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目名称：</div>\r\n              <div class=\"value\">建设一体飞行平台及发展游戏项目</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目价值：</div>\r\n              <div class=\"value\">储备在谈</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈情况：</div>\r\n              <div class=\"value content-text\">\r\n                重点围绕低空经济生态打造，在青岛地区开展飞行平台及发展游戏项目等方面进行了深入交流。双方将持续飞行系统等部分产品，推动项目落地。最终，双方就青岛低空经济应用通道</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </van-pull-refresh>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { NavBar, Sticky, PullRefresh } from 'vant'\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [PullRefresh.name]: PullRefresh\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const $general = inject('$general')\r\n    const $api = inject('$api')\r\n    const $appTheme = inject('$appTheme')\r\n    const $isShowHead = inject('$isShowHead')\r\n\r\n    const data = reactive({\r\n      hasApi: false,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {},\r\n      refreshing: false\r\n    })\r\n\r\n    if (data.title) {\r\n      document.title = data.title\r\n    }\r\n\r\n    if (typeof (api) === 'undefined') {\r\n      data.hasApi = false\r\n    } else {\r\n      data.hasApi = true\r\n    }\r\n\r\n    onMounted(() => {\r\n      getInfo()\r\n    })\r\n\r\n    const onRefresh = () => {\r\n      setTimeout(() => {\r\n        getInfo()\r\n      }, 520)\r\n    }\r\n\r\n    // 详情请求\r\n    const getInfo = async () => {\r\n      try {\r\n        const res = await $api.ImportantWork.info(data.id)\r\n        var { data: details } = res\r\n        data.details = details\r\n        data.refreshing = false\r\n      } catch (error) {\r\n        console.error('获取详情失败:', error)\r\n        data.refreshing = false\r\n      }\r\n    }\r\n\r\n    // 格式化日期\r\n    const formatDate = (dateStr) => {\r\n      if (!dateStr) return ''\r\n      const date = new Date(dateStr)\r\n      return date.getFullYear() + '-' +\r\n        String(date.getMonth() + 1).padStart(2, '0') + '-' +\r\n        String(date.getDate()).padStart(2, '0')\r\n    }\r\n\r\n    // 获取类型文本\r\n    const getTypeText = (type) => {\r\n      const typeMap = {\r\n        '1': '外出拜访',\r\n        '2': '在青接待',\r\n        '3': '自主举办'\r\n      }\r\n      return typeMap[type] || '外出拜访'\r\n    }\r\n\r\n    const onClickLeft = () => {\r\n      history.back()\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      $general,\r\n      onRefresh,\r\n      onClickLeft,\r\n      formatDate,\r\n      getTypeText\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n\r\n  .details-container {\r\n    padding: 20px;\r\n\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      margin-bottom: 16px;\r\n      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n    }\r\n\r\n    .header-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #F4F4F4;\r\n\r\n      .date {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n      }\r\n\r\n      .tag {\r\n        background: rgba(0, 122, 255, 0.12);\r\n        border: 1px solid #007AFF;\r\n        border-radius: 4px;\r\n        padding: 4px 8px;\r\n        font-size: 12px;\r\n        color: #007AFF;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .info-section {\r\n      .info-item {\r\n        margin-bottom: 20px;\r\n\r\n        .label {\r\n          font-size: 16px;\r\n          color: #999999;\r\n          font-weight: 600;\r\n          margin-bottom: 8px;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #333333;\r\n          font-weight: 600;\r\n          line-height: 1.5;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n\r\n          &.content-text {\r\n            line-height: 1.6;\r\n            font-weight: normal;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 导航栏样式\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAkGA,SAASA,QAAO,QAAS,YAAW;AACpC,SAASC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAK,QAAS,KAAI;AACxD,eAAe;EACbC,IAAI,EAAE,8BAA8B;EACpCC,UAAU,EAAE;IACV,CAACC,OAAA,CAAOF,IAAI,GAAAE,OAAS;IACrB,CAACC,OAAA,CAAOH,IAAI,GAAAG,OAAS;IACrB,CAACC,YAAA,CAAYJ,IAAI,GAAAI;EACnB,CAAC;EACDC,KAAIA,CAAA,EAAK;IACP,MAAMC,KAAI,GAAIX,QAAQ,CAAC;IACvB,MAAMY,QAAO,GAAIX,MAAM,CAAC,UAAU;IAClC,MAAMY,IAAG,GAAIZ,MAAM,CAAC,MAAM;IAC1B,MAAMa,SAAQ,GAAIb,MAAM,CAAC,WAAW;IACpC,MAAMc,WAAU,GAAId,MAAM,CAAC,aAAa;IAExC,MAAMe,IAAG,GAAIb,QAAQ,CAAC;MACpBc,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAEJ,SAAS;MACnBK,UAAU,EAAEJ,WAAW;MACvBK,KAAK,EAAET,KAAK,CAACU,KAAK,CAACD,KAAI,IAAK,IAAI;MAChCE,EAAE,EAAEX,KAAK,CAACU,KAAK,CAACC,EAAE;MAClBC,OAAO,EAAE,CAAC,CAAC;MACXC,UAAU,EAAE;IACd,CAAC;IAED,IAAIR,IAAI,CAACI,KAAK,EAAE;MACdK,QAAQ,CAACL,KAAI,GAAIJ,IAAI,CAACI,KAAI;IAC5B;IAEA,IAAI,OAAQM,GAAG,KAAM,WAAW,EAAE;MAChCV,IAAI,CAACC,MAAK,GAAI,KAAI;IACpB,OAAO;MACLD,IAAI,CAACC,MAAK,GAAI,IAAG;IACnB;IAEAf,SAAS,CAAC,MAAM;MACdyB,OAAO,CAAC;IACV,CAAC;IAED,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBC,UAAU,CAAC,MAAM;QACfF,OAAO,CAAC;MACV,CAAC,EAAE,GAAG;IACR;;IAEA;IACA,MAAMA,OAAM,GAAI,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACF,MAAMG,GAAE,GAAI,MAAMjB,IAAI,CAACkB,aAAa,CAACC,IAAI,CAAChB,IAAI,CAACM,EAAE;QACjD,IAAI;UAAEN,IAAI,EAAEO;QAAQ,IAAIO,GAAE;QAC1Bd,IAAI,CAACO,OAAM,GAAIA,OAAM;QACrBP,IAAI,CAACQ,UAAS,GAAI,KAAI;MACxB,EAAE,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BjB,IAAI,CAACQ,UAAS,GAAI,KAAI;MACxB;IACF;;IAEA;IACA,MAAMW,UAAS,GAAKC,OAAO,IAAK;MAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,EAAC;MACtB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,OAAO;MAC7B,OAAOC,IAAI,CAACE,WAAW,CAAC,IAAI,GAAE,GAC5BC,MAAM,CAACH,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,IAAI,GAAE,GACjDF,MAAM,CAACH,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG;IAC1C;;IAEA;IACA,MAAME,WAAU,GAAKC,IAAI,IAAK;MAC5B,MAAMC,OAAM,GAAI;QACd,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,MAAM;QACX,GAAG,EAAE;MACP;MACA,OAAOA,OAAO,CAACD,IAAI,KAAK,MAAK;IAC/B;IAEA,MAAME,WAAU,GAAIA,CAAA,KAAM;MACxBC,OAAO,CAACC,IAAI,CAAC;IACf;IAEA,OAAO;MACL,GAAG7C,MAAM,CAACY,IAAI,CAAC;MACfJ,QAAQ;MACRgB,SAAS;MACTmB,WAAW;MACXZ,UAAU;MACVS;IACF;EACF;AACF", "ignoreList": []}]}