<template>
  <div class="performanceDutiesList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      style="min-height: 80vh;"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist_box"
            style="background: #FFF;">
          <li v-for="(item,index) in listData"
              :key="index"
              @click="openDetails(item)"
              class="van-hairline--bottom click"
              style="padding: 8px 12px;">
            <div class="text_two"
                 :style="$general.loadConfiguration(-1)+'color:#333;'">{{item.name}}</div>
            <div :style="$general.loadConfiguration(-6)+'color:#333;margin-top:12px;'">{{dayjs(item.date || item.longDate).format('YYYY-MM-DD HH:mm')}}</div>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
/* eslint-disable */
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'performanceDutiesList',
  components: {
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      title: route.query.title || '详情',
      id: route.query.id,
      year: route.query.year,
      listData: [],
      correspond: route.query.correspond
    })

    onMounted(() => {
    })

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getWisemanUserDutyDetail()
    }
    const onLoad = () => {
      data.loading = true
      getWisemanUserDutyDetail()
    }
    // 获取履职类型里边的列表
    const getWisemanUserDutyDetail = async () => {
      var postParam = {
        userId: data.id,
        year: data.year
      }
      const res = await $api.thinkTanks.findDutyDetail(postParam)
      console.log('res==>', res)
      data.refreshing = false
      var code = res ? res.errcode : ''
      var info = res.data || {}
      var total = res.total
      data.listData = []
      if (code === 200) {
        console.log('data.correspond==>', data.correspond)
        data.listData = info[data.correspond] || []
        console.log('data.listData==>', data.listData)
      }
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 点击履职列表进详细
    const openDetails = async (row) => {
      console.log('row==>>', row)
      router.push({ name: 'performanceDutiesDetails', query: { id: data.id, title: row.name, content: row.content, time: row.date } })
    }
    return { ...toRefs(data), onRefresh, dayjs, onLoad, $general, openDetails }
  }
}
</script>

<style lang="less" >
.performanceDutiesList {
  width: 100%;
}
</style>

