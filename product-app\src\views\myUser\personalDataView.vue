<template>
  <div class="personalDataView">
    <div class="tablebox"
         style="background: #fff;margin: 12px;">
      <div class="z_container">
        <div class="flex_box z_panel">
          <div class="zl">
            <img :src="img.url"
                 alt=""
                 style="padding: 10px;width: 140px;">
          </div>
          <div class="zr">
            <div class="z_row flex_box"
                 :style="$general.loadConfiguration(-3)">
              <div class="z_label">姓名</div>
              <div class="z_value">{{name}}{{remarks}}</div>
            </div>
            <div class="z_row flex_box"
                 :style="$general.loadConfiguration(-3)">
              <div class="z_label">性别</div>
              <div class="z_value">{{sex}}</div>
            </div>
            <div class="z_row flex_box"
                 :style="$general.loadConfiguration(-3)">
              <div class="z_label_text1">所属部门</div>
              <div class="z_value_text1">{{officeName}}</div>
            </div>
            <div class="z_row flex_box">
              <div class="z_label">职务</div>
              <div class="z_value">{{des}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import { useRoute } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet } from 'vant'
export default {
  name: 'personalDataView',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem
  },
  setup () {
    // const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id || JSON.parse(sessionStorage.getItem('user').id) || '',
      img: { url: '' },
      name: '',
      remarks: '',
      sex: '',
      officeName: '',
      des: ''
    })
    onMounted(() => {
      getUserInfo()
    })
    const getUserInfo = async () => {
      var res = await $api.general.memberLook({
        id: data.id,
        areaId: sessionStorage.getItem('areaId'),
        memberType: '3'
      })
      var { data: info } = res
      data.img.url = info.headImg || ''
      data.name = info.userName
      data.remarks = info.remarks
      data.sex = info.sex
      data.officeName = info.officeName
      data.des = info.position
    }
    return { ...toRefs(data), $general }
  }
}
</script>
<style lang="less">
.personalDataView {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;
  .z_container {
  }

  .z_panel {
  }

  .z_panel .zl {
    flex: 1.5;
  }

  .z_panel .zr {
    flex: 2;
  }

  .z_container .z_row:not(.z_panel),
  .z_container .z_panel.z_row > [class*="z"] {
    padding: 10px 0;
    /* line-height: 1; */
  }

  .z_container .z_label {
    color: #c9c9c9;
    white-space: nowrap;
    margin-left: 18px;
  }

  .z_container .z_value {
    margin-left: 20px;
    /* margin-right: 0.2rem; */
    color: #292929;
  }

  .z_container .z_panel .z_row .z_value {
    margin-left: 26px;
  }

  .z_container .z_panel .z_row ~ .z_row,
  .z_container > .z_row {
    border-top: 1px solid #ebebeb;
  }

  .z_container .z_panel .zr {
    border-left: 1px solid #ebebeb;
  }

  .z_container .z_label_text1 {
    color: #c9c9c9;
    white-space: nowrap;
    margin-left: 7px;
  }

  .z_container .z_value_text1 {
    margin-left: 10px;
    color: #292929;
  }
}
</style>
