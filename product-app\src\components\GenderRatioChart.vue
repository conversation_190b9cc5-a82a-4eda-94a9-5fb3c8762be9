<template>
  <div class="gender-ratio-chart">
    <div class="chart-container">
      <!-- ECharts饼图 -->
      <div ref="chartRef" class="chart"></div>
      <!-- 性别统计信息 -->
      <div class="gender-stats">
        <div class="gender-item male">
          <div class="gender-icon male-icon">
            <div class="icon-bg"></div>
          </div>
          <div class="gender-info">
            <div class="gender-text">男性 {{ maleCount }} 名</div>
            <div class="gender-percent">{{ malePercent }}%</div>
          </div>
        </div>
        <div class="gender-item female">
          <div class="gender-icon female-icon">
            <div class="icon-bg"></div>
          </div>
          <div class="gender-info">
            <div class="gender-text">女性 {{ femaleCount }} 名</div>
            <div class="gender-percent">{{ femalePercent }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'GenderRatioChart',
  props: {
    maleCount: {
      type: Number,
      default: 314
    },
    femaleCount: {
      type: Number,
      default: 108
    },
    chartHeight: {
      type: String,
      default: '120px'
    },
    chartWidth: {
      type: String,
      default: '120px'
    }
  },
  setup (props) {
    const chartRef = ref(null)
    let chartInstance = null

    // 计算总人数和百分比
    const totalCount = computed(() => props.maleCount + props.femaleCount)
    const malePercent = computed(() => Math.round((props.maleCount / totalCount.value) * 100))
    const femalePercent = computed(() => Math.round((props.femaleCount / totalCount.value) * 100))

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        series: [
          {
            type: 'pie',
            radius: ['35%', '65%'],
            center: ['50%', '50%'],
            data: [
              {
                value: props.maleCount,
                name: '男性',
                itemStyle: {
                  color: '#4AA3FF'
                }
              },
              {
                value: props.femaleCount,
                name: '女性',
                itemStyle: {
                  color: '#FF6B9D'
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return Math.random() * 200
            }
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance) return

      const option = {
        series: [
          {
            data: [
              {
                value: props.maleCount,
                name: '男性',
                itemStyle: {
                  color: '#4AA3FF'
                }
              },
              {
                value: props.femaleCount,
                name: '女性',
                itemStyle: {
                  color: '#FF6B9D'
                }
              }
            ]
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 监听数据变化
    watch(
      () => [props.maleCount, props.femaleCount],
      () => {
        nextTick(() => {
          updateChart()
        })
      },
      { deep: true }
    )

    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef,
      malePercent,
      femalePercent
    }
  }
}
</script>

<style lang="less" scoped>
.gender-ratio-chart {
  .chart-container {
    display: flex;
    align-items: center;
    gap: 30px;

    .chart {
      width: v-bind(chartWidth);
      height: v-bind(chartHeight);
      flex-shrink: 0;
    }

    .gender-stats {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15px;

      .gender-item {
        display: flex;
        align-items: center;
        gap: 12px;

        .gender-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .icon-bg {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px dashed #ccc;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 12px;
              height: 12px;
              border-radius: 50%;
              background: white;
            }
          }
        }

        .male-icon {
          background: #4AA3FF;

          .icon-bg {
            background: #4AA3FF;
            border-color: #4AA3FF;
          }
        }

        .female-icon {
          background: #FF6B9D;

          .icon-bg {
            background: #FF6B9D;
            border-color: #FF6B9D;
          }
        }

        .gender-info {
          flex: 1;

          .gender-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
          }

          .gender-percent {
            font-size: 16px;
            font-weight: bold;
          }
        }

        &.male .gender-percent {
          color: #4AA3FF;
        }

        &.female .gender-percent {
          color: #FF6B9D;
        }
      }
    }
  }
}
</style>