<template>
  <writeRemeber :show="show"
                ref="writefn" />
  <div class="bookDetail">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   title="书籍详情"
                   left-text=""
                   right-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="onClickRight">
        <template #right>
          <van-icon name="share-o"
                    size="18" />
        </template>
      </van-nav-bar>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                :finished-text="(listData.length ===0?'':'没有更多了')"
                offset="52"
                @load="onLoad">
        <!--书本信息-->
        <div class="flex_box details_box">
          <div @click="goBookReader(txt);"
               class="details_img">
            <img v-if="txt.bookType == '2'"
                 class="item_Sound"
                 :src="icon_hasSound" />
            <img v-if="txt.isAvailable == '0'"
                 class="item_overdue"
                 src="../../../assets/img/overdue.png" />
            <a v-if="isIOS"
               :href="bookUrl + addUrl + signUrl">
              <img :src="img.url || icon_no_data"
                   style="object-fit: cover;border-radius: 2px;" />
            </a>
            <img v-else
                 :src="img.url || icon_no_data"
                 style="object-fit: cover;border-radius: 2px;" />
            <div v-if="entity"
                 class="details_entity"
                 v-html="'实体书'"></div>
          </div>
          <div class="flex_placeholder">
            <div class="details_name"
                 v-html="name"></div>
            <div class="text_one2"
                 :style="'font-size:12px;color:'+appTheme"
                 v-html="author"></div>
            <div v-if="label.length != 0"
                 class="details_label flex_box flex_align_center T-flex-flow-row-wrap">
              <div v-for="(nItem) in label"
                   :key="nItem"
                   :style="'font-size:12px;padding:0 5px 5px 0;'">
                <van-tag plain
                         color="#555">{{nItem}}</van-tag>
              </div>
            </div>
            <div v-if="time"
                 class="details_time"
                 :style="'font-size:12px;color:#888;margin-top:5px;'"
                 v-html="time"></div>
          </div>
        </div>
        <!--简介-->
        <div @click="isSummary = !isSummary;"
             class="details_summary">
          <div :class="isSummary?'text_two':''">
            <span :style="'font-size:14px;font-weight: bold;'">简介：</span>
            <span :style="'font-size:14px;'">{{summary}}</span>
          </div>
          <!--<div v-if="hasSummary"  :style="loadConfiguration(-4)+'padding:0.05rem 0.1rem 0 0;color:'+appTheme">{{isSummary?'展开':'收起'}}</div>-->
        </div>
        <!--按钮-->
        <div style="padding: 5px 10px;">
          <div class="details_statistics flex_box flex_align_center">
            <template v-for="(nItem,nIndex) in detailsBtn"
                      :key="nItem.id">
              <div v-if="nIndex != 0"
                   class="details_statistics_line"></div>
              <div @click="detailsBtnClick(nItem)"
                   class="details_statistics_item click">
                <div :style="'font-size:12px;margin-bottom:10px;color:'+nItem.color">{{nItem.hint}}</div>
                <div><span :style="'font-weight: bold;'">{{nItem.value}}</span><span :style="'font-size:14px;'">{{nItem.unit}}</span></div>
              </div>
            </template>
          </div>
        </div>
        <!--评论 v-if="!commentList.hide"-->
        <div class="van-hairline--top"
             style="margin-top: 10px;">
          <div class="commentList_hint flex_box flex_align_center">
            <!-- <div :style="'font-size:14px;font-weight: bold;'">{{commentList.hint}}</div> -->
            <div v-if="listData.length != 0"
                 :style="'font-size:13px;font-weight: bold;'">({{listData.length}}条)</div>
            <div class="flex_placeholder"></div>
            <!-- <div @click="openAddComment()"
                 style="padding: 10px 15px;">
              <van-icon :size="22"
                        :color="appTheme"
                        name="edit"></van-icon>
            </div> -->
          </div>
          <template v-if="listData.length != 0">
            <div v-for="(item,index) in listData"
                 :key="item.id"
                 class="flex_box"
                 style="padding: 10px 0;"
                 @click="openAddComment(item)">
              <img @click.stop="openPersonalDataDetails({id:item.user.userId})"
                   :src="item.user.url"
                   style="width:28px;hegiht:28px;object-fit: contain;border-radius: 50%;margin-right: 6px;margin-left: 20px;" />
              <div class="flex_placeholder"
                   style="padding-right: 15px;">
                <div class="flex_box flex_align_center">
                  <div :style="'font-size:14px;color:#555'">{{item.user.name}}</div>
                  <div class="flex_placeholder"></div>
                  <div @click.stop="clickLickBtn(item,'appraise')"
                       class="flex_box flex_align_end"
                       style="padding: 5px 0;">
                    <van-icon :size="18"
                              :color="item.islike?appTheme:'#999'"
                              name="good-job"></van-icon>
                    <span :style="'font-size:12px;color:'+(item.islike?appTheme:'#999')">{{item.likeNum}}</span>
                  </div>
                </div>
                <div :style="'font-size:12px;color:#5E646D;margin: 5px 0;'">{{item.content}}</div>
                <div v-if='item.showFull'
                     :style="'font-size:12px;'"
                     class="qunw"
                     @click.stop='fullText(item)'>{{item.isFull ? '收起' : '展开'}}</div>
                <div class="flex_box flex_align_center">
                  <div :style="'font-size:12px;color:#b0b0b2'">{{item.time}}</div>
                  <div :style="'font-size:12px;color:'+appTheme+';margin-left:15px;font-weight: bold;'">回复</div>
                  <div v-if="item.user.userId == userId"
                       @click.stop="deleteItem(item,index,listData)"
                       :style="'font-size:12px;color:'+appTheme+';margin-left:15px;font-weight: bold;'">删除</div>
                </div>
                <!--子评论-->
                <template v-if="item.children.length != 0 && item.openModule != 0">
                  <div v-for="(nItem,nIndex) in item.children"
                       :key="nItem.id"
                       class="flex_box"
                       style="padding: 10px 0;"
                       @click.stop="">
                    <img @click.stop="openPersonalDataDetails({id:nItem.user.userId})"
                         :src="nItem.user.url"
                         style="width:23px;height:23px;object-fit: contain;border-radius: 50%;margin-right: 6px;" />
                    <div class="flex_placeholder">
                      <div class="flex_box flex_align_center">
                        <div :style="'font-size:14px;color:#555'">{{nItem.user.name}}</div>
                        <div class="flex_placeholder"></div>
                        <div @click.stop="clickLickBtn(nItem,'appraise')"
                             class="flex_box flex_align_end"
                             style="padding: 5px 0;">
                          <van-icon :size="18"
                                    :color="nItem.islike?appTheme:'#999'"
                                    name="good-job"></van-icon>
                          <span :style="'font-size:12px;color:'+(nItem.islike?appTheme:'#999')">{{nItem.likeNum}}</span>
                        </div>
                      </div>
                      <div :style="'font-size:12px;color:#5E646D;margin: 5px 0;'">{{nItem.content}}</div>
                      <div v-if='nItem.showFull'
                           :style="'font-size:12px;'"
                           class="qunw"
                           @click.stop='fullText(nItem)'>{{nItem.isFull ? '收起' : '展开'}}</div>
                      <div class="flex_box flex_align_center">
                        <div :style="'font-size:12px;color:#b0b0b2'">{{nItem.time}}</div>
                        <div v-if="nItem.user.userId == userId"
                             @click.stop="deleteItem(nItem,nIndex,item.children)"
                             :style="'font-size:12px;color:'+appTheme+';margin-left:15px;font-weight: bold;'">删除</div>
                      </div>
                    </div>
                  </div>
                </template>
                <div @click.stop="if(item.openModule==0){item.openModule = 1}else{item.openModule = 0};"
                     v-if="item.children.length != 0"
                     :style="'font-size:12px;color:'+appTheme+';margin-top: 7px;'">{{item.openModule==0?('展开'+item.children.length+'条回复  >'):"收起"}}</div>
                <div v-if="index != listData.length-1"
                     style="width: 100%;height: 1px;background: #eee;margin-top: 7px;"></div>
              </div>
            </div>
          </template>
        </div>
        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton">
          <van-skeleton round
                        v-for="(item) in 3"
                        :key="item"
                        title
                        :row="3"></van-skeleton>
        </div>
        <!-- <van-empty v-else-if="listData.length == 0"
                   :style="'font-size:14px'"
                   :image="icon_no_comment"
                   :description="''">
          <van-button @click.stop="openAddComment()"
                      round
                      type="info"
                      size="large"
                      :color="appTheme">{{'我来说两句'}}</van-button>
        </van-empty> -->
      </van-list>
    </van-pull-refresh>
    <div style="height:50px;"></div>

    <!--添加分类-->
    <van-overlay :show="addCategory"
                 z-index="100"
                 :lock-scroll="false">
      <div class="T-flexbox-vertical flex_align_center flex_justify_content">
        <div class="category_box T-flexbox-vertical"
             style="max-height: 70%;">
          <div class="flex_placeholder"
               style="height:1px;overflow-y: auto;-webkit-overflow-scrolling: touch;">
            <van-empty v-if="switchs.data.length <= 1"
                       :style="'font-size:14px'"
                       :image="icon_no_comment"
                       :description="'暂无分类'"></van-empty>
            <div v-else
                 v-for="(nItem,nIndex) in switchs.data"
                 :key="nItem.id">
              <div v-if="nIndex != 0"
                   class="category_item flex_box flex_align_center">

                <van-icon @click="editCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'edit'"></van-icon>
                <div @click="clickCategory(nItem,nIndex)"
                     class="flex_placeholder"
                     :style="'color:'+appTheme+';text-align: center;'">{{nItem.label}}</div>
                <van-icon @click="deleteCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'close'"></van-icon>
              </div>
            </div>
          </div>
          <div @click.stop="createCategory()"
               class="category_item flex_box flex_align_center flex_justify_content">
            <van-icon :size="21"
                      :color="appTheme"
                      :name="'plus'"></van-icon>
            <div :style="'color:'+appTheme+';text-align: center;margin:0 40px 0 10px;'">新建分类</div>
          </div>
          <div class="flex_box flex_align_center flex_justify_content">
            <van-tag @click.stop="addCategory = false;"
                     style="padding: 4px 25px;margin-top:20px;border-radius: 5px;"
                     :color="appTheme"
                     plain
                     type="primary">{{'关闭'}}</van-tag>
            <!-- <van-tag @click="clickCategory({label:'',value:''})"
                     style="padding: 4px 25px;margin-top:20px;border-radius: 5px;margin-left: 10px;"
                     :color="appTheme"
                     type="primary">{{'确认'}}</van-tag> -->
          </div>
        </div>
      </div>
    </van-overlay>
    <van-dialog v-model:show="addTypeNameShow"
                title="请输入分类名字"
                :confirmButtonColor="appTheme"
                @confirm="confirmAddTypeName"
                show-cancel-button>
      <van-field v-model="addTypeName"
                 label="分类名字"
                 placeholder="请输入分类名字" />
    </van-dialog>
    <van-popup v-model:show="commentInputShow"
               position="bottom"
               :style="{ height: '70px' }">
      <div style="height:10px"></div>
      <van-field v-model="commentValue"
                 center
                 clearable
                 autofocus
                 border
                 label=""
                 :placeholder="placeholder">
        <template #button>
          <van-button size="small"
                      type="primary"
                      @click="sendComment">发表</van-button>
        </template>
      </van-field>
    </van-popup>
    <!-- <van-share-sheet v-model:show="showShare"
                     title="立即分享给好友"
                     :options="options"
                     @select="onSelect" /> -->
    <!--不为一级页面时 适配底部条-->
    <footer :style="'padding-bottom:0;position: fixed;width:100%;bottom: 0;background:#fff;'">
      <div v-if="firstAjax"
           class="flex_box flex_align_center van-hairline--top">
        <div @click="addBookshelf()"
             :style="'color:'+(bookshelf?'#ccc':appTheme)"
             class="footer_btn click">{{bookshelf?'已加入书架':'加入书架'}}</div>
        <div @click="goBookReader(txt);"
             :style="'color:'+appTheme"
             class="footer_btn flex_box flex_align_center flex_justify_content click van-hairline--left">
          <van-loading v-if="txt.state==1"
                       :size="17"
                       style="margin-right: 5px;"
                       :color="appTheme"></van-loading>
          <a v-if="isIOS">
            <!-- :href="bookUrl + addUrl + signUrl" -->
            {{txt.state==1?("打开中("+txt.schedule+"%)"):txt.state==3?"打开失败":(txt.bookType == '2'?"听一听":"阅读")}}
          </a>
          <span v-else>
            {{txt.state==1?("打开中("+txt.schedule+"%)"):txt.state==3?"打开失败":(txt.bookType == '2'?"听一听":"阅读")}}
          </span>
        </div>
        <!-- <div @click="addwirte()"
             :style="'color:'+(appTheme)"
             class="footer_btn click  van-hairline--left">推荐</div>
        <div v-if="!isShowHead"
             @click="onClickRight"
             :style="'color:'+(appTheme)"
             class="footer_btn click  van-hairline--left">分享</div> -->
        <div v-if="isIOS"
             @click="openNote"
             :style="'color:'+(appTheme)"
             class="footer_btn click  van-hairline--left">笔记</div>
      </div>
    </footer>
    <!-- <van-dialog v-model:show="diashow"
                confirmButtonText="提交"
                confirmButtonColor="#007AFF"
                title="推荐理由"
                @confirm="confirmAddNote"
                show-cancel-button>
      <van-field v-if="diashow"
                 v-model="message"
                 style="background:#fff"
                 rows="5"
                 autofocus
                 autosize
                 label=""
                 type="textarea"
                 placeholder="请输入推荐理由"
                 show-word-limit />
    </van-dialog> -->
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { NavBar, Sticky, Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Popup, ShareSheet } from 'vant'
import md5 from 'js-md5'
import moment from 'moment'
import writeRemeber from '../../../components/writeRemeber/writeRemeber'
// import Vrouter from '@/router'
export default {
  name: 'bookDetail',
  components: {
    writeRemeber,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [ShareSheet.name]: ShareSheet,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [NavBar.name]: NavBar
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const route = Vrouter.currentRoute.value
    // const router = Vrouter
    const id = route.query.id
    const imgdefault = require('../../../assets/img/img_test1.png')
    const data = reactive({
      writefn: null,
      show: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: id,
      name: '',
      author: '',
      summary: '',
      img: { url: imgdefault },
      txt: { state: 0, schedule: -1, bookType: '3' }, // 书本链接
      entity: false,
      label: [],
      time: '',
      isSummary: true, // 是否是简介
      hasSummary: true, // 是否有更多
      detailsBtn: [
        { hint: '阅读', color: '#4CD964', value: '452', unit: '人', key: 'members' },
        { hint: '字数', color: '#FF9500', value: '10.5', unit: '', key: '' },
        { hint: '版权', color: '#007AFF', value: '', unit: '外语出版社', key: '' },
        { hint: '笔记', color: '#FF3B30', value: '358', unit: '篇', key: 'notes' }
      ],

      // commentList: {
      //   hide: false,
      //   hint: '大家这么说',
      //   data: [
      //   ]
      // },
      bookshelf: false,
      moreLength: 50, // 超出多少字 显示展开
      // userId: JSON.parse(sessionStorage.getItem('user')).id, // 我的id
      details: {},
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      showSkeleton: false,
      firstAjax: false,
      diashow: false,
      listData: [],
      dataList: [],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      switchs: { value: '', data: [{ label: '所有书籍', value: '' }] },
      addCategory: false, // 添加分类是否
      // eslint-disable-next-line no-dupe-keys
      moreLength: 50, // 超出多少字 显示展开
      optionItem: null,
      editIndex: 0,
      isEdit: false,
      addTypeNameShow: false,
      commentInputShow: false,
      commentItem: '',
      commentValue: '',
      placeholder: '说两句吧',
      addTypeName: '',
      showShare: false,
      options: [
        { name: '分享', icon: require('../../../assets/img/icon_book_activity.png') }
        // { name: '微信', icon: 'wechat' },
        // { name: '微博', icon: 'weibo' },
        // { name: '复制链接', icon: 'link' },
        // { name: '分享海报', icon: 'poster' },
        // { name: '二维码', icon: 'qrcode' }
      ],
      message: '',
      bookUrl: '',
      addUrl: '',
      signUrl: '',
      isIOS: false,
      isGoole: false
    })
    // document.title=''
    onMounted(() => {
      console.log(navigator.userAgent)
      if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        // ios
        console.log(' 我是ios')
        try {
          console.log(' 我是ios跳转')
          data.isIOS = true
        } catch (error) {
          console.log('WKWebView post message')
        }
      } else if (/(AppleWebKit|Safari)/i.test(navigator.userAgent)) {
        try {
          console.log(' 我是ios跳转')
          data.isGoole = true
        } catch (error) {
          console.log('AppleWebKit post message')
        }
      } else if (/(Android)/i.test(navigator.userAgent)) {
        // android
        console.log('我是android')
        data.isIOS = false
        data.isGoole = false
      }
      addCounter()
      getBookInfo()
    })
    // 增加书院统计
    const addCounter = async () => {
      var datas = {
        dataId: data.id
      }
      await $api.bookAcademy.addCounter(datas)
    }
    const getReadInfo = async () => {
      var { data: info } = await $api.bookAcademy.readbookinfo({
        bookId: data.id
      })
      if (info) {
        data.headerName = info.bookName// 书名
        data.codingType = info.codingType || 'UTF-8'
        data.progress = info.readPercent || 1// 当前 进度
        data.readMinutes = info.readMinutes || 1// 当前分钟数
        if (data.txt.bookType === '3') {
          // data.bookUrl = `http://view.xdocin.com/xdoc?_xdoc=${data.txt.url}`
        } else {
          data.bookUrl = 'https://s.zhangyue.com/' + (data.txt.bookType === '1' ? 'read?bid' : 'audio/ting?bookId') + '=' + data.id + '&' + (data.txt.bookType === '1' ? 'cid' : 'chapterId') + '=' + data.progress + '&'
          data.addUrl = 'appId=' + '41ec83c2' + '&rentId=' + '103053' + '&timestamp=' + (Date.parse(new Date()).toString().substr(0, 10)) + '&userId=ShanDongQingDaoShiZXappTest'
          data.signUrl = '&sign=' + zyBookSign(data.addUrl)
          const url = data.bookUrl + data.addUrl + data.signUrl
          await $api.general.general(url, '')
          const urlParam = url.split('?')[1].split('&')
          var myParam = {}
          urlParam.forEach(element => {
            myParam[element.split('=')[0]] = element.split('=')[1]
          })
        }
      }
    }
    // 计算签名
    const zyBookSign = (str) => {
      var encrypt = md5(str).substr(6, 18)
      return md5(encrypt).substr(10, 16)
    }
    // 增加书院统计
    const getBookInfo = async () => {
      var datas = {
        id: data.id
      }
      var { data: info } = await $api.bookAcademy.getBookInfo(datas)
      if (info) {
        data.img.url = info.coverImgUrl || ''
        data.id = info.id || ''
        data.name = info.bookName || ''
        data.author = info.authorName || ''
        data.label = []
        if (info.bookTypeFirstName) { data.label.push(info.bookTypeFirstName) }
        if (info.bookTypeSecondName) { data.label.push(info.bookTypeSecondName) }
        data.summary = info.bookDescription || ''
        data.hasSummary = data.summary.length > 45
        data.txt.name = info.bookName || ''
        data.txt.url = info.bookContentUrl || ''
        data.txt.bookType = info.bookType || '3'
        data.txt.isAvailable = info.isAvailable
        // that.annexCheck(that.txt);//附件检测 拿到附件缓存 信息
        data.detailsBtn[0].value = info.readCount
        data.detailsBtn[1].value = info.words || '暂无'
        if (data.txt.bookType === 2) {
          data.detailsBtn[1].hint = '时长'
          data.detailsBtn[1].value = '暂无'
        }
        data.detailsBtn[2].unit = info.versionOwner
        data.detailsBtn[3].value = info.noteAmount
        getReadInfo()
      }
      var { data: book } = await $api.bookAcademy.existBook({
        bookId: data.id
      })
      data.bookshelf = book || false
      onRefresh()
    }
    // 推荐理由弹框
    const addwirte = () => {
      data.diashow = true
    }
    const getCommntList = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        businessId: data.id
      }
      var { data: commentList } = await $api.bookAcademy.getCommentList(datas)
      var dataListNew = []
      commentList.forEach((_eItem, index) => {
        var item = { user: { url: _eItem.userHeadImg, name: _eItem.userName, userId: _eItem.commentUser } }
        item.noteId = _eItem.id
        item.islike = _eItem.clicked
        item.likeNum = Number(_eItem.clicks)
        item.time = moment(_eItem.createDate).format('YYYY-M-D HH:mm')
        var content = _eItem.content// 评论内容
        item.showFull = content.length > data.moreLength
        item.isFull = false
        item.fullContent = content
        item.content = content
        if (item.showFull) {
          item.content = content.substring(0, data.moreLength) + '...'
        }
        item.openModule = 0// 打开程度 0不打开 1打开3条 2打开所有
        item.children = []
        var children = _eItem.children || []
        children.reverse()
        children.forEach(function (_nItem, _nIndex, _nArr) { // item index 原数组对象
          var nItem = { user: { url: _nItem.userHeadImg, name: _nItem.userName, userId: _nItem.commentUser } }
          nItem.noteId = _nItem.id
          nItem.islike = _nItem.clicked
          nItem.likeNum = Number(_nItem.clicks)
          nItem.time = moment(_nItem.createDate).format('YYYY-M-D HH:mm')
          var content = _nItem.content// 评论内容
          nItem.showFull = content.length > data.moreLength
          nItem.isFull = false
          nItem.fullContent = content
          nItem.content = content
          if (nItem.showFull) {
            nItem.content = content.substring(0, data.moreLength) + '...'
          }
          item.children.push(nItem)
        })
        dataListNew.push(item)
      })
      data.listData = data.listData.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      data.showSkeleton = false
      data.firstAjax = true
      // 数据全部加载完成
      if (commentList.length < data.pageSize) {
        data.finished = true
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getCommntList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getCommntList()
    }
    const addBookshelf = async () => {
      if (!data.bookshelf) {
        data.addCategory = true
        getTypeList()// 获取所有分类
      } else {
        var { data: res } = await $api.bookAcademy.delByBookIds({
          bookIds: data.id
        })
        if (res) {
          Toast('移除成功！')
          data.bookshelf = false
        }
      }
    }
    // 添加到分类里
    const clickCategory = async (_item) => {
      var clickName = data.name
      var clickId = data.id
      var datas = {
        bookIds: clickId, typeId: _item.value, isSingle: true
      }
      var res = []
      res = await $api.bookAcademy.addBookToType(datas)
      var { data: list } = res
      if (list) {
        data.addCategory = false
        data.bookshelf = true
        Toast('书本【' + clickName + '】添加到' + (!_item.label ? '书架' : ('分类【' + _item.label + '】')) + (list ? '成功' : '失败'))
      }
    }
    const confirmAddTypeName = async () => {
      var typeName = data.addTypeName
      if (!(typeName).replace(/(^\s*)|(\s*$)/g, '')) {
        return Toast('请输入分类名字')
      }
      var datas = {}
      if (data.isEdit) {
        datas = {
          id: typeName, typeName: typeName
        }
        var { data: editData } = await $api.bookAcademy.editType(datas)
        if (editData) {
          Toast('修改分类' + (editData ? '成功' : '失败'))
          data.switchs.data[data.editIndex].label = typeName
          data.addTypeName = ''
        }
      } else {
        datas = {
          typeName: typeName
        }
        var { data: addData } = await $api.bookAcademy.addType(datas)
        if (addData) {
          Toast('新增分类' + (addData ? '成功' : '失败'))
          data.addTypeName = ''
          data.switchs.data.push({ label: typeName, value: addData })
        }
      }
    }
    const confirmAddNote = async () => {
      var message = data.message
      var clickName = data.name
      var clickId = data.id
      // if (!(message).replace(/(^\s*)|(\s*$)/g, '')) {
      //   return Toast('请输入推荐理由')
      // }
      var datas = {}
      datas = {
        noteContent: message, bookId: clickId, screenshot: '', isPrivacy: 0
      }
      var { data: addData } = await $api.bookAcademy.addCommissar(datas)
      if (addData) {
        Toast('新增分类' + (addData ? '成功' : '失败'))
        Toast('书本【' + clickName + '】推荐' + (addData ? '成功' : '失败'))
        data.message = ''
      }
    }
    // 创建新分类
    const createCategory = () => {
      data.addTypeNameShow = true
      data.isEdit = false
    }
    // 修改分类
    const editCategory = (_item, _index) => {
      data.addTypeNameShow = true
      data.addTypeName = _item.label
      data.editIndex = _index
      data.isEdit = true
    }
    // 删除分类
    const deleteCategory = async (_item, _index) => {
      var datas = {
        id: _item.value
      }
      var { data: list } = await $api.bookAcademy.delType(datas)
      if (list === 1) {
        Toast('删除分类' + (list === 1 ? '成功' : '失败'))
        $general.delItemForKey(_index, data.switchs.data)
        if (_item.value === data.switchs.value) { // 当前 正处于分类  回到所有书籍
          data.switchs.value = data.switchs.data[0].value
          onRefresh()
        }
      }
    }
    // 获取所有分类
    const getTypeList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getALlTypeList(datas)
      data.switchs.data = [{ label: '所有书籍', value: '' }]
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        data.switchs.data.push({ label: _eItem.typeName, value: _eItem.id })
      })
    }
    // 点赞 取消点赞
    const clickLickBtn = async (_item, _type) => {
      _item.islike = !_item.islike
      var datas = {}
      if (_item.islike) {
        _item.likeNum++
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.addClick(datas, _type)
      } else {
        _item.likeNum--
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.cansalClick(datas, _type)
      }
    }
    const goBookReader = (txt) => {
      console.log('txt===>', txt)
      console.log('data.isIOS===>>', data.isIOS)
      if (txt.url) {
        // var isPDF = txt.url.endsWith('.pdf')
        // if (isPDF) {
        //   if (window.location.origin === 'http://59.224.134.152:81') {
        //     window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + txt.url)
        //   } else {
        //     window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + txt.url)
        //   }
        // } else {
        var param = {
          url: txt.url,
          name: txt.name + '.pdf'
        }
        router.push({ name: 'superFile', query: param })
        // }
      } else {
        if (!data.isIOS) {
          if (txt.bookType === '2') {
            router.push({ name: 'bookReader', query: { id: data.id, txt: JSON.stringify(txt), imgs: data.img.url } })
          } else {
            router.push({ name: 'bookReader', query: { id: data.id, txt: JSON.stringify(txt), imgs: data.img.url } })
          }
          // }
        } else {
          if (txt.bookType === '2') {
            router.push({ name: 'bookReader', query: { id: data.id, txt: JSON.stringify(txt), imgs: data.img.url } })
          } else {
            router.push({ name: 'bookReader', query: { id: data.id, txt: JSON.stringify(txt), imgs: data.img.url } })
          }
        }
      }
    }
    const openAddComment = (_item) => {
      if (_item) {
        data.placeholder = '回复' + _item.user.name
        data.commentItem = _item
      } else {
        data.commentItem = ''
        data.placeholder = '说两句吧'
      }
      data.commentInputShow = true
    }
    const sendComment = async () => {
      var datas = {
        businessId: data.id, content: data.commentValue, parentId: data.commentItem ? data.commentItem.noteId : ''
      }
      const { data: addData } = await $api.bookAcademy.addComment(datas)
      if (addData) {
        Toast('操作成功')
        data.commentInputShow = false
        data.commentValue = ''
        data.commentItem = ''
        data.placeholder = ''
        onRefresh()
      }
    }
    // 点击删除评论
    const deleteItem = async (_item, _index, _list) => {
      var datas = {
        id: _item.noteId,
        businessId: data.id
      }
      const { errcode } = await $api.bookAcademy.delComment(datas)
      if (errcode) {
        Toast('删除成功')
        _list.splice(_index, 1)// 删除
      }
    }
    const detailsBtnClick = (_item) => {
      switch (_item.key) {
        case 'notes':
          router.push({ name: 'notesDetails', query: { id: data.id } })// 跳转笔记详情
          break
        case 'members':// 阅读人数
          router.push({ name: 'bookReadUserList', query: { id: data.id } })// 跳转阅读详情
          break
      }
    }
    const onClickLeft = () => history.back()
    const onClickRight = () => {
      data.showShare = true
    }
    const onSelect = (option) => {
      switch (option.name) {
        case '分享':
          var myParam = {}
          var bookInfo = { id: data.id, url: data.img.url, name: data.name, author: data.author, bookType: data.txt.bookType }
          myParam.sendInfo = JSON.stringify({ type: 'sendRichContentMessage', info: bookInfo, name: '[书籍]' })
          router.push({ name: 'forward', query: myParam })// 转发消息
          break
      }
      data.showShare = false
    }
    // 点击笔记
    const openNote = () => {
      data.show = true
      data.writefn.takeScreenshot(data.img.url, data.id)
    }
    const openPersonalDataDetails = (_item) => {
      router.push({ name: 'personData', query: { id: _item.id } })
    }
    return { ...toRefs(data), onRefresh, onLoad, onClickLeft, onClickRight, openNote, onSelect, goBookReader, addBookshelf, sendComment, deleteItem, clickCategory, confirmAddTypeName, confirmAddNote, createCategory, editCategory, deleteCategory, clickLickBtn, openAddComment, detailsBtnClick, addwirte, openPersonalDataDetails }
  }
}
</script>
<style lang="less" scoped>
@import "./bookDetail.less";
</style>
