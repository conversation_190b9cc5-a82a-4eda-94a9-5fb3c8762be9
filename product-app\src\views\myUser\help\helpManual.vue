<template>
  <div class="helpManual">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <van-tabs v-model:active="switchs.value"
              @change="onRefresh"
              :color="appTheme"
              :title-active-color="appTheme"
              sticky>
      <van-tab v-for="(item,index) in switchs.data"
               :key="index"
               :title="item.label"
               :name="item.value">
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索内容" />
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <template v-if="switchs.value == '1'">
            <div v-for="(item,index) in listData"
                 :key="index"
                 class="van-hairline--bottom">
              <van-cell clickable
                        class="vue_newslist_item"
                        @click="openDetails(item)">
                <div class="flex_box flex_align_center">
                  <div class="flex_placeholder"
                       :style="$general.loadConfiguration(-1)+'color: #333333;'"
                       v-html="item.name"></div>
                  <van-icon :size="((appFontSize-2)*0.01)+'rem'"
                            color="#8A8A8A"
                            name="arrow"></van-icon>
                </div>
              </van-cell>
            </div>
          </template>
          <template v-else-if="switchs.value == '2'">
            <div style="margin-top: -0.14rem;"
                 class="flex_box T-flex-flow-row-wrap">
              <div v-for="(item,index) in listData"
                   :key="index"
                   class="specialsubject_item_video"
                   @click="openDetails(item)">
                <div class="specialsubject_item_video_img">
                  <div class="specialsubject_item_video_play flex_box flex_align_center flex_justify_content"><van-icon :size="((appFontSize+20)*0.01)+'rem'"
                              color="#FFF"
                              name="play-circle"></van-icon></div>
                  <img style="width: 100%;height: 100%;object-fit: cover;"
                       :src="item.url" />
                </div>
                <div class="text_two"
                     :style="$general.loadConfiguration(-2)+'color: #333;margin:0.08rem;line-height: 1.42;min-height:0.34rem;'">{{item.name}}</div>
              </div>
            </div>
          </template>
        </ul>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>

import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
export default {
  name: 'helpManual',
  components: {
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      switchs: { value: '1', data: [{ label: '操作手册', value: '1' }] },
      appTheme: $appTheme,
      keyword: '',
      listData: []
    })
    onMounted(() => {
      getList()
    })
    const search = async () => {
      data.pageNo = 1
      data.listData = []
      getList()
    }
    // 获取列表
    const getList = async () => {
      if (data.switchs.value === '2') {
        var datas2 = {
          pageNo: 1,
          pageSize: 999,
          keyword: data.keyword
        }
        var res2 = await $api.general.tutorialvideolList(datas2)
        var { data: list2 } = res2
        data.listData = data.listData.concat(list2)
      } else {
        var datas1 = {
          pageNo: 1,
          pageSize: 999,
          keyword: data.keyword
        }
        var res1 = await $api.general.operationmanualList(datas1)
        var { data: list1 } = res1
        data.listData = data.listData.concat(list1)
      }
    }
    // 进详情
    const openDetails = async (row) => {
      router.push({ name: 'helpManualDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), $general, openDetails, search }
  }
}
</script>
<style lang="less">
.helpManual {
  width: 100%;
}
</style>
