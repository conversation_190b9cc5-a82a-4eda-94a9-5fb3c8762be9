<template>
  <div class="socialsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <li class="vue_newslist_li flex_box">
        <div class="flex_placeholder vue_newslist_warp T-flexbox-vertical">
          <div class="vue_newslist_title"
               :style="$general.loadConfiguration(1)">{{details.title}}</div>
          <div class="T-flex-item"></div>
          <div class="">
            <div class="vue_newslist_more text_one2"
                 :style="$general.loadConfiguration(-3)">{{details.org}}</div>
            <div class="vue_newslist_more text_one2 vue_newslist_more_right"
                 :style="$general.loadConfiguration(-3)">{{details.dataTime}}</div>
            <!-- <div v-if="details.type"
                 class="vue_newslist_more_right"
                 :style="$general.loadConfiguration(-3)">
              <van-tag plain
                       :color="'#8C96A2'">{{details.type}}</van-tag>
              <van-tag v-if="details.statusName"
                       style="margin-left:20px"
                       :color="getColor(details.statusName)"
                       :text-color="getTextColor(details.statusName)">{{details.statusName}}</van-tag>
            </div> -->
            <div style="clear: both;"></div>
          </div>
        </div>
      </li>
      <div class="n_details_content"
           @click="setImgBigger"
           :style="$general.loadConfiguration()"
           v-html="details.content"></div>
      <!--展示附件-->
      <template v-if="attachInfo.data.length != 0">
        <div class="add_warp attach_warp flex_box">
          <div class="attach_name">{{attachInfo.name}}:</div>
          <div class="">
            <van-swipe-cell v-for="(nItem,nIndex) in attachInfo.data"
                            :key="nIndex"
                            class="flex_box">
              <van-cell :border="false"
                        :class="nItem.state==2?'cache':'cache'"
                        @click="annexClick(nItem)"
                        :title="nItem.name"
                        :title-style="'font-size:14px;'"></van-cell>
            </van-swipe-cell>
          </div>
        </div>
      </template>
    </van-pull-refresh>
    <div v-if="type === 'Processed' || type === 'Pending'">
      <div style="height: 120px;"></div>
      <footer class="footer"
              :style="$general.loadConfiguration() + 'padding-bottom:' + (((pageType != 'home'?safeAreaBottom:0)+10)*0.01)+'rem'">
        <div class="flex_box">
          <div :style="$general.loadConfiguration(-1)+'width:100%;padding: 0 12px;'">
            <div class="footer_btn flex_box flex_align_center flex_justify_content"
                 @click="pendingSocial()">
              <van-icon class-prefix="iconfont"
                        class="icon iconfont icon-dingdanchuli"
                        :color="appTheme"
                        :size="(($general.appFontSize+2)*1)+'px'"></van-icon>
              <div :style="$general.loadConfiguration(-1)+'color: #333333;font-weight: 600;margin-left:13px;'">处理方式</div>
            </div>
          </div>
          <div :style="$general.loadConfiguration(-1)+'width:100%;padding: 0 12px;'">
            <div class="footer_btn flex_box flex_align_center flex_justify_content"
                 @click="editSocial()">
              <van-icon class-prefix="iconfont"
                        class="icon iconfont icon-bianjiedit26"
                        :color="appTheme"
                        :size="(($general.appFontSize+4)*2)+'px'"></van-icon>
              <div :style="$general.loadConfiguration(-1)+'color: #333333;font-weight: 600;margin-left:13px;'">编辑详情</div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview, Tag, Icon } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'socialsDetails',
  components: {
    [Icon.name]: Icon,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Tag.name]: Tag,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      currentProcessStatus: route.query.currentProcessStatus || '',
      details: {},
      process_no: require('../../assets/img/process_no.png'),
      process_on: require('../../assets/img/process_on.png'),
      process: require('../../assets/img/process.png'),
      backgroundcolor: ['#FFE7DC', '#DCEBFF', '#EBF0F6'],
      textcolor: ['#FE7530', '#3088FE', '#9CA6B3'],
      refreshing: false,
      show: false,
      type: route.query.type,
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getInfo()
    })
    const onRefresh = () => {
      getInfo()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    // 列表请求
    const getInfo = async () => {
      const res = await $api.social.getSocialDetails(data.id)
      var { data: details } = res
      details.title = details.titile || ''// 标题
      details.org = !data.type ? details.firstReflecterName : (details.reportUnit ? details.reportUnit : details.firstReflecterName) // 部门
      details.dataTime = dayjs(details.reportTime).format('YYYY-MM-DD HH:mm') || ''// 时间
      details.type = details.firstCategory || ''// 标签
      details.statusName = data.currentProcessStatus || ''// 状态
      details.content = $general.dealWithCon(details.content || '')// 内容
      data.details = details
      data.attachInfo.data = []
      var attachmentList = details.attachments || []// 附件
      if (attachmentList.length !== 0) {
        for (var k = 0; k < attachmentList.length; k++) {
          var nItemName = attachmentList[k].originalName
          var nItemPath = attachmentList[k].downloadUrl
          data.attachInfo.data.push({
            url: nItemPath,
            state: 0,
            schedule: -1,
            name: nItemName
          })
        }
      }
      data.loading = false
      data.refreshing = false
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.n_details_content img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview(img, nowIndex)
      }
    }
    // 编辑社情民意
    const editSocial = (row) => {
      router.push({ name: 'socialsNews', query: { id: data.id, type: 'edit', showDrafts: false } })
    }
    // 处理社情民意
    const pendingSocial = () => {
      router.push({ name: 'socialsProcess', query: { id: data.id, type: 1 } })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, setImgBigger, annexClick, $general, dayjs, editSocial, pendingSocial }
  }
}
</script>
<style lang="less">
.socialsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  // background: #f8f8f8;
  .vue_newslist_li:active {
    background: rgba(0, 0, 0, 0);
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
  }
  .attach_name {
    font-size: 14px;
    width: 40px;
    font-weight: 400;
    color: #999999;
    opacity: 1;
    margin-top: 10px;
  }
  .add_warp {
    padding: 10px 10px;
    background: #fff;
  }
  .vue_newslist_li {
    padding: 10px 12px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    border-bottom: 1px solid #f4f5ee;
  }
  .vue_newslist_more {
    color: #888;
    float: left;
    padding: 20px 0;
    line-height: 1.5;
  }
  .vue_newslist_more_right {
    float: right;
    padding: 20px 0;
    line-height: 1.5;
  }
  .vue_newslist_title {
    margin-bottom: 5px;
  }
  .vue_newslist_li:after {
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 200%;
    height: 1px;
    background-color: #dedede;
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    box-sizing: border-box;
  }
  .vue_newslist_more + .vue_newslist_more {
    margin-left: 10px;
  }
  .footer {
    padding: 10px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    .van-button {
      padding: 10px 21px;
      border-color: #e2e2e2;
      font-weight: 600;
    }
    .footer_btn {
      width: 100%;
      padding: 8px 0;
      box-shadow: 0px 0px 5px 0px #e2e2e2;
      background: #ffffff;
      border-radius: 19px;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
    }
  }
}
</style>
