<template>
  <div class="thinkTanksDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>

    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="btnRight_box flex_box">
        <div v-if="switchs.value == '1'"
             @click="selYear()"
             class="img_btn flex_box flex_align_center flex_justify_content">
          <div class="flex_box flex_align_center"
               :style="'padding: 4px 0;'">
            <span :style="$general.loadConfiguration(-3)"
                  style="color:inherit;margin-right:4px;"
                  v-html="year+'年度'"></span>
            <van-icon :size="((appFontSize-8)*0.01)+'rem'"
                      name="arrow-down"></van-icon>
          </div>
        </div>
      </div>
      <!--顶上的个人信息-->
      <ul style="padding: 12px 12px 0;">
        <li class="expert_item flex_box click">
          <img class="expert_img"
               :style="$general.loadConfigurationSize([43,52])"
               :src="img.url" />
          <div class="flex_placeholder">
            <div class="flex_box flex_align_center"
                 style="margin-bottom:12px;">
              <div class="expert_name text_one2"
                   :style="$general.loadConfiguration()"
                   v-html="name"></div>
              <div class="expert_sex"
                   :style="$general.loadConfiguration(-4)"
                   v-html="sex"></div>
              <div class="flex_placeholder"></div>
            </div>
            <div class="flex_placeholder expert_partisan"
                 :style="$general.loadConfiguration(-3)"
                 v-html="partisan"></div>
            <div class="expert_position"
                 :style="$general.loadConfiguration(-2)"
                 v-html="position"></div>
          </div>
        </li>
      </ul>
      <div style="padding: 14px 14px 0;">
        <div class="expert_main">
          <div class="expert_main_header flex_box flex_align_center">
            <div v-for="(item,index) in switchs.data"
                 :key="index"
                 @click="switchTabs(item)"
                 :style="$general.loadConfiguration(switchs.value==item.value?0:-3)+'border-color:'+appTheme"
                 class="expert_main_header_item"
                 :class="switchs.value==item.value?'expert_main_header_active':''">{{item.label}}</div>
          </div>
          <div v-if="switchs.value == '0'"
               style="padding: 15.2px;">
            <div class="n_details_content"
                 :style="$general.loadConfiguration(-1)"
                 v-html="contribute"></div>
          </div>
          <template v-else-if="switchs.value == '1'">
            <ul style="background: #FFF;border-radius: 8px;">
              <li v-for="(item,index) in listData"
                  :key="index"
                  @click="openDetails(item)"
                  :class="index!=0?' van-hairline--top':''"
                  class="flex_box flex_align_center click"
                  style="padding: 14px 24px 14px 8px;">
                <div style="min-width: 32px;margin-right:10px;"
                     class="flex_box flex_justify_content">
                  <div :style="$general.loadConfiguration(-3)+'font-weight: bold;color:'+(index<3?['#FF5E5E','#FFC05E','#2FC994'][index]:'#ccc')"
                       v-if="index<9">{{ 0+(index+1) }}</div>
                  <div :style="$general.loadConfiguration(-3)+'font-weight: bold;color:'+(index<3?['#FF5E5E','#FFC05E','#2FC994'][index]:'#ccc')"
                       v-else>
                    {{index+1}}
                  </div>
                </div>
                <div class="text_one2 flex_placeholder"
                     :style="$general.loadConfiguration(-1)+'color:#333;font-weight: 600;margin-right:24px;'">{{item.name}}</div>
                <div :style="$general.loadConfiguration(-2)+'color:#333;'">{{item.values.length}}</div>
              </li>
            </ul>
          </template>
        </div>
      </div>
    </van-pull-refresh>
    <van-action-sheet v-model:show="showAction"
                      :actions="actionsData"
                      :description="description"
                      cancel-text="取消"
                      @select="onSelect"
                      close-on-click-action />
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview, ActionSheet } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'thinkTanksDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [ActionSheet.name]: ActionSheet
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      correspond: {
        allMeet: '全体会议',
        negotiationMeet: '双月协商座谈会',
        negotiationAct: '协商监督，议政建言',
        researchAct: '调研活动',
        fiveAct: '五进五送',
        deleAct: '界别活动',
        specialAct: '专题协商议政',
        legislationAct: '立法协商',
        inspectAct: '视察活动',
        otherAct: '其他活动',
        threeAct: '三双活动',
        contributionAct: '特别贡献',
        social: '提交社情民意',
        goodSocial: '优秀社情民意'
      },
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false,
      img: { url: '' },
      name: '',
      sex: '男',
      partisan: '中共党派',
      position: '内蒙古君正能源化工股份有限公司董事局主席山西安泰控股集团总裁',
      contribute: '12',
      switchs: { value: '0', data: [{ label: '主要介绍', value: '0' }, { label: '履职情况', value: '1' }] },
      year: '',
      showAction: false,
      actionsData: [],
      description: '选择年份',
      listData: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      var nowYear = new Date().getFullYear()
      for (let i = 2018; i <= nowYear; i++) {
        data.actionsData.push({ name: i })
      }
      data.year = nowYear
      onRefresh()
    })
    const onRefresh = () => {
      getInfo()
    }
    // 获取详情
    const getInfo = async () => {
      const res = await $api.thinkTanks.thinkTanksDetails(data.id)
      var { data: info } = res
      data.id = info.id || ''
      data.img.url = info.fullImgUrl || ''
      data.name = info.userName || ''
      data.sex = info.sex === 1 ? '男' : info.sex === 2 ? '女' : ''
      data.partisan = info.partisan || ''
      data.position = info.position || ''
      data.contribute = info.contribute || ''
      getWisemanUserDutyDetail()
    }
    // 获取履职分类
    const getWisemanUserDutyDetail = async () => {
      var postParam = {
        userId: data.id,
        year: data.year
      }
      const res = await $api.thinkTanks.findDutyDetail(postParam)
      data.refreshing = false
      var code = res ? res.errcode : ''
      var info = res ? res.data : {} || {}
      data.listData = []
      if (code === 200) {
        for (var _eItem in data.correspond) {
          data.listData.push({ type: _eItem, name: data.correspond[_eItem], values: info[_eItem] || [] })
        }
        data.listData.sort(function (a, b) {
          return b.values.length - a.values.length
        })
      }
    }
    // 点击履职分类进列表
    const openDetails = async (row) => {
      console.log('row==>', row)
      router.push({ name: 'performanceDutiesList', query: { id: data.id, title: row.name, year: data.year, correspond: row.type } })
    }
    // 点击年份弹窗
    const selYear = async () => {
      data.showAction = true
    }
    // 选择年份
    const onSelect = async (_item) => {
      data.year = _item.name
      getWisemanUserDutyDetail()
    }
    // tab切换
    const switchTabs = async (_item) => {
      data.switchs.value = _item.value
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, $general, switchTabs, selYear, onSelect, openDetails }
  }
}
</script>
<style lang="less">
.thinkTanksDetails {
  width: 100%;
  min-height: 100vh;
  background: #f4f4f4;
  .btnRight_box {
    position: relative;
    left: 265px;
    height: 35px;
  }

  .img_btn {
    min-width: 29px;
    padding: 0 10px;
  }
  .img_btn:active {
    background: rgba(0, 0, 0, 0.05);
  }
  .btnLeft_box .img_btn:first-child {
    margin-left: 4px;
  }

  .expert_item {
    padding: 16px 15px;
    background: #ffffff;
    border-radius: 8px;
  }

  .expert_img {
    object-fit: cover;
    border-radius: 4px;
    margin-right: 12px;
    margin-top: 4px;
  }

  .expert_name {
    font-weight: 600;
    color: #333333;
  }

  .expert_sex {
    font-weight: 500;
    color: #b2b9d9;
    margin-left: 10px;
    line-height: 1.2;
  }

  .expert_position {
    font-weight: 500;
    color: #b2b9d9;
    line-height: 1.3;
  }

  .expert_partisan {
    font-weight: 500;
    color: #6f78a2;
    margin-bottom: 8px;
  }
  .expert_main {
    background: #ffffff;
    border-radius: 8px;
  }
  .expert_main_header_item {
    padding: 15px 16px;
    font-weight: 500;
    color: #666666;
  }
  .expert_main_header_active {
    font-weight: 600;
    color: #333333;
    position: relative;
  }
  .expert_main_header_active:after {
    position: absolute;
    content: "";
    width: 14%;
    height: 2.4px;
    border-radius: 0 0 50% 50%/0 0 100% 100%;
    left: 36%;
    bottom: 8px;
    border-width: 4px;
    border-style: solid;
    border-color: inherit;
    border-top: none;
  }
  .n_details_content {
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    padding-top: 0;
    line-height: 28px;
  }
}
</style>
