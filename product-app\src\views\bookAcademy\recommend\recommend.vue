<template>
  <div class="recommend">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <van-search v-model="value"
                  show-action
                  shape="round"
                  placeholder="搜索"
                  readonly="true"
                  @click-input="onSearch"
                  @search="onSearch">
        <template #action>
          <div @click="onClickButton">书库</div>
        </template>
      </van-search>
    </van-sticky>
    <!-- <div style="height:70px;"></div> -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="listBox">
          <div v-for="item in dataList"
               :key="item.id">
            <!--一本书样式-->
            <li v-if="item.type=='typeOne'"
                class="item_body_warp"
                @click="openBookDetails(item)">
              <div class="item_body typeOne T-flexbox-vertical flex_align_center">
                <div v-if="item.title"
                     class="item_title"
                     v-html="item.title"></div>
                <div v-if="item.smallTitle"
                     class="item_smallTitle"
                     v-html="item.smallTitle"></div>
                <div class="item_img">
                  <div v-if="item.txt.state == 1"
                       class="item_download flex_box flex_align_center flex_justify_content"
                       :style="'font-size: 14px;'">
                    <van-circle fill="#FFF"
                                :size="56"
                                v-model="item.txt.schedule"
                                :rate="item.txt.schedule"
                                stroke-width="100"
                                :text="item.txt.schedule+'%'"></van-circle>
                  </div>
                  <img v-if="item.txt.bookType == '2'"
                       class="item_Sound"
                       :src="icon_hasSound" />
                  <img v-if="item.url"
                       :src="item.url"
                       style="object-fit: cover;border-radius: 2px;" />
                  <div v-if="item.entity"
                       class="item_entity"
                       v-html="'实体书'"></div>
                </div>
                <div v-if="item.name"
                     class="item_name"
                     v-html="item.name"></div>
                <div v-if="item.hint"
                     class="item_hint"
                     v-html="item.hint"></div>
                <div v-if="item.btn.show"
                     class="flex_box flex_align_center flex_justify_content">
                  <div class="item_btn_box">
                    <van-button style="min-height: 40px;"
                                :plain="item.btn.plain"
                                @click.stop="listBtnClick(item)"
                                round
                                type="info"
                                size="large"
                                :color="appTheme">{{item.btn.text}}</van-button>
                  </div>
                </div>
              </div>
            </li>
            <!--六本书样式-->
            <li v-else-if="item.type=='typeSex'"
                class="item_body_warp">
              <div class="item_body">
                <div v-if="item.title"
                     class="item_title"
                     v-html="item.title"></div>
                <van-empty v-if="!item.list || item.list.length == 0"
                           :style="'font-size:14px'"
                           :image="pageNot.url"
                           :description="item.notText"></van-empty>
                <div v-else
                     class="itemSex_box flex_box T-flex-flow-row-wrap">
                  <div @click="openBookDetails(nItem)"
                       v-for="nItem in item.list"
                       :key="nItem.id"
                       class="itemSex_item">
                    <div :style="'width:93px;height:119px;position: relative;'">
                      <img v-if="nItem.txt.bookType == '2'"
                           class="item_Sound"
                           :src="icon_hasSound" />
                      <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                           :src="nItem.img.url" />
                    </div>
                    <div v-if="nItem.name"
                         class="itemSex_name text_two"
                         v-html="nItem.name"></div>
                  </div>
                </div>
                <div v-if="item.btn.show"
                     class="flex_box flex_align_center flex_justify_content">
                  <div class="item_btn_box">
                    <van-button @click.stop="listBtnClick(item)"
                                round
                                type="info"
                                size="large"
                                :color="appTheme">{{item.btn.text}}</van-button>
                  </div>
                </div>
              </div>
            </li>
            <!--三本书样式-->
            <li v-else-if="item.type=='typeThree'"
                class="item_body_warp">
              <div class="item_body">
                <div v-if="item.title"
                     class="item_title"
                     v-html="item.title"></div>
                <van-empty v-if="!item.list || item.list.length == 0"
                           :style="'font-size:14px'"
                           :image="icon_no_data"
                           :description="item.notText"></van-empty>
                <template v-else>
                  <van-cell v-for="nItem in item.list"
                            :key="nItem.id"
                            @click="openBookDetails(nItem)"
                            style="padding:0;">
                    <div class="itemThree_item flex_box">
                      <div :style="'width:93px;height:117px;position: relative;'">
                        <img v-if="nItem.txt.bookType == '2'"
                             class="item_Sound"
                             :src="icon_hasSound" />
                        <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                             :src="nItem.img.url" />
                      </div>
                      <div class="flex_placeholder"
                           style="padding-top: 15px;padding-left: 10px;width:100%;">
                        <div v-if="nItem.name"
                             class="itemThree_name text_one2"
                             v-html="nItem.name"></div>
                        <div v-if="nItem.author"
                             class="itemThree_author text_one2"
                             :style="'font-size: 11px;color:'+appTheme"
                             v-html="nItem.author"></div>
                        <div v-if="nItem.summary"
                             class="itemThree_summary text_two"
                             v-html="nItem.summary"></div>
                      </div>
                    </div>
                  </van-cell>
                </template>
                <div v-if="item.btn.show"
                     class="flex_box flex_align_center flex_justify_content">
                  <div class="item_btn_box">
                    <van-button @click.stop="listBtnClick(item)"
                                round
                                type="info"
                                size="large"
                                :color="appTheme">{{item.btn.text}}</van-button>
                  </div>
                </div>
              </div>
            </li>
            <!--笔记样式-->
            <li v-else-if="item.type=='typeNotes'"
                class="item_body_warp">
              <div class="item_body">
                <div style="position: absolute;right: 0.25rem;top:0;">
                  <van-icon :size="39"
                            :color="appTheme"
                            name="bookmark"></van-icon>
                </div>
                <div v-if="item.title"
                     class="item_title"
                     v-html="item.title"></div>
                <van-empty v-if="!item.list || item.list.length == 0"
                           :style="'font-size:14px'"
                           :image="icon_no_data"
                           :description="item.notText"></van-empty>
                <template v-else>
                  <div v-for="(nItem,nIndex) in item.list"
                       :key="nItem.id"
                       class="itemNotes_item flex_box">
                    <div @click="openPersonalDataDetails({id:nItem.userId})">
                      <img :style="'width:40px;height:40px;object-fit: contain;border-radius:50%;'"
                           :src="nItem.url" />
                      <div class="itemNotes_name flex_placeholder text_one2"
                           :style="'margin-top:0.08rem;text-align: center;'"
                           v-html="nItem.userName"></div>
                    </div>
                    <div class="flex_placeholder itemNotes_item_box"
                         :class="nIndex!=item.list.length-1?'van-hairline--bottom':''">
                      <div @click="showNoteDetails(nItem)"
                           class="itemNotes_content"
                           v-html="nItem.showAll?nItem.allContent:nItem.content"></div>
                      <div class="flex_box flex_align_end">
                        <div class="flex_placeholder">
                          <div v-if="nItem.name"
                               :style="'font-size: 12px;'"><span @click="openBookDetails(nItem)"
                                  :style="'font-size: 12px;color:'+nItem.Tcolor">{{nItem.name}}</span>{{nItem.author}}</div>
                        </div>
                        <div class="flex_box flex_align_center">
                          <div @click="clickLickBtn(nItem)"
                               class="flex_box flex_align_end">
                            <van-icon :size="18"
                                      :color="nItem.islike?appTheme:'#999'"
                                      name="good-job"></van-icon>
                            <span :style="'font-size: 12px;color:'+(nItem.islike?appTheme:'#999')">{{nItem.likeNum}}</span>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </template>
                <div v-if="item.btn.show"
                     class="flex_box flex_align_center flex_justify_content">
                  <div class="item_btn_box">
                    <van-button round
                                type="info"
                                size="large"
                                :color="appTheme">{{item.btn.text}}</van-button>
                  </div>
                </div>
              </div>
            </li>
            <!--名言样式-->
            <li v-else-if="item.type=='typeWords'"
                class="item_body_warp">
              <div class="item_body">
                <div v-if="item.title"
                     class="item_title"
                     :style="'color:'+item.color"
                     v-html="item.title"></div>
                <div class="itemWords_content_box"
                     :style="'border:1px solid '+item.color">
                  <div class="itemWords_content">
                    <div class="itemWords_content_hint"
                         :style="'top:-0.65rem;left:-0.45rem;color:'+item.color">“</div>
                    <div class="itemWords_content_hint"
                         :style="'bottom:-0.05rem;;right:-0.3rem;color:'+item.color">”</div>
                    <div :style="'font-size:14px;color:222;line-height: 1.6;'"
                         v-html="item.content"></div>
                  </div>
                  <div v-if="item.name"
                       class="itemWords_author"
                       :style="'font-size:14px;'">—— {{item.author}} ·<span @click="openBookDetails(item)"
                          :style="'font-size:14px;text-decoration:underline;color:'+item.Tcolor">{{item.name}}</span></div>
                </div>
                <div v-if="item.btn.show"
                     class="flex_box flex_align_center flex_justify_content">
                  <div class="item_btn_box">
                    <van-button round
                                type="info"
                                size="large"
                                :color="appTheme">{{item.btn.text}}</van-button>
                  </div>
                </div>
              </div>
            </li>
            <!--大咖样式-->
            <li v-else-if="item.type=='typeBig'"
                class="item_body_warp">
              <div class="item_body">
                <div v-if="item.title"
                     v-html="item.title"></div>
                <div class="flex_box flex_align_center"
                     style="padding: 0.1rem 0;">
                  <img v-if="item.user.url"
                       :src="item.user.url"
                       style="object-fit: contain;border-radius: 50%;margin-right: 0.1rem;width:40px;height:40px;"
                       alt="" />
                  <div class="flex_placeholder">
                    <div class="flex_box flex_align_center">
                      <div class="flex_placeholder flex_box flex_align_center T-flex-flow-row-wrap">
                        <div v-html="item.user.name"></div>
                        <div v-for="(nItem) in item.user.tags"
                             :key="nItem.id"
                             :style="'font-size:12px;padding:0.04rem;'">
                          <van-tag plain
                                   round
                                   type="primary">{{nItem}}</van-tag>
                        </div>
                      </div>
                      <div :style="'font-size:14px;padding:0.05rem;'">
                        <van-tag style="padding: 0.02rem 0.1rem;"
                                 plain
                                 round
                                 type="primary">{{'私信'}}</van-tag>
                      </div>
                    </div>
                    <div :style="'font-size:13px;margin-top:0.04rem;color:#888'"
                         v-html="item.user.profession"></div>
                  </div>
                </div>
                <div :style="'font-size:14px;padding:0.1rem 0;'"
                     v-html="'他的书籍'"></div>
                <van-empty v-if="!item.list || item.list.length == 0"
                           :style="'font-size:14px;'"
                           :image="icon_no_data"
                           :description="item.notText"></van-empty>
                <div v-else
                     class="itemSex_box flex_box T-flex-flow-row-wrap">
                  <div v-for="(nItem) in item.list"
                       :key="nItem.id"
                       class="itemSex_item">
                    <img v-if="nItem.url"
                         :style="'width:93px;height:119px;object-fit: contain;border-radius: 0.02rem;'"
                         :src="nItem.url" />
                    <div v-if="nItem.name"
                         class="itemSex_name text_one2"
                         v-html="nItem.name"></div>
                  </div>
                </div>
              </div>
            </li>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

    <!--添加分类-->
    <van-overlay :show="addCategory"
                 z-index="100"
                 :lock-scroll="false">
      <div class="T-flexbox-vertical flex_align_center flex_justify_content">
        <div class="category_box T-flexbox-vertical"
             style="max-height: 70%;">
          <div class="flex_placeholder"
               style="height:1px;overflow-y: auto;-webkit-overflow-scrolling: touch;">
            <van-empty v-if="switchs.data.length <= 1"
                       :style="'font-size:14px;'"
                       :image="icon_no_data"
                       :description="'暂无分类'"></van-empty>
            <div v-else
                 v-for="(nItem,nIndex) in switchs.data"
                 :key='nItem.id'>
              <div v-if="nIndex != 0"
                   class="category_item flex_box flex_align_center">
                <van-icon @click="editCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'edit'"></van-icon>
                <div @click="clickCategory(nItem,nIndex)"
                     class="flex_placeholder"
                     :style="'color:'+appTheme+';text-align: center;'">{{nItem.label}}</div>
                <van-icon @click="deleteCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'close'"></van-icon>
              </div>
            </div>
          </div>
          <div @click.stop="createCategory()"
               class="category_item flex_box flex_align_center flex_justify_content">
            <van-icon :size="21"
                      :color="appTheme"
                      :name="'plus'"></van-icon>
            <div :style="'color:'+appTheme+';text-align: center;margin:0 0.4rem 0 0.1rem;'">新建分类</div>
          </div>
          <div class="flex_box flex_align_center flex_justify_content">
            <van-tag @click.stop="addCategory = false;"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;"
                     :color="appTheme"
                     plain
                     type="primary">{{'关闭'}}</van-tag>
            <!-- <van-tag @click="clickCategory({label:'',value:''})"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;margin-left: 0.1rem;"
                     :color="appTheme"
                     type="primary">{{'确认'}}</van-tag> -->
          </div>
        </div>
      </div>
    </van-overlay>
    <van-dialog v-model:show="addTypeNameShow"
                title="请输入分类名字"
                :confirmButtonColor="appTheme"
                @confirm="confirmAddTypeName"
                show-cancel-button>
      <van-field v-model="addTypeName"
                 label="分类名字"
                 placeholder="请输入分类名字" />
    </van-dialog>
  </div>
  <div style="height:50px;"></div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { Toast, Sticky, Empty, Overlay, Tag, Icon, Field, Dialog, NavBar } from 'vant'
export default {
  name: 'recommend',
  components: {
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const router = useRouter()
    const route = useRoute()
    const data = reactive({
      title: route.query.title || '',
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      value: '',
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      switchs: { value: '', data: [{ label: '所有书籍', value: '' }] },
      addCategory: false, // 添加分类是否
      optionItem: null,
      editIndex: 0,
      isEdit: false,
      addTypeNameShow: false,
      addTypeName: ''
    })
    onMounted(() => {
      getList()
    })
    // 推荐列表
    const getList = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      }
      var res = []
      res = await $api.bookAcademy.recommendlList(datas)
      var { data: list } = res
      var dataListNew = []
      list.forEach((element, index) => {
        var item = {}
        item.index = index + ((data.pageNo - 1) * data.pageSize)
        var itemType = element.type
        if (itemType === 'book') { // 书籍
          element = element.book
          item.count = element.count || ''
          item.type = element.count === 3 ? 'typeThree' : element.count === 6 ? 'typeSex' : 'typeOne'
          item.title = element.mainTitle || ''
          item.smallTitle = element.subhead || ''
          var books = element.books || []
          if (books.length < 1) { // 没关联书籍 但是发布了 不显示
            return
          }
          item.books = books
          if (item.type === 'typeOne') {
            item.nowSwitch = 0// 现在的位置角标
            item.maxSwitch = books.length// 共有几次可以切换 为1时不能切换
            showItemData(item)
            item.btn = { show: true, text: item.maxSwitch > 1 ? '换一本' : '加入书架', plain: item.maxSwitch === 1 }
            if (item.maxSwitch === 1) {
              // item.btn.text = ''
              existMyBook(item)// 如果只有一本 检查是否在书架中
              // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
            }
          } else if (item.type === 'typeThree' || item.type === 'typeSex') {
            item.nowSwitch = 0// 现在的位置角标  向上取整
            item.maxSwitch = Math.ceil(books.length / item.count)
            item.btn = { show: item.maxSwitch > 1, text: '换一批' }
            item.notText = '暂无数据'
            showItemData(item)
          }
        } else if (itemType === 'goldword') { // 金玉良言
          element = element.goldword
          item.type = 'typeWords'
          item.title = '金玉良言'
          item.color = '#DCB769'
          item.Tcolor = '#1987FF'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.content = element.content || ''
          item.id = element.bookId || ''
          item.author = element.authorName || ''
          item.name = element.bookName || ''
        } else if (itemType === 'note') { // 阅读笔记
          element = element.note
          item.type = 'typeNotes'
          item.title = '精选笔记'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.list = []
          var nItem = { islike: element.hasClick, likeNum: element.likenum }
          nItem.url = element.headImg || ''
          nItem.userName = element.createBy || ''
          nItem.userId = element.userId || ''
          nItem.content = element.noteContent || ''
          nItem.allContent = ''
          nItem.showAll = false
          nItem.noteId = element.id || ''
          nItem.id = element.bookId || ''
          nItem.Tcolor = '#1987FF'
          nItem.author = element.authorName || ''
          nItem.name = element.bookName || ''
          item.list.push(nItem)
        } else {

        }
        dataListNew.push(item)
      })
      data.dataList = data.dataList.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    // 展示 一本 三本 六本 书
    const showItemData = (_item) => {
      var books = _item.books
      if (_item.type === 'typeOne') {
        _item.id = books[_item.nowSwitch].bookId || ''
        _item.url = books[_item.nowSwitch].coverImgUrl || ''
        _item.txt = { url: books[_item.nowSwitch].bookContentUrl || '', state: 0, schedule: -1, name: books[_item.nowSwitch].bookName || '', bookType: books[_item.nowSwitch].bookType || '3' }
        _item.name = books[_item.nowSwitch].bookName || ''
        _item.hint = books[_item.nowSwitch].recommendWord || ''
      } else if (_item.type === 'typeThree' || _item.type === 'typeSex') {
        var nowData = []
        if (_item.maxSwitch > 1) {
          books.forEach((_eItem, _eIndex, _eArr) => {
            var itemSwitch = Math.ceil((_eIndex + 1) / (_item.count))
            if (itemSwitch - 1 === _item.nowSwitch) {
              nowData.push(_eItem)
            }
          })
        }
        if (nowData.length < _item.count) { // 翻页少于当前数量 再加上去
          var nowLength = _item.count - nowData.length
          books.forEach((_eItem, _eIndex, _eArr) => {
            if (_eIndex < nowLength) {
              nowData.push(_eItem)
            }
          })
        }
        _item.list = []
        nowData.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
          var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
          item.id = _eItem.bookId || ''// 书本id
          item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
          _item.list.push(item)
        })
      }
    }

    // 点击 展示 笔记
    const showNoteDetails = async (_item) => {
      if (!_item.showAll && !_item.allContent) { // 要展示所有的时候 又没有内容的时候 就请求
        var datas = {
          id: _item.noteId
        }
        var { data: list } = await $api.bookAcademy.existBook(datas)
        if (list) {
          _item.allContent = list.noteContent
          _item.showAll = true
        } else {
          _item.showAll = false
        }
      } else {
        _item.showAll = !_item.showAll
      }
    }

    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }

    // 列表点击 换一本或加入书架
    const listBtnClick = (_item) => {
      if (_item.maxSwitch === 1) { // 为1时 加入书架
        if (_item.btn.existMyBook) {
          goBookReader(_item.txt, _item.id)
        } else {
          data.optionItem = _item
          data.addCategory = true
          getTypeList()// 获取所有分类
        }
      } else { // 换一本
        if (_item.nowSwitch + 1 >= _item.maxSwitch) {
          _item.nowSwitch = 0
        } else {
          _item.nowSwitch++
        }
        showItemData(_item)
      }
    }
    // 检查是否在书架
    const existMyBook = async (_item) => {
      var datas = {
        bookId: _item.id
      }
      var { data: list } = await $api.bookAcademy.existBook(datas)
      _item = data.dataList[_item.index]
      _item.btn.existMyBook = list || false
      _item.btn.text = _item.btn.existMyBook ? '继续阅读' : '加入书架'
      _item.btn.show = true
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }

    // 添加到分类里
    const clickCategory = async (_item) => {
      var clickName = data.optionItem.name
      var clickId = data.optionItem.id
      var datas = {
        bookIds: clickId, typeId: _item.value, isSingle: true
      }
      var res = []
      res = await $api.bookAcademy.addBookToType(datas)
      var { data: list } = res
      if (list) {
        data.addCategory = false
        data.optionItem.btn.existMyBook = true
        data.optionItem.btn.text = '开始阅读'
        data.optionItem = null
        Toast('书本【' + clickName + '】添加到' + (!_item.label ? '书架' : ('分类【' + _item.label + '】')) + (list ? '成功' : '失败'))
      }
    }
    const confirmAddTypeName = async () => {
      var typeName = data.addTypeName
      if (!(typeName).replace(/(^\s*)|(\s*$)/g, '')) {
        return Toast('请输入分类名字')
      }
      var datas = {}
      if (data.isEdit) {
        datas = {
          id: typeName, typeName: typeName
        }
        var { data: editData } = await $api.bookAcademy.editType(datas)
        if (editData) {
          Toast('修改分类' + (editData ? '成功' : '失败'))
          data.switchs.data[data.editIndex].label = typeName
          data.addTypeName = ''
        }
      } else {
        datas = {
          typeName: typeName
        }
        var { data: addData } = await $api.bookAcademy.addType(datas)
        if (addData) {
          Toast('新增分类' + (addData ? '成功' : '失败'))
          data.addTypeName = ''
          data.switchs.data.push({ label: typeName, value: addData })
        }
      }
    }
    // 创建新分类
    const createCategory = () => {
      data.addTypeNameShow = true
      data.isEdit = false
    }
    // 修改分类
    const editCategory = (_item, _index) => {
      data.addTypeNameShow = true
      data.addTypeName = _item.label
      data.editIndex = _index
      data.isEdit = true
    }
    // 删除分类
    const deleteCategory = async (_item, _index) => {
      var datas = {
        id: _item.value
      }
      var { data: list } = await $api.bookAcademy.delType(datas)
      if (list === 1) {
        Toast('删除分类' + (list === 1 ? '成功' : '失败'))
        $general.delItemForKey(_index, data.switchs.data)
        if (_item.value === data.switchs.value) { // 当前 正处于分类  回到所有书籍
          data.switchs.value = data.switchs.data[0].value
          onRefresh()
        }
      }
    }

    // 获取所有分类
    const getTypeList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getALlTypeList(datas)
      data.switchs.data = [{ label: '所有书籍', value: '' }]
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        data.switchs.data.push({ label: _eItem.typeName, value: _eItem.id })
      })
    }

    // 点赞 取消点赞
    const clickLickBtn = async (_item, _type) => {
      _item.islike = !_item.islike
      var datas = {}
      if (_item.islike) {
        _item.likeNum++
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.addClick(datas, _type)
      } else {
        _item.likeNum--
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.cansalClick(datas, _type)
      }
    }
    const onSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    const onClickButton = () => { router.push({ name: 'library', query: {} }) }
    const onChange = () => {

    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onChange, onSearch, onClickLeft, onClickButton, getList, openBookDetails, onRefresh, onLoad, showItemData, showNoteDetails, listBtnClick, existMyBook, getTypeList, clickCategory, deleteCategory, createCategory, editCategory, confirmAddTypeName, clickLickBtn }
  }
}
</script>
<style lang="less" scoped>
@import "./recommend.less";
</style>
