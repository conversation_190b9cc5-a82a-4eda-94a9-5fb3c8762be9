const socialsMyList = () => import('@/views/social/socialsMyList')
const socialsNews = () => import('@/views/social/socialsNews')
const socials = () => import('@/views/social/socials')
const socialsExcellentList = () => import('@/views/social/socialsExcellentList')
const socialsDetails = () => import('@/views/social/socialsDetails')
const socialsAllList = () => import('@/views/social/socialsAllList')
const socialsProcess = () => import('@/views/social/socialsProcess')
const social = [
  {
    path: '/socialsMyList',
    name: 'socialsMyList',
    component: socialsMyList,
    meta: {
      title: '我的社情民意',
      keepAlive: true
    }
  },
  {
    path: '/socialsNews',
    name: 'socialsNews',
    component: socialsNews,
    meta: {
      title: '新增社情民意',
      keepAlive: true
    }
  },
  {
    path: '/socials',
    name: 'socials',
    component: socials,
    meta: {
      title: '社情民意首页',
      keepAlive: true
    }
  },
  {
    path: '/socialsExcellentList',
    name: 'socialsExcellentList',
    component: socialsExcellentList,
    meta: {
      title: '优秀社情民意',
      keepAlive: true
    }
  },

  {
    path: '/socialsDetails',
    name: 'socialsDetails',
    component: socialsDetails,
    meta: {
      title: '详情',
      keepAlive: true
    }
  },
  {
    path: '/socialsAllList',
    name: 'socialsAllList',
    component: socialsAllList,
    meta: {
      title: '所有社情民意',
      keepAlive: true
    }
  },
  {
    path: '/socialsProcess',
    name: 'socialsProcess',
    component: socialsProcess,
    meta: {
      title: '处理信息',
      keepAlive: true
    }
  }
]
export default social
