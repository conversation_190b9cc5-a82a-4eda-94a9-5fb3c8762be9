<template>
  <div class="zxBookmain">
    <div class="topImg">
      <img class="bgimg"
           :src="bgimg[0]"
           @click="change()"
           alt="">
      <!-- <img class="topback" @click="back()" src="../../assets/img/Frame153.png" /> -->
    </div>
    <input type="file"
           accept="image/*"
           capture="camera"
           ref="camera"
           @change="getcamera($event)"
           v-show=false>
    <input type="file"
           accept="image/*"
           ref="img"
           @change="getimg($event)"
           v-show=false>
    <van-action-sheet v-model:show="showsheet"
                      :actions="actions"
                      cancel-text="取消"
                      @select="select"
                      close-on-click-action
                      @cancel="onCancel" />
    <!-- 中间部分 说明书----委员读书群 -->
    <div class="centerbook flex_box flex_align_center margintop15">
      <div class="yx-10 centerbook-box"
           v-for="i of centerList"
           :key="i"
           @click="noticeList(i.text)">
        <div class="text">{{i.text}}</div>
        <div class="text2">{{i.text2}}</div>
        <div class="yx-all">
          <img src="../../assets/img/mainbook.png"
               alt="">
        </div>
      </div>
    </div>
    <div class="centerwy flex_box flex_align_center"
         @click="wypush">
      <div class="yx-all-wy">
        <img src="../../assets/img/wy.png"
             alt="">
      </div>
      <div class="font">
        <div class="text flex_box">
          {{wyText}}
          <div class="yx-number"
               v-if="number>0">{{number}}</div>
        </div>
        <div class="text2">{{wyText2}}</div>
      </div>
    </div>
    <!-- 尾部列表 -->
    <div class="centerbook flex_box flexwarp flex_align_center">
      <div class="yx-10 centerbook-box margintop15"
           style="background:#FFFFFF"
           v-for="i of centerList2"
           :key="i"
           @click="openbookStore(i)">
        <div class="text">{{i.text}}</div>
        <div class="text2">{{i.text2}}</div>
        <img class="bookimg"
             :src="i.url"
             alt="">
      </div>
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, ref, inject } from 'vue'
// import writeRember from '../writeRember/writeRember'
import { ActionSheet, Toast, ImagePreview } from 'vant'

// import * as RongIMLib from '@rongcloud/imlib-next'
export default {
  name: 'zxBookmain',
  components: {
    [ActionSheet.name]: ActionSheet,
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  setup () {
    const camera = ref(null)
    const img = ref(null)
    const router = useRouter()
    const $api = inject('$api')
    const actions = [
      // { name: '相机' },
      { name: '相册' },
      { name: '预览' }
    ]
    const data = reactive({
      value: '',
      number: '0',
      show: false,
      showsheet: false,
      bgimg: '',
      // bgimg: [require('../../assets/img/topimg.png')],
      centerList: [
        { text: '倡议书', text2: '倡议书' },
        { text: '活动通知', text2: '活动通知' }
      ],
      centerList2: [
        { text: '推荐书目', text2: '推荐书目', url: require('../../assets/img/1.png') },
        { text: '委员荐书', text2: '委员荐书', url: require('../../assets/img/2.png') },
        { text: '学习资料', text2: '学习资料', url: require('../../assets/img/3.png') },
        { text: '专题荟萃', text2: '专题荟萃', url: require('../../assets/img/4.png') },
        { text: '读书心得', text2: '读书心得', url: require('../../assets/img/5.png') },
        { text: '学用结合', text2: '学用结合', url: require('../../assets/img/6.png') }
      ],
      wyText: '委员读书群',
      wyText2: '委员读书群',
      isUsing: false
    })
    onMounted(() => {
      // init()
      getList()
      getsysbg()
    })
    // 打开委员读书群
    const wypush = async () => {
      router.push({ name: 'msgList' })
    }
    // 通知公告
    const noticeList = async (name) => {
      if (name === '活动通知') {
        // router.push({ name: 'bookNotice', query: { title: name } })
        router.push({ name: 'noticeList', query: { title: name, type: '通知' } })
      } else if (name === '倡议书') {
        router.push({ name: 'noticeList', query: { title: name, type: '公告' } })
      } else {
      }
    }
    // 打开书目
    const openbookStore = async (text) => {
      switch (text.text) {
        case '推荐书目':
          router.push({ name: 'library', query: { title: text.text } })
          // router.push({ name: 'recommend', query: { title: text.text } })
          break
        case '委员荐书':
          console.log(1)
          router.push({ name: 'commissarRecom', query: { title: text.text } })
          break
        case '学习资料':
          router.push({ name: 'newsList', query: { title: text.text, module: 6 } })
          break
        case '专题荟萃':
          router.push({ name: 'ZT', query: { title: text.text } })
          break
        case '读书心得':
          router.push({ name: 'notes', query: { title: text.text } })
          // router.push({ name: 'student', query: { title: text.text } })
          break
        case '学用结合':
          router.push({ name: 'newsList', query: { title: text.text, module: 7 } })
          // router.push({ name: 'zxBoolmain', query: { title: text.text } })
          break
      }
    }
    // 列表
    const getList = async () => {
    }
    // 返回
    const back = async () => {
    }
    // 打开相机
    const change = async () => {
      if (!data.isUsing) {
        data.showsheet = true
      }
    }
    // 获取背景
    const getsysbg = async () => {
      var { data: list } = await $api.general.getsysbgimg({
        pageNo: 1,
        pageSize: 20
      })
      data.bgimg = []
      list.forEach(element => {
        if (element.type === 'myBackground') {
          data.isUsing = element.isUsing === '1'
          if (!data.isUsing) {
            getbg()
          } else {
            data.bgimg.push(element.iconFullUrl)
          }
        }
      })
      console.log(data.bgimg)
    }
    // 获取背景
    const getbg = async () => {
      var { data: list } = await $api.general.getbgimg()
      data.bgimg = []
      data.bgimg.push(list)
      console.log(data.bgimg)
    }
    // 获取相机照片后..
    const getcamera = async (e) => {
      console.log(e.target.files)
      const formData = new FormData()
      formData.append('attachment', e.target.files[0])
      formData.append('module', 'mine_bg')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const { data: msg } = await $api.general.uploadFile(formData)
      console.log(msg)
      changeBgImg(msg[0].id)
    }
    // 获取相册照片后..
    const getimg = async (e) => {
      console.log(e.target.files)
      const formData = new FormData()
      formData.append('attachment', e.target.files[0])
      formData.append('module', 'mine_bg')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const { data: msg } = await $api.general.uploadFile(formData)
      if (msg) {
        changeBgImg(msg[0].id)
      }
    }
    const changeBgImg = async (id) => {
      await $api.bookAcademy.editmainpage({ imgId: id })
      getbg()
    }
    const onCancel = () => Toast('取消')
    const select = (_data) => {
      switch (_data.name) {
        case '相机':
          camera.value.click()
          break
        case '相册':
          img.value.click()
          break
        case '预览':
          ImagePreview({
            images: data.bgimg,
            closeable: true
          })
          break
      }
    }
    // const getTotalUnreadCount = () => {
    //   const includeMuted = false
    //   const conversationTypes = [RongIMLib.ConversationType.PRIVATE, RongIMLib.ConversationType.GROUP]
    //   RongIMLib.getTotalUnreadCount(includeMuted, conversationTypes).then(res => {
    //     if (res.code === 0) {
    //       data.number = res.data
    //     } else {
    //       console.log(res.code, res.msg)
    //     }
    //   }).catch(error => {
    //     console.log(error)
    //   })
    // }
    // const init = () => {
    //   RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
    //   const Events = RongIMLib.Events
    //   /** 接收消息监听器 */
    //   const callback = function (messages) {
    //     console.log(messages)
    //     getTotalUnreadCount()
    //   }
    //   RongIMLib.addEventListener(Events.MESSAGES, callback)
    //   RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
    //     console.log(res)
    //     getTotalUnreadCount()
    //   })
    // }
    return { ...toRefs(data), img, wypush, noticeList, openbookStore, getList, camera, back, change, getimg, getcamera, onCancel, ActionSheet, actions, Toast, select }
  }
}
</script>
<style lang="less" scoped>
@import "./zxBookmain.less";
</style>
