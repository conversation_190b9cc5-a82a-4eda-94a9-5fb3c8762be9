<template>
  <div class="gender-ratio-chart">
    <div class="chart-container">
      <!-- ECharts饼图 -->
      <div :id="chartId" class="chart"></div>
      <!-- 性别统计信息 -->
      <div class="gender-stats">
        <div class="gender-item male">
          <img src="../../../../assets/img/largeScreen/man.png" alt="" class="gender-icon">
          <div class="gender-info">
            <div class="gender-text">男性 {{ maleCount }} 名</div>
            <div class="gender-percent">{{ malePercent }}%</div>
          </div>
        </div>
        <div class="gender-item female">
          <img src="../../../../assets/img/largeScreen/woman.png" alt="" class="gender-icon">
          <div class="gender-info">
            <div class="gender-text">女性 {{ femaleCount }} 名</div>
            <div class="gender-percent">{{ femalePercent }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'GenderRatioChart',
  props: {
    maleCount: {
      type: Number,
      default: 314
    },
    femaleCount: {
      type: Number,
      default: 108
    },
    id: {
      type: String,
      default: () => ''
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    // 计算百分比
    const malePercent = computed(() => {
      const total = props.maleCount + props.femaleCount
      return total > 0 ? ((props.maleCount / total) * 100).toFixed(1) : '0.0'
    })

    const femalePercent = computed(() => {
      const total = props.maleCount + props.femaleCount
      return total > 0 ? ((props.femaleCount / total) * 100).toFixed(1) : '0.0'
    })

    // 初始化图表
    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
            confine: true, // 限制 tooltip 在容器内
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'transparent',
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            extraCssText: 'border-radius: 4px; padding: 8px 12px;'
          },
          series: [
            {
              type: 'pie',
              startAngle: 60,
              avoidLabelOverlap: false,
              radius: ['50%', '80%'],
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 5
              },
              data: [
                {
                  value: props.maleCount,
                  name: '男性',
                  itemStyle: {
                    color: '#4AA3FF'
                  }
                },
                {
                  value: props.femaleCount,
                  name: '女性',
                  itemStyle: {
                    color: '#FF6B9D'
                  }
                }
              ],
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              emphasis: {
              }
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }
    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }
    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartId,
      malePercent,
      femalePercent
    }
  }
}
</script>

<style lang="less" scoped>
.gender-ratio-chart {
  .chart-container {
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;
    height: 145px;

    .chart {
      width: 38%;
      height: 145px;
    }

    .gender-stats {
      width: 62%;
      display: flex;
      flex-direction: column;
      gap: 10x;

      .gender-item {
        display: flex;
        align-items: center;

        .gender-icon {
          width: 34px;
          height: 34px;
          margin-right: 6px;
        }

        .gender-info {
          flex: 1;
          display: flex;
          align-items: center;

          .gender-text {
            font-size: 14px;
            color: #666;
            margin-right: 12px;
          }

          .gender-percent {
            font-size: 14px;
          }
        }

        &.male .gender-percent {
          color: #3A93FF;
        }

        &.female .gender-percent {
          color: #FF738D;
        }
      }
    }
  }
}
</style>
