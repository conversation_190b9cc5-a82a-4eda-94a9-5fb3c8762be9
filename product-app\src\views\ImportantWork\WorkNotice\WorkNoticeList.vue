<template>
  <div class="WorkNoticeList">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="工作通知" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <ul class="vue_newslist_box">
          <div v-for="(item, index) in dataList" :key="index" class="van-hairline--bottom">
            <van-cell clickable style=" padding: 10px 15px;" @click="openDetails(item)">
              <div class="flex_box">
                <img class="vue_newslist_img" v-if="item.url" :src="item.url" :alt="cacheImg(item, true)" />
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two" :style="$general.loadConfiguration()">
                    <span v-if="item.isTop == '1'" class="vue_newslist_top" :style="$general.loadConfiguration(-4)">
                      <van-tag plain :color="appTheme">置顶</van-tag>
                    </span>
                    <span class="inherit" style="font-size: 18px;margin-left: 5px;" v-html="item.title"></span>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div class="vue_newslist_time" style="font-size: 13px;">
                      {{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</div>
                    <div class="vue_newslist_source text_one2 flex_placeholder" style="font-size: 13px;">
                      {{ item.source }}
                    </div>
                  </div>
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Tag } from 'vant'
export default {
  name: 'WorkNoticeList',
  components: {
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: data.pageNo,
        pageSize: 10,
        columnId: route.query.columnId
      })
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      var attachmentList = row.fileListVo || []
      var attachInfo = []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          item.id = _eItem.id || ''
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          attachInfo.push(item)
        })
      }
      if (attachmentList.length > 0) {
        annexClick(attachInfo[0])
      } else {
        router.push({ name: 'WorkNoticeDetails', query: { id: row.id } })
      }
    }
    const annexClick = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.host === '*************') {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://*************/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        } else {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://www.cszysoft.com/appShare/qdzx/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        }
      } else {
        var param = {
          id: item.id,
          url: item.url,
          name: item.name
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), dayjs, search, onRefresh, onLoad, openDetails, $general, onClickLeft }
  }
}
</script>
<style lang="less">
.WorkNoticeList {
  width: 100%;
  min-height: 100vh;
  background: #f8f8f8;
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
