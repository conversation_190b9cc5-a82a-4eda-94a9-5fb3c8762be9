<template>
  <div class="leaveApproval">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <div v-if="switchs.data.length != 0"
         :style="$general.loadConfiguration(1)">
      <van-tabs v-model="switchs.value"
                @click="tabClick"
                :color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :name="item.code">
          <template v-slot:title>
            <div class="switch_box T-flexbox-vertical flex_align_center">
              <div :style="$general.loadConfiguration()+'color:'+switchColors[parseInt(index%10)]">{{item.num}}</div>
              <div :style="$general.loadConfiguration(-1)+'margin-top:8px;color:#222;font-weight: 600;'">{{item.name}}</div>
            </div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      success-text="刷新成功">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <van-checkbox-group v-if="listData.length != 0"
                            v-model="selects"
                            :checked-color="appTheme"
                            :icon-size="((appFontSize+3)*0.01)+'rem'">
          <div v-for="(item,index) in listData"
               :key="index"
               @click="openUser(item)"
               class="user_item flex_box flex_align_center click van-hairline--bottom">
            <template v-if="showItem(item)">
              <img :src="item.headImg"
                   :style="$general.loadConfigurationSize(32)+'margin-right:10px;'">
              <div class="flex_placeholder">
                <div class="text_one2"
                     :style="$general.loadConfiguration(-1)+'color:#262626;font-weight: 600;'">{{item.userName}}</div>
                <div class="text_one2"
                     :style="$general.loadConfiguration(-4)+'margin-top:5px;color:#8C8C8C;font-weight: 400;'">{{item.position}}</div>
              </div>
              <div @click.stop=""
                   style="padding: 10px 5px;">
                <van-checkbox v-if="managementBtns.length != 0"
                              :name="item"
                              icon-size="15"></van-checkbox>
              </div>
            </template>
          </div>
        </van-checkbox-group>
      </van-list>
    </van-pull-refresh>
    <footer v-if="managementBtns.length != 0 && listData.length != 0"
            :style="'padding-bottom:' + (((pageType=='page'?safeAreaBottom:0)+16)*0.01)+'px'"
            style="position:absolute;bottom: 30px;width: 100%;">
      <div class="flex_box"
           :style="$general.loadConfiguration(-2)">
        <template v-for="(item,index) in managementBtns"
                  :key="index">
          <van-button loading-type="spinner"
                      round
                      :loading-size="((appFontSize-4)*0.01)+'rem'"
                      size="large"
                      :disabled="selects.length == 0"
                      :color="item.color?item.color:appTheme"
                      @click="footerClick(item)"
                      :loading="item.loading"
                      :loading-text="item.name+'中...'"
                      style="margin: 15px;">{{item.name}}</van-button>
        </template>
      </div>
    </footer>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, Toast } from 'vant'
export default {
  name: 'leaveApproval',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appFontSize: 16,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      seachText: '',
      loading: false,
      finished: false,
      refreshing: false,
      switchs: { value: '', data: [] },
      switchColors: ['#4D8CF8', '#F8B24D', '#7D4DF8', '#1C850A', '#F84D4D', '#7D4DF8', '#1C850A', '#4D8CF8', '#F8B24D', '#7D4DF8'], // 角标从0开始
      selects: [], // 选择用户ids
      managementBtns: [],
      listData: [],
      showFooterDialog: false
    })
    onMounted(() => {
      console.log('data.switchs.value', data.switchs.value)
      getHeader()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.switchs.data = []
      data.switchs.value = ''
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getHeader()
    }
    watch(() => data.switchs.value, (_val, _oldVal) => {
      data.managementBtns = []
      switch (_val) {
        case '6':// 已请假
          data.managementBtns.push({ type: 'cancelLeave', disabled: false, color: '', loading: false, name: '取消请假' })
          break
        case '7':// 请假未通过
          data.managementBtns.push({ type: 'byLeave', disabled: false, color: '', loading: false, name: '请假通过' })
          break
        case '8':// 请假待审核
          data.managementBtns.push({ type: 'byLeave', disabled: false, color: '', loading: false, name: '请假通过' })
          data.managementBtns.push({ type: 'notLeave', disabled: false, color: '', loading: false, name: '请假不通过' })
          break
      }
    })
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.switchs.data = []
      data.loading = true
      data.finished = false
      getListData()
    }
    // 获取表头数据
    const getHeader = async (_getNum) => {
      const res = await $api.conferenceActivitiesFile.getCopyAttendanceHeader({
        conferenceId: data.id
      })
      console.log('获取表头数据====>', res)
      if (res) {
        res.data.forEach(function (_eItem, _eIndex, _eArr) {
          data.switchs.data.push(_eItem)
        })
        if (!data.switchs.value || !$general.getItemForKey(data.switchs.value, data.switchs.data, 'code')) {
          data.switchs.value = res.data[0].code
          _getNum = false
        }
        if (!_getNum) {
          getListData(0)
        }
      } else {
        data.showSkeleton = false
      }
    }
    // 点击某个人
    const openUser = async (_item) => {
      if (data.switchs.value === '6' || data.switchs.value === '7' || data.switchs.value === '8') {
        router.push({ name: 'meetingLeave', query: { title: '请假', id: data.id, paramType: 'addLeave', leaveId: _item.id } })
      }
    }
    // 获取人员列表
    const getListData = async (_type) => {
      if (!_type) {
        data.pageNo = 1
        data.selects = [] // 为0时清空选择
      }
      const res = await $api.conferenceActivitiesFile.getAttendanceListVos({
        pageNo: 1,
        pageSize: 99,
        conferenceId: data.id,
        code: data.switchs.value
      })
      if (!_type) { // 有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
        data.pageNo = 1
        data.listData = []
      }
      var { data: list, total } = res
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
      console.log('获取人员列表===>', res)
    }
    // tab切换事件
    const tabClick = async (_name, _title) => {
      data.switchs.value = _name
      data.seachText = ''
      data.listData = []
      getListData(0)
    }
    // 显示搜索的人
    const showItem = async (_item) => {
      if (data.seachText) {
        if (_item.userName.indexOf(data.seachText) === -1) {
          return false
        }
      }
      return true
    }
    const footerClick = async (_item, _state) => {
      if (!_state) {
        Dialog.confirm({
          message: '确定' + _item.name + '吗？'
        }).then(() => {
          footerClick(_item, true)
        }).catch(() => {
          Toast({ type: 'fail', message: '取消', duration: 1500 })
        })
        return
      }
      var url = ''
      var param = {
        conferenceId: data.id
      }
      var userIds = ''
      var listIds = ''
      data.selects.forEach(function (_eItem, _eIndex, _eArr) {
        userIds += (userIds ? ',' : '') + _eItem.userId
        listIds += (listIds ? ',' : '') + _eItem.id
      })
      switch (_item.type) {
        case 'cancelSignUp': // 取消报名
          url = 'conference/delJoinUser'
          param.ids = listIds
          param.type = 'meetingSignUp'
          break
        case 'signUp': // 报名
          url = 'conference/addSignUpJoinUser'
          param.userIds = userIds
          break
        case 'cancelSignIn': // 取消签到
          url = 'conference/delJoinUser'
          param.ids = listIds
          param.type = 'meetingSignIn'
          break
        case 'signIn': // 签到
          url = 'conference/addSignInJoinUser'
          param.userIds = userIds
          break
        case 'cancelLeave': // 取消请假
          url = 'conferenceleave/dels'
          param.ids = listIds
          break
        case 'byLeave': // 请假通过
          url = 'conferenceleave/auditLeave'
          param.ids = listIds
          param.state = 1
          break
        case 'notLeave': // 请假不通过
          url = 'conferenceleave/auditLeave'
          param.ids = listIds
          param.state = 2
          break
      }
      const res = await $api.conferenceActivitiesFile.conferenceleaveDels(url, param)
      if (res) {
        var code = res ? res.errcode : ''
        Toast(code !== 200 ? res.errmsg || res.data || (_item.name + '失败') : (_item.name + '成功'))
        if (code === 200) {
          data.selects.forEach(function (_eItem, _eIndex, _eArr) {
            $general.delItemForKey(_eItem.id, data.listData, 'id')
          })
          data.selects = []
          data.seachText = ''
          data.switchs.data = []
          getHeader(true)
        }
      } else {
        Toast('失败,请重试！')
      }
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, $general, search, openUser, tabClick, showItem, onRefresh, footerClick }
  }
}
</script>
<style lang="less" scoped>
.leaveApproval {
  background: #fff;
  .switch_box {
    padding: 0 12px 0 12px;
  }
  .user_item {
    padding: 10px 14px;
    background: #fff;
  }
  .van-tab {
    padding: 9px 7px 1px 7px;
  }
  .van-tabs__line {
    bottom: 2px;
    width: 19px;
    height: 2px;
  }
  .van-tabs__nav--line {
    padding-bottom: 5px;
  }
}
</style>
