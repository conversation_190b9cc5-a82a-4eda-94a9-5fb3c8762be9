<template>
  <div class="bookReadUserList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <van-search v-model="keyword"
                  @search="search"
                  @clear="search"
                  placeholder="请输入搜索关键词" />
    </van-sticky>
    <!-- <div class="bookReadUserListHead">
      <van-search v-model="keyword"
                  placeholder="请输入搜索关键词" />
    </div> -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <!--数据列表-->
      <ul v-if="readUser.length != 0"
          class="search_box">
        <li @click="openUser(item)"
            v-for="(item,index) in readUser.filter(data => !keyword || data.userName.toLowerCase().includes(keyword.toLowerCase()))"
            :key="index"
            class="search_item flex_box click">
          <img :style="'width:46px;height:46px;margin-right:10px;object-fit: contain;border-radius: 50%;'"
               :src="item.url" />
          <div class="flex_placeholder">
            <div class="text_one2 flex_placeholder"
                 :style="'margin-top:0'"
                 v-html="item.name"></div>
            <div class=""
                 :style="'font-size:13px;margin-top:8px;line-height: 1;color: #5E646D;'"
                 v-html="item.position"></div>
          </div>
        </li>
      </ul>
      <!-- <div class="readUserBox">
        <div class="readUserItem"
             v-for="item in readUser.filter(data => !keyword || data.userName.toLowerCase().includes(keyword.toLowerCase()))"
             :key="item.userId"
             @click="openUser(item)">
          <div class="readUserImg">
            <img :src="item.headImg"
                 alt="">
          </div>
          <div class="readUserName">{{item.userName}}</div>
        </div>
      </div> -->
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'bookReadUserList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '人员详情',
      id: route.query.id,
      keyword: '',
      pageNo: 1,
      pageSize: 10,
      refreshing: false,
      readUser: []
    })
    onMounted(() => {
      readingDetail()
    })
    const onRefresh = () => {
      data.readUser = []
      data.pageNo = 1
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      setTimeout(() => {
        readingDetail()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      readingDetail()
    }
    const readingDetail = async () => {
      const { data: list } = await $api.bookAcademy.getReadusers({
        bookId: data.id,
        pageNo: data.pageNo,
        pageSize: data.pageSize
      })
      list.forEach(item => {
        item.id = item.userId || ''
        item.name = item.userName || ''
        item.url = item.headImg || ''
        item.position = item.position || ''
      })
      data.readUser = data.readUser.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.readUser.length < data.pageSize) {
        data.finished = true
      }
    }
    const openUser = (_item) => {
      router.push({ name: 'personData', query: { id: _item.userId } })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onLoad, onClickLeft, openUser }
  }
}
</script>
<style lang="less">
.bookReadUserList {
  width: 100%;
  min-height: 100%;
  // padding-top: 44px;
  background-color: #fff;
  .bookReadUserListHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .van-search {
      width: 100%;
      .van-cell {
        padding: 3px 0;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        .van-field__body {
          font-size: 14px;
        }
      }
    }
  }

  .popular_box {
    margin-top: 30px;
    padding: 0 16px;
  }
  .popular_item {
    margin: 19px 14px 0 0;
    padding: 7px 15px 7px 7px;
    background: #f3f3f3;
    color: #5e646d;
    border-radius: 20px;
  }
  .search_box {
    padding: 11px 15px;
  }
  .search_item {
    padding: 13px 10px;
    box-shadow: 0px 0px 26px -11px rgba(0, 0, 0, 0.4);
    border-radius: 16px;
  }
  .search_item + .search_item {
    margin-top: 20px;
  }
}
</style>
