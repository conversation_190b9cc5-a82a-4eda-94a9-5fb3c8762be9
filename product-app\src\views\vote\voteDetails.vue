<template>
  <div class="voteDetails">
    <!--顶上图片-->
    <div class="voteDetails_top_img">
      <img v-if="themeImg.url"
           loading="lazy"
           :src="themeImg.url" />
    </div>
    <!--正文内容-->
    <div v-if="title"
         class="voteDetails_box"
         :style="bgImg.url?('background:url('+bgImg.url+') no-repeat top center / contain;'):('background: linear-gradient(180deg, '+ $general.colorRgba((customColor||appTheme),0.6)+', #FFFFFF);')">
      <!--活动数据-->
      <div v-if="$general.getItemForKey('8',pageConfig)"
           class="box_item">
        <div v-if="$general.getItemForKey('1',pageConfig)"
             class="flex_box flex_align_center flex_justify_content">
          <div :style="$general.loadConfiguration(-2)+'background: linear-gradient(124deg,'+(customColor||appTheme)+' 0%,'+$general.colorRgba((customColor||appTheme),0.8)+' 53%,'+$general.colorRgba((customColor||appTheme),0.6)+' 100%);box-shadow: 0px 2px 5px 0px '+$general.colorRgba((customColor||appTheme),0.6)+';margin-bottom:12px;'"
               class="vote_title">{{title}}</div>
        </div>
        <div style="margin-bottom:12px;">
          <div v-if="voteStatus == 3"
               :style="$general.loadConfiguration(-1)+'text-align: center;color:#ccc'">投票已结束</div>
          <template v-else>
            <div :style="$general.loadConfiguration(-1)+'text-align: center;margin-bottom:6px;color:'+(customColor||appTheme)">距活动{{voteStatus==1?'开始':'结束'}}时间</div>
            <div :style="$general.loadConfiguration()"
                 class="flex_box flex_justify_content">
              <van-count-down :time="dayjs(voteStatus==1?startTime:endTime).valueOf() - dayjs().valueOf()">
                <template #default="timeData">
                  <span :style="'background-color:'+(customColor||appTheme)"
                        class="downTime_item inherit"
                        v-html="timeData.days"></span>
                  <span :style="'color:'+(customColor||appTheme)"
                        class="inherit">天</span>
                  <span :style="'background-color:'+(customColor||appTheme)"
                        class="downTime_item inherit"
                        v-html="(timeData.hours<10?'0':'') + timeData.hours"></span>
                  <span :style="'color:'+(customColor||appTheme)"
                        class="inherit">:</span>
                  <span :style="'background-color:'+(customColor||appTheme)"
                        class="downTime_item inherit"
                        v-html="(timeData.minutes<10?'0':'') + timeData.minutes"></span>
                  <span :style="'color:'+(customColor||appTheme)"
                        class="inherit">:</span>
                  <span :style="'background-color:'+(customColor||appTheme)"
                        class="downTime_item inherit"
                        v-html="(timeData.seconds<10?'0':'') + timeData.seconds"></span>
                </template>
              </van-count-down>
            </div>
          </template>
        </div>
        <!--票数啥的-->
        <div style="padding: 6px;"
             class="flex_box flex_align_center">
          <div v-if="$general.getItemForKey('9',pageConfig)"
               class="vote_num_item"
               :style="'border-right:1px solid '+(customColor||appTheme)">
            <div :style="$general.loadConfiguration(2)+'text-align: center;font-weight: 800;color:'+(customColor||appTheme)">{{totalVotes}}</div>
            <div :style="$general.loadConfiguration(-2)+'text-align: center;color:#999;margin-top:4px;'">总票数</div>
          </div>
          <div class="vote_num_item"
               :style="'border-right:1px solid '+(customColor||appTheme)">
            <div :style="$general.loadConfiguration(2)+'text-align: center;font-weight: 800;color:'+(customColor||appTheme)">{{participatingVotes}}</div>
            <div :style="$general.loadConfiguration(-2)+'text-align: center;color:#999;margin-top:4px;'">参与投票数</div>
          </div>
          <div class="vote_num_item">
            <div :style="$general.loadConfiguration(2)+'text-align: center;font-weight: 800;color:'+(customColor||appTheme)">{{views}}</div>
            <div :style="$general.loadConfiguration(-2)+'text-align: center;color:#999;margin-top:4px;'">访问量</div>
          </div>
        </div>
      </div>
      <!--规则  内容-->
      <div v-if="$general.getItemForKey('2',pageConfig)"
           class="box_item">
        <div class="flex_box flex_align_center flex_justify_content">
          <div :style="$general.loadConfiguration(-1)+'background: linear-gradient(124deg,'+(customColor||(customColor||appTheme))+' 0%,'+$general.colorRgba((customColor||appTheme),0.8)+' 53%,'+$general.colorRgba((customColor||appTheme),0.6)+' 100%);box-shadow: 0px 2px 5px 0px '+$general.colorRgba((customColor||appTheme),0.6)+';margin-bottom:12px;'"
               class="vote_title">投票规则</div>
        </div>
        <div class="n_details_content"
             :style="$general.loadConfiguration(-2)+''"
             v-html="content.length > contentMore.length?!contentMore.is?content.substring(0,contentMore.length)+'...':content:content"></div>
        <div v-if="content.length > contentMore.length"
             :style="$general.loadConfiguration(-3)"
             @click="contentMore.is = !contentMore.is;"
             class="flex_box flex_align_center flex_justify_content"
             style="padding: 3px 0;color: #999">
          <van-icon :class="contentMore.is?'arrowClose':'arrowOpen'"
                    :size="((appFontSize-3)*0.01)+'rem'"
                    :color="'#999'"
                    name="arrow"
                    style="margin-right:4px;"></van-icon>{{contentMore.is?'收起':'展开'}}
        </div>
      </div>
      <!--列表-->
      <div class="box_item"
           style="padding: 0 12px;">
        <!--搜索-->
        <div v-if="$general.getItemForKey('3',pageConfig)"
             id="search"
             class="search_box"
             :style="$general.loadConfiguration()+'padding-top:12px;'">
          <div class="search_warp flex_box">
            <div @click="search()"
                 class="search_btn img_btn flex_box flex_align_center flex_justify_content"><van-icon :size="((appFontSize)*0.01)+'rem'"
                        :color="'#A6A6A6'"
                        name="search"></van-icon></div>
            <form class="flex_placeholder flex_box flex_align_center search_input"
                  action="javascript:return true;"><input :style="$general.loadConfiguration(-3)"
                     :placeholder="seachPlaceholder"
                     maxlength="100"
                     type="search"
                     ref="search"
                     @keyup.enter="search()"
                     v-model="seachText" /></form>
          </div>
        </div>
        <div v-if="$general.getItemForKey('4',pageConfig)"
             :style="$general.loadConfiguration(-2)+'padding:0.03rem 0.3rem;'">
          <van-tabs v-model:active="switchs.value"
                    @click="tabClick"
                    :ellipsis="false"
                    :color="(customColor||appTheme)">
            <van-tab v-for="(item,index) in switchs.data"
                     :key="index"
                     :title="item.label"
                     :name="item.value"></van-tab>
          </van-tabs>
        </div>
        <div v-if="listData.length != 0"
             :style="$general.getItemForKey('4',pageConfig)?'':'padding-top:12px;'">
          <!--排行榜-->
          <div v-if="switchs.value == 1"
               style="margin:0 -12px;">
            <van-pull-refresh v-model="refreshing"
                              @refresh="onRefresh"
                              success-text="刷新成功">
              <van-list v-model:loading="loading"
                        :finished="finished"
                        finished-text="没有更多了"
                        offset="52"
                        @load="onLoad">
                <div v-for="(item,index) in listData"
                     :key="index"
                     class="flex_box flex_align_center"
                     style="padding: 12px 32px 12px 0;">
                  <template v-if="seachText?item.name.indexOf(seachText)!=-1:true">
                    <div style="min-width: 32px;margin-right:8px;"
                         class="flex_box flex_justify_content">
                      <div v-if="index > 2"
                           :style="$general.loadConfiguration(1)+'font-weight: 500;color:#ccc;'">{{index+1}}</div>
                      <van-icon v-else
                                :size="20"
                                :color="['#ffeb43','#e4e7e7','#ff9265'][index]"
                                :name="'medal'"></van-icon>
                    </div>
                    <div v-if="displayMode == '2'"
                         :style="$general.loadConfigurationSize(16)+'margin-right:10px;'">
                      <van-image round
                                 width="100%"
                                 height="100%"
                                 fit="cover"
                                 :src="item.url"></van-image>
                    </div>
                    <div class="text_one2 flex_placeholder"
                         :style="$general.loadConfiguration(-2)+'color:#333;margin-right:24px;'">{{item.name}}</div>
                    <div v-if="$general.getItemForKey('7',pageConfig)"
                         :style="$general.loadConfiguration(-2)+'color:#333;'">{{item.voteNumber}}票</div>
                  </template>
                </div>
              </van-list>
            </van-pull-refresh>
          </div>
          <!--列表 图表式-->
          <div v-else-if="displayMode == '2'"
               class="flex_box T-flex-flow-row-wrap"
               style="margin-right: -12px;">
            <van-pull-refresh v-model="refreshing"
                              @refresh="onRefresh"
                              success-text="刷新成功">
              <van-list v-model:loading="loading"
                        :finished="finished"
                        finished-text="没有更多了"
                        offset="52"
                        @load="onLoad">
                <div class="flex_box T-flex-flow-row-wrap">
                  <div v-for="(item,index) in listData"
                       :key="index"
                       @click="openItemDetails(item)"
                       class="vote_item click">
                    <template v-if="seachText?item.name.indexOf(seachText)!=-1:true">
                      <div style="width: 172px;height: 170px;position: relative;">
                        <div v-if="$general.getItemForKey('5',pageConfig)"
                             :style="$general.loadConfiguration(-4)"
                             class="vote_sort">{{item.sort}}号</div>
                        <div v-if="$general.getItemForKey('7',pageConfig)"
                             :style="$general.loadConfiguration(-4)"
                             class="vote_numVotes">{{item.voteNumber}}票</div>
                        <van-image width="100%"
                                   height="100%"
                                   fit="cover"
                                   :src="item.url"></van-image>
                      </div>
                      <div style="padding: 10px;">
                        <div v-if="$general.getItemForKey('6',pageConfig)"
                             class="text_one2"
                             :style="$general.loadConfiguration(-4)+'color:#333;text-align: center;'">{{item.name}}</div>
                        <div style="margin: 6px 0;"
                             :style="$general.loadConfiguration(-4)"
                             @click.stop
                             class="flex_box flex_justify_content">
                          <van-button round
                                      loading-type="spinner"
                                      :loading-size="((appFontSize)*0.01)+'rem'"
                                      :loading="item.loading"
                                      :loading-text="item.nText+'中...'"
                                      :color="item.color?item.color:(customColor||appTheme)"
                                      :disabled="item.disabled || voteStatus != 2 || preview"
                                      @click.stop="voteBtnClick(item)"
                                      :icon="item.icon">{{preview?'预览中':item.nText}}</van-button>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </van-list>
            </van-pull-refresh>
          </div>
          <!--列表  列表式-->
          <div v-else-if="displayMode == '1'"
               style="margin:0 -12px;">
            <van-pull-refresh v-model="refreshing"
                              @refresh="onRefresh"
                              success-text="刷新成功">
              <van-list v-model:loading="loading"
                        :finished="finished"
                        finished-text="没有更多了"
                        offset="52"
                        @load="onLoad">
                <div v-for="(item,index) in listData"
                     :key="index"
                     @click="openItemDetails(item)"
                     class="flex_box flex_align_start click van-hairline--bottom"
                     style="padding: 14px 0;">
                  <template v-if="seachText?item.name.indexOf(seachText)!=-1:true">
                    <div v-if="$general.getItemForKey('5',pageConfig)"
                         :style="$general.loadConfiguration(-4)"
                         class="vote_sort2">{{item.sort}}号</div>
                    <div class="flex_placeholder"
                         style="padding: 0 12px 0 12px;">
                      <div class="text_two"
                           :style="$general.loadConfiguration()+'color:#333;font-weight: 600;margin-bottom: 6px;'">{{item.name}}</div>
                      <div class="flex_box flex_align_center">
                        <div v-if="$general.getItemForKey('7',pageConfig)"
                             :style="$general.loadConfiguration(-2)+'color:'+(customColor||appTheme)+';font-weight: 800;'">{{item.voteNumber}}票</div>
                        <div class="flex_placeholder"></div>
                        <div :style="$general.loadConfiguration(-3)"
                             @click.stop>
                          <van-button round
                                      loading-type="spinner"
                                      :loading-size="((appFontSize)*0.01)+'rem'"
                                      :loading="item.loading"
                                      :loading-text="item.nText+'中...'"
                                      :color="item.color?item.color:(customColor||appTheme)"
                                      :disabled="item.disabled || voteStatus != 2 || preview"
                                      @click.stop="voteBtnClick(item)"
                                      :icon="item.icon">{{preview?'预览中':item.nText}}</van-button>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </van-list>
            </van-pull-refresh>
          </div>
        </div>
      </div>
    </div>
    <van-overlay :show="showItem"
                 @click="showItem = false">
      <div class="T-flexbox-vertical showOverlay">
        <div class="flex_placeholder"></div>
        <div class="showOverlay_item_box">
          <div class="flex_box flex_align_center">
            <div v-if="clickItem.url"
                 style="width: 100px;height: 105px;position: relative;border-radius: 3px;overflow: hidden;margin-right:8px;">
              <van-image width="100%"
                         height="100%"
                         fit="cover"
                         :src="clickItem.url"></van-image>
            </div>
            <div class="flex_placeholder">
              <div :style="$general.loadConfiguration(1)+'color:#333;text-align: center;margin-bottom:12px;font-weight: 600;'">{{clickItem.name}}</div>
              <div style="padding: 6px 0;"
                   class="flex_box flex_align_center">
                <div class="vote_num_item">
                  <div :style="$general.loadConfiguration(1)+'text-align: center;font-weight: 800;color:'+(customColor||appTheme)">{{clickItem.rank}}</div>
                  <div :style="$general.loadConfiguration(-3)+'text-align: center;color:#999;margin-top:4px;'">排名</div>
                </div>
                <div v-if="$general.getItemForKey('7',pageConfig)"
                     class="vote_num_item"
                     :style="'border-left:1px solid '+(customColor||appTheme)">
                  <div :style="$general.loadConfiguration(1)+'text-align: center;font-weight: 800;color:'+(customColor||appTheme)">{{clickItem.voteNumber}}</div>
                  <div :style="$general.loadConfiguration(-3)+'text-align: center;color:#999;margin-top:4px;'">票数</div>
                </div>
              </div>
            </div>
          </div>
          <div :style="$general.loadConfiguration(-4)+'padding:8px 0;'"
               @click.stop>
            <van-button style="width:100%;padding: 12px 10px;"
                        round
                        loading-type="spinner"
                        :loading-size="((appFontSize)*0.01)+'rem'"
                        :loading="clickItem.loading"
                        :loading-text="clickItem.nText+'中...'"
                        :color="clickItem.color?clickItem.color:(customColor||appTheme)"
                        :disabled="clickItem.disabled || voteStatus != 2 || preview"
                        @click.stop="voteBtnClick(clickItem)"
                        :icon="clickItem.icon">{{preview?'预览中':clickItem.nText}}</van-button>
          </div>
          <div :style="$general.loadConfiguration()+'color:#333;margin-top:12px;'"
               v-html="clickItem.instructions"></div>
        </div>
      </div>
    </van-overlay>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, CountDown, Overlay, Toast } from 'vant'

export default {
  name: 'voteDetails',
  components: {
    [VanImage.name]: VanImage,
    [Overlay.name]: Overlay,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [CountDown.name]: CountDown
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      id: route.query.id,
      themeImg: { url: '' },
      bgImg: { url: '' },
      title: '优秀委员评选',
      content: '',
      contentMore: { length: 80, is: false },
      voteStatus: '',
      startTime: '',
      endTime: '',
      totalVotes: '',
      participatingVotes: '',
      views: '',
      displayMode: '',
      pageConfig: '',
      isPublish: '',
      customColor: '',
      periodSet: '',
      voteNum: '',
      voteType: '',
      playerMax: '',

      listData: [],
      switchs: { value: 0, data: [{ label: '列表', value: 0 }, { label: '排行榜', value: 1 }] },
      seachPlaceholder: '请输入搜索内容',
      clickItem: {},
      showItem: false,
      preview: false // 是否预览
    })
    onMounted(() => {
      redPointSign()
      getVoteInfo()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getVoteInfo()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 去除红点
    const redPointSign = async () => {
      const res = await $api.Networkpolitics.redPointSign({
        id: data.id,
        type: 'vote'
      })
      console.log('去除红点', res)
    }
    // 点击投票
    const voteBtnClick = async (_item) => {
      const res = await $api.vote.voterecordAdd({
        voteId: data.id,
        voteOptionsId: _item.id
      })
      if (res.errcode === 200) {
        Toast('投票成功')
        _item.nText = '已投票'
        _item.disabled = true
        data.listData = []
        getListData()
      }
      console.log('点击投票了==>>', res)
    }

    const openItemDetails = (_item) => {
      data.clickItem = _item
      data.showItem = true
    }

    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getListData()
    }

    // 获取投票详情
    const getVoteInfo = async () => {
      const res = await $api.vote.voteInfo(data.id)
      var { data: info } = res
      var bigImageforVote = $general.getItemForKey('bigImageforVote', info.attachmentList || [], 'moduleType')
      data.themeImg.url = bigImageforVote ? bigImageforVote.filePath : ''
      data.title = info.theme || ''
      data.content = info.voteRule || ''
      data.id = info.id || ''
      var bgImageforVote = $general.getItemForKey('bgImageforVote', info.attachmentList || [], 'moduleType')
      data.bgImg.url = bgImageforVote ? bgImageforVote.filePath : ''
      data.voteStatus = info.voteStatus
      data.startTime = info.startTime || ''
      data.endTime = info.endTime || ''
      data.totalVotes = info.totalVotes
      data.participatingVotes = info.totalVoteArchive
      data.views = info.visits
      data.displayMode = info.displayMode
      var a = (info.pageConfig || '').split(',')
      data.pageConfig = a.map(item => ({ key: item.toString() }))
      data.isPublish = info.isPublish
      data.customColor = info.color
      data.periodSet = info.periodSet
      data.voteNum = info.voteNum
      data.voteType = info.voteType
      data.playerMax = info.playerMax
      getListData()
    }

    // 获取具体列表数据
    const getListData = async () => {
      const res = await $api.vote.voteoptionsList({
        pageNo: 1,
        pageSize: 10,
        keyword: data.seachText,
        voteId: data.id
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.loading = false
        item.nText = (item.isVote === 1 ? '已' : '') + '投票'
        item.disabled = item.isVote === 1
        var bgImageforVoteItem = $general.getItemForKey('bgImageforVoteItem', item.attachmentList || [], 'moduleType')
        item.url = bgImageforVoteItem ? bgImageforVoteItem.filePath : ''
      })
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }

    const tabClick = () => {
      data.seachText = ''
      data.listData = []
      data.showSkeleton = true
      getListData(0)
    }

    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    return { ...toRefs(data), dayjs, search, onRefresh, onLoad, $general, tabClick, annexClick, openItemDetails, voteBtnClick }
  }
}
</script>
<style lang="less" scoped>
.voteDetails {
  background: #fff;
  .voteDetails_top_img {
    height: 200px;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .voteDetails_box {
    padding: 14px;
    min-height: 360px;
  }
  .box_item {
    background: #fff;
    border-radius: 5px;
    padding: 12px;
  }
  .box_item + .box_item {
    margin-top: 12px;
  }
  .vote_title {
    width: 180px;
    padding: 6px 14px;
    text-align: center;
    border-radius: 13px;
    font-weight: 600;
    color: #ffffff;
  }
  .downTime_item {
    display: inline-block;
    color: #fff;
    text-align: center;
    min-width: 18px;
    min-height: 18px;
    padding: 3px 2px;
    line-height: 1;
    border-radius: 2px;
    margin: 2px 5px;
  }
  .vote_num_item {
    width: 100%;
  }

  .n_details_content {
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    color: #666;
    line-height: 1.69;
  }
  .search_box {
    padding: 0;
    .search_warp {
      min-height: 30px;
      border-radius: 3px;
      background: #fff;
      border: 1px solid #ebebeb;
    }
  }
  .vote_item {
    width: 155px;
    background: #ffffff;
    border-radius: 0px 0px 3px 3px;
    margin: 0 12px 12px 0;
    border: 1px solid #f1f1f1;
  }

  .vote_sort {
    position: absolute;
    left: 0;
    top: 0;
    line-height: 1;
    padding: 4px 8px;
    z-index: 1;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    border-radius: 3px 0px 3px 0px;
  }
  .vote_sort2 {
    line-height: 1;
    padding: 5px 12px;
    height: 20px;
    border-radius: 0px 999px 999px 0px;
    background: #8b8b8c;
    color: #fff;
  }
  .vote_numVotes {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    line-height: 1;
    padding: 5px 0;
    z-index: 1;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    text-align: center;
  }

  .showOverlay {
    min-height: 320px;
    .showOverlay_item_box {
      height: 450px;
      padding: 10px 20px 5px 20px;
      box-sizing: border-box;
      background: #fafafa;
      border-top-right-radius: 20px;
      border-top-left-radius: 20px;
    }
  }
}
#app .van-tabs__line {
  width: 16px;
}
#app .van-button {
  padding: 5px 20px;
  min-width: 80px;
}
</style>
