const newsList = () => import('@/views/news/newsList')
const newsZTList = () => import('@/views/news/newsZTList')
const newsZTListTwo = () => import('@/views/news/newsZTListTwo')
const newsDetails = () => import('@/views/news/newsDetails')
const ZT = () => import('@/views/news/ZT')

const news = [{
  path: '/newsList',
  name: 'newsList',
  component: newsList,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/newsZTList',
  name: 'newsZTList',
  component: newsZTList,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/newsZTListTwo',
  name: 'newsZTListTwo',
  component: newsZTListTwo,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/newsDetails',
  name: 'newsDetails',
  component: newsDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/ZT',
  name: 'ZT',
  component: ZT,
  meta: {
    title: '专题',
    keepAlive: true
  }

}]
export default news
