import { HTTP, loginUc, Gepoint } from '../http.js'
class general extends HTTP {
  general (url, params) {
    return this.request({ url: url, data: params })
  }

  // 更换头像
  fileMyHeadimg (params) {
    return this.request({ url: '/file/headimg', data: params })
  }

  // 收藏列表
  favoriteList (params) {
    return this.request({ url: '/favorite/list', data: params })
  }

  // 操作手册 1
  operationmanualList (params) {
    return this.request({ url: '/operationmanual/list', data: params })
  }

  // 操作手册详情
  operationmanualInfo (params) {
    return this.request({ url: `/operationmanual/info/${params}` })
  }

  // 操作手册 2
  tutorialvideolList (params) {
    return this.request({ url: '/tutorialvideo/list', data: params })
  }

  // 智慧豆数量
  wisdombeanCount (params) {
    return this.request({ url: '/wisdombean/userScore/count', data: params })
  }

  // 今日上限
  wisdombeanToday (params) {
    return this.request({ url: '/wisdombean/userScore/today', data: params })
  }

  // 每日首次登录
  wisdombeanUserScore1 (params) {
    return this.request({ url: '/wisdombean/userScore/1', data: params })
  }

  // 获取阅读文章
  wisdombeanUserScore2 (params) {
    return this.request({ url: '/wisdombean/userScore/2', data: params })
  }

  // 发布有效评论
  wisdombeanUserScore3 (params) {
    return this.request({ url: '/wisdombean/userScore/3', data: params })
  }

  // 转发文章
  wisdombeanUserScore4 (params) {
    return this.request({ url: '/wisdombean/userScore/4', data: params })
  }

  // 阅读文章
  wisdombeanAdd (params) {
    return this.request({ url: '/wisdombean/add', data: params })
  }

  appallogusers (params) {
    return this.request({ url: '/appallogusers', data: params })
  }

  applogusers (params) {
    return this.request({ url: '/applogusers', data: params })
  }

  wholeuserJoinapp (params) {
    return this.request({ url: '/wholeuser/joinapp', data: params })
  }

  // 山东通单点
  authenticationLogin (params) {
    return this.request({ url: 'shandongAccess/authenticationAppLogin?code=' + params.code + '&verifyCode=' + params.verifyCode })
  }

  // 政通单点
  authenticationPcLogin (params) {
    return this.request({ url: '/shandongAccess/authenticationPcLogin', data: params })
  }

  // 获取公开的配置管理
  getPersonal (params) {
    return this.request({ url: `${Gepoint}/rest/oagxh/personal_getdetail_v7`, data: 'params={}' })
  }

  // 获取公开的配置管理
  nologin (params) {
    return this.request({ url: '/readonfig/nologin', data: params })
  }

  // 登录
  calogin (params) {
    return this.request({ url: `${loginUc}/calogin`, data: params })
  }

  // 用户中心登录
  loginUc (params, header) {
    return this.request({ url: `${loginUc}/login?`, data: params, method: 'post', header: header })
  }

  // 切换系统登录
  changearea () {
    return this.request({ url: '/changearea' })
  }

  systemfeedbackAdd (params) {
    return this.request({ url: '/systemfeedback/add', data: params })
  }

  systemfeedbackList (params) {
    return this.request({ url: '/systemfeedback/list', data: params })
  }

  systemfeedbackInfo (params) {
    return this.request({ url: `/systemfeedback/info/${params}` })
  }

  // 获取菜单模块
  appList (params) {
    return this.request({ url: '/module/appList', data: params })
  }

  // 获取字典
  pubkvs (params) {
    return this.request({ url: '/dictionary/pubkvs?', data: params })
  }

  // 获取角色
  findRolesByUserId (params) {
    return this.request({ url: '/role/findRolesByUserId', data: params })
  }

  // 获取角色相碰
  findAllRoleAttach (params) {
    return this.request({ url: '/role/attach/findAllRoleAttach', data: params })
  }

  // 获取当前届次
  crrentcircles (params) {
    return this.request({ url: '/member/crrentcircles?', data: params })
  }

  // 选人点获取机构
  pointrees (params) {
    return this.request({ url: `/pointrees/${params}` })
  }

  // 选人点根据机构获取用户
  users (params) {
    return this.request({ url: '/pointree/users', data: params })
  }

  // 选人点根据用户id获取用户
  poinexistsids (params) {
    return this.request({ url: `/poinexistsids/${params}` })
  }

  // 获取办理单位
  chooseList (params) {
    return this.request({ url: '/flowgroup/chooseList', data: params })
  }

  // 获取背景图片
  getbgimg (params) {
    return this.request({ url: '/library/user/mainpage', data: params })
  }

  // 获取系统背景图片
  getsysbgimg (params) {
    return this.request({ url: '/appimage/list', data: params })
  }

  // 上传笔记
  uploadFile (params) {
    return this.request({ url: '/attachment/uploadFile', data: params })
  }

  // 添加笔记
  add (params) {
    return this.request({ url: '/syReadingNotes/add', data: params })
  }

  // 是否公开
  delsOpen (params) {
    return this.request({ url: '/syReadingNotes/delsOpen', data: params })
  }

  // 附件在线查看
  fcscloudFile (params) {
    return this.request({ url: 'https://www.yozodcs.com/fcscloud/file/http?', data: params })
  }

  // 附件在线查看
  fcscloudCompositeConvert (params) {
    return this.request({ url: 'https://www.yozodcs.com/fcscloud/composite/convert', data: params })
  }

  // 增加阅读数
  saveBrowse (params) {
    return this.request({ url: '/browse/save', data: params })
  }

  // 获取评论、点赞等5种数据
  getCommentStats (params) {
    return this.request({ url: '/comment/stats', data: params })
  }

  // 点赞
  saveFabulous (params) {
    return this.request({ url: '/fabulous/save', data: params })
  }

  // 取消点赞
  delFabulous (params) {
    return this.request({ url: '/fabulous/del', data: params })
  }

  // 收藏
  addFavorite (params) {
    return this.request({ url: '/favorite/add', data: params })
  }

  // 取消收藏
  delFavorite (params) {
    return this.request({ url: '/favorite/del', data: params })
  }

  // 新增分享
  saveShare (params) {
    return this.request({ url: '/share/save', data: params })
  }

  // 新增评论
  saveComment (params) {
    return this.request({ url: '/comment/save', data: params })
  }

  // 评论列表
  getCommentList (params) {
    return this.request({ url: '/comment/list', data: params })
  }

  // 删除评论
  delsComment (params) {
    return this.request({ url: '/comment/dels', data: params })
  }
}
export {
  general
}
