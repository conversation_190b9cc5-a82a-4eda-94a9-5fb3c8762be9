<template>
  <div :id="chartId" class="horizontal-bar-echarts"></div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'HorizontalBarEcharts',
  props: {
    id: { type: String, required: true },
    barList: { type: Array, default: () => [] },
    colorStart: { type: String, default: '#FFFFFF' },
    colorEnd: { type: String, default: '#EF817C' }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const renderChart = () => {
      if (!chartInstance) {
        chartInstance = echarts.init(document.getElementById(chartId.value))
      }
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: params => {
            const item = params[0]
            return `${item.name}<br/><span style="color:#0271E3;font-weight:bold;">${item.value}</span>`
          }
        },
        grid: { left: 10, right: 20, top: 10, bottom: 10, containLabel: true },
        xAxis: {
          type: 'value',
          splitLine: { show: false },
          axisLabel: { color: '#bfbfbf', interval: 0, fontSize: 14 }
        },
        yAxis: {
          type: 'category',
          data: props.barList.map(item => item.name).reverse(),
          axisLabel: {
            color: '#bfbfbf',
            fontSize: 13
          },
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#bfbfbf',
              width: '1'
            },
            show: true
          }
        },
        series: [
          {
            type: 'bar',
            data: props.barList.map(item => item.value).reverse(),
            barWidth: 12,
            barCategoryGap: '10%',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: props.colorStart },
                  { offset: 1, color: props.colorEnd }
                ]
              }
            },
            label: {
              show: true,
              position: 'right',
              color: '#bfbfbf',
              fontSize: 15
            }
          }
        ]
      }
      chartInstance.setOption(option)
    }

    onMounted(() => {
      renderChart()
    })

    onBeforeUnmount(() => {
      chartInstance && chartInstance.dispose()
    })

    watch(() => props.barList, () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      renderChart()
    }, { deep: true })

    return {
      chartId
    }
  }
})
</script>

<style scoped>
.horizontal-bar-echarts {
  width: 100%;
  height: 100%;
}
</style>
