<template>
  <div class="module">
    <div class="bg_default T-flexbox-vertical flex_align_center flex_justify_content" :style="'padding-top:' +
      safeAreaTop +
      'px;background-image: url(' +
      platformBackground +
      ')'
      ">
      <div class="bg_img_text">
        <img src="../../assets/img/zx.png" style="width: 30px;" />
        <img class="bg_default_icon" src="../../assets/img/font.png" />
      </div>
      <div class="scan">
        <img src="../../assets/img/saoyisao.png" alt="" style="width: 25px;height: 25px;" @click="openScan">
      </div>

      <!--一、轮播图-->
      <div class="carouselMap">
        <van-swipe :autoplay="30000" :show-indicators="true" @change="carouselChange">
          <van-swipe-item v-for="(item, index) in carouselList" :key="index" @click="details(item, 1)">
            <div class="carousel_img" :style="'background-image:url(' + item.url + ')'"></div>
            <div class="carousel_elBox">
              <div class="carousel_title" :style="$general.loadConfiguration(-2)" v-html="item.title"></div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <!--待办-->
    <div style="padding:35px 12px 8px 12px;">
      <van-swipe class="pendingMap" :show-indicators="true">
        <van-swipe-item class="flex_box" v-for="(item, index) in pendings" :key="index" @click="openDetails(item)">
          <div class="flex_placeholder" style="padding: 12px 10px;border-left: 5px solid #F69731">
            <div :style="$general.loadConfiguration(-2) + 'color:#F69731;font-weight: bold;'">
              您有{{ pendings.length }}项会议活动待处理</div>
            <div class="text_two"
              :style="$general.loadConfiguration(-1) + 'color:#333;font-weight: bold;line-height:1.5;padding:10px 0;min-height:50px;'">
              {{ item.name }}</div>
            <div class="flex_box flex_align_center">
              <van-icon :size="((appFontSize + 4) * 0.01) + 'rem'" color="#F69731" name="clock"></van-icon>
              <div class="flex_placeholder"
                :style="$general.loadConfiguration() + 'color:#333;font-weight: bold;padding:0 8px;'">
                {{ item.date ? dayjs(item.date).format('YYYY-MM-DD HH:mm') : '' }}</div>
              <div :style="$general.loadConfiguration(-3) + 'color:#666'">查看详情</div>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
    <!-- 工作应用 菜单栏目-->
    <div class="apply_box2">
      <div class="main_box_item_header flex_box flex_align_center apply_img_text">
        <img src="../../assets/img/yingyong.png" alt="" />
        <div class="main_box_item_header_text" v-html="'工作应用'"></div>
      </div>
      <van-grid clickable :column-num="4">
        <!--  :badge="getModuleRedPointNum(item)" -->
        <van-grid-item v-for="(item, index) in menuList" :key="index" :style="$general.loadConfiguration(-2)"
          @click="itemClick(item)">
          <template v-slot:default>
            <img :style="$general.loadConfigurationSize(32) + 'margin-bottom:0.03rem;object-fit: cover;'"
              :src="item.url" />
            <div :style="$general.loadConfiguration(-4) + 'font-weight: 500;color: #333;width: 55px;'" v-html="item.name">
            </div>
            <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
              :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
              :style="item.pointType == 'big' ? $general.loadConfiguration(-4) + $general.loadConfigurationSize(4) : $general.loadConfigurationSize(-6)"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '...' : item.pointNumber) : ''"></p>
          </template>
        </van-grid-item>
      </van-grid>
    </div>
    <!-- 工作品牌 -->
    <div class="apply_box">
      <div class="main_box_item_header flex_box flex_align_center apply_img_text">
        <img src="../../assets/img/yingyong.png" alt="" />
        <div class="main_box_item_header_text" v-html="'省政协工作应用'"></div>
      </div>
      <van-grid clickable :column-num="4">
        <van-grid-item v-for="(item, index) in pinpaiData" :key="index"
          :badge="item.pointNumber > 0 ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"
          :style="$general.loadConfiguration(-2)" @click="itemClick(item)">
          <template v-slot:default>
            <img :style="$general.loadConfigurationSize(32) + 'margin-bottom:0.03rem;object-fit: cover;'"
              :src="item.url" />
            <div :style="$general.loadConfiguration(-4) + 'font-weight: 500;color: #333;'" v-html="item.name"></div>
            <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
              :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
              :style="item.pointType == 'big' ? $general.loadConfiguration(-4) + $general.loadConfigurationSize(4) : $general.loadConfigurationSize(-6)"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '...' : item.pointNumber) : ''"></p>
          </template>
        </van-grid-item>
      </van-grid>
    </div>

    <!--专题-->
    <div v-if="newsSpecialsubjects.length != 0" class="main_box">
      <div class="main_box_item_header flex_box flex_align_center">
        <div class="main_box_item_header_line" :style="$general.loadConfigurationSize(-3, 'h') + ';background:' + appTheme">
        </div>
        <div class="main_box_item_header_text flex_placeholder" :style="$general.loadConfiguration(-1)" v-html="'专题'">
        </div>
      </div>
      <div style="padding: 8px 12px;">
        <van-swipe class="specialTopic_warp" :autoplay="6000" :show-indicators="true">
          <van-swipe-item v-for="(item, index) in newsSpecialsubjects" :key="index" @click="details(item, 2);">
            <div class="specialTopic_img" :style="'background-image:url(' + item.url + ')'"></div>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <!-- 聚焦与关注 -->
    <div v-if="dataList.length != 0" class="focus_item_box">
      <div class="focus_item_header flex_box flex_align_center">
        <div class="focus_item_header_line" :style="$general.loadConfigurationSize(-3, 'h') + ';background:' + appTheme">
        </div>
        <div class="focus_item_header_text flex_placeholder" :style="$general.loadConfiguration(-1)" v-html="'聚焦与关注'">
        </div>
      </div>
      <!--数据列表-->
      <ul class="vue_newslist_box" style="margin-top: 10px;">
        <van-swipe-cell v-for="(item, index) in dataList" :key="index" class="van-hairline--bottom">
          <van-cell clickable class="vue_newslist_item" style="padding: 10px 15px;" @click="details(item)">
            <div class="flex_box flex_align_center">
              <div class="flex_placeholder vue_newslist_warp">
                <div class="vue_newslist_title text_two" :style="$general.loadConfiguration()" v-html="item.title">
                </div>
                <div class="flex_box flex_align_center">
                  <div v-if="item.time" class="vue_newslist_source" :style="$general.loadConfiguration(-6)">
                    {{ dayjs(item.time).format('YYYY-MM-DD') }}</div>
                  <div v-if="item.createBy" class="vue_newslist_source"
                    :style="$general.loadConfiguration(-6) + 'margin-left:8px;'">{{ item.createBy }}</div>
                  <div class="flex_placeholder"></div>
                </div>
              </div>
              <img class="vue_newslist_img" v-if="item.url" :src="item.url" />
            </div>
          </van-cell>
        </van-swipe-cell>
      </ul>
      <!--<div class="notText" :style="loadConfiguration(-2)" v-html="pageNot.text" @click="loadMore()"></div>-->
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton" class="notText">
      <van-skeleton v-for="(item, index) in 3" :key="index" title :row="3"></van-skeleton>
    </div>
    <van-overlay :show="isShowLoading">
      <div style="text-align: center; padding-top: 100%">
        <van-loading size="24px" vertical text-color="#0094ff" color="#0094ff">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, computed } from 'vue'
import { SwipeCell, Swipe, SwipeItem, NavBar, Sticky, Grid, GridItem, Image as VanImage, Toast, Popover, Skeleton, Loading, Overlay, Lazyload } from 'vant'
import { useStore } from 'vuex'
export default {
  name: 'module',
  components: {
    [SwipeCell.name]: SwipeCell,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Lazyload.name]: Lazyload,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [Skeleton.name]: Skeleton,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Popover.name]: Popover
  },
  setup () {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: sessionStorage.getItem('appTheme'),
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: false,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: '1',
      carouselIndex: 0,
      carouselList: [],
      // dataList: [],
      appName: JSON.parse(sessionStorage.getItem('areaName')),
      menuList: [],
      pinpaiData: [],
      // newsSpecialsubjects: [],
      isShowLoading: false,
      logoTheme: sessionStorage.getItem('logo').split('#').length > 1 ? sessionStorage.getItem('logo').split('#')[1] : '',
      platformBackground: sessionStorage.getItem('appHomeBg'),
      pendings: [],
      ModuleRedDotsNumber1: computed(() => store.state.ModuleRedDotsNumber1),
      ModuleRedDotsNumber2: computed(() => store.state.ModuleRedDotsNumber2),
      ModuleRedDotsNumber3: computed(() => store.state.ModuleRedDotsNumber3),
      ModuleRedDotsNumber4: computed(() => store.state.ModuleRedDotsNumber4),
      ModuleRedDotsNumber5: computed(() => store.state.ModuleRedDotsNumber5),
      ModuleRedDotsNumber6: computed(() => store.state.ModuleRedDotsNumber6),
      newsSpecialsubjects: computed(() => store.state.newsSpecialsubjects),
      dataList: computed(() => store.state.dataList)
    })
    document.title = data.appName.slice(0, 2) + '政协'
    onMounted(() => {
      getRedPointNums()
      getCarousel() // 顶部轮播
      getPendingActivityLists()
      getPendingConferenceLists()
      getList() // 工作应用
      if (route.query.module) {
        data.module = route.query.module
      }
      // getSpecialsubjectRelateinfoList() // 聚焦与关注
    })
    const getRedPointNums = async () => {
      const ret = await $api.Networkpolitics.getRedPointNumByModule({ module: 'app' })
      const list = ret ? ret.data || [] : []
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          switch (_eItem.code) {
            case 'survey':
              store.commit('setOnlinePoliticalDiscussionRedDotsNumber', _eItem.count)
              break
            case 'meet':
              store.commit('setModuleRedDotsNumber1', _eItem.count || 0)
              break
            case 'activity':
              store.commit('setModuleRedDotsNumber2', _eItem.count || 0)
              break
            case 'memberead':
              store.commit('setModuleRedDotsNumber3', _eItem.count || 0)
              break
            case 'social_check_red_point':
              store.commit('setModuleRedDotsNumber4', _eItem.count || 0)
              break
            case 'vote':
              store.commit('setModuleRedDotsNumber5', _eItem.count || 0)
              break
            case 'questionnaire':
              store.commit('setModuleRedDotsNumber6', _eItem.count || 0)
              break
            default:
              break
          }
        })
      }
      data.menuList.forEach(item => {
        getModuleRedPointNum(item)
      })
    }
    // 统计各模块需要红点的赋值
    const getModuleRedPointNum = (_item) => {
      if (_item.name === '更多') {
        data.AllCount = data.ModuleRedDotsNumber1 + data.ModuleRedDotsNumber2 + data.ModuleRedDotsNumber3 + data.ModuleRedDotsNumber4 + data.ModuleRedDotsNumber5 + data.ModuleRedDotsNumber6
        data.menuList.forEach(item => {
          if (item.appUrl === 'conferenceActivities') {
            data.AllCount = data.AllCount - (data.ModuleRedDotsNumber1 + data.ModuleRedDotsNumber2)
          }
          if (item.appUrl === 'bookHome') {
            data.AllCount = data.AllCount - data.ModuleRedDotsNumber3
          }
          if (item.appUrl === 'socials') {
            data.AllCount = data.AllCount - data.ModuleRedDotsNumber4
          }
          if (item.appUrl === 'voteList') {
            data.AllCount = data.AllCount - data.ModuleRedDotsNumber5
          }
          if (item.appUrl === 'questionnaireList') {
            data.AllCount = data.AllCount - data.ModuleRedDotsNumber6
          }
          if (item.name === '更多') {
            item.pointNumber = data.AllCount
          }
        })
        return
      }
      if (_item.appUrl === 'conferenceActivities') { // 会议活动
        _item.pointNumber = data.ModuleRedDotsNumber1 + data.ModuleRedDotsNumber2 || 0
      } else if (_item.appUrl === 'bookHome') { // 书香政协
        _item.pointNumber = data.ModuleRedDotsNumber3 || 0
      } else if (_item.appUrl === 'socials') { // 社情民意
        _item.pointNumber = data.ModuleRedDotsNumber4 || 0
      } else if (_item.appUrl === 'voteList') { // 投票
        _item.pointNumber = data.ModuleRedDotsNumber5 || 0
      } else if (_item.appUrl === 'questionnaireList') { // 调查问卷
        _item.pointNumber = data.ModuleRedDotsNumber6 || 0
      } else {
        _item.pointNumber = ''
      }
    }
    // 会议待办进详情
    const openDetails = (row) => {
      router.push({ name: 'meetingDetailFile', query: { id: row.id } })
    }
    // 打开扫一扫
    const openScan = () => {
      router.push({ name: 'scan' })
    }
    // 获取首页置顶轮播图
    const getCarousel = async () => {
      var ret = await $api.news.getTopList({
        pageNo: 1,
        pageSize: 8,
        module: 1,
        areaId: data.user.areaId,
        auditingFlag: 1,
        isAppShow: 1
      })
      if (ret) {
        var topList = ret.data || []
        console.log('topList==>', topList)
        data.carouselList = []
        topList.forEach(element => {
          var image = element.image || {}
          element.url = element.leftImage || image.fullUrl || ''// 图片
          element.class = element.infoClass || ''
          element.relateType = '5'
        })
        data.carouselList = data.carouselList.concat(topList)
      }
    }
    // 获取首页活动待办
    const getPendingActivityLists = async (_num) => {
      const res = await $api.conferenceActivitiesFile.getPendingActivityLists()
      console.log('获取活动待办===>', res)
      var { data: list } = res
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          data.pendings.push(_eItem)
        })
      }
    }
    // 获取首页会议待办
    const getPendingConferenceLists = async (_num) => {
      const res = await $api.conferenceActivitiesFile.getPendingConferenceLists()
      console.log('获取会议待办==>', res)
      var { data: list } = res
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          data.pendings.push(_eItem)
        })
      }
    }
    // 改变事件
    const carouselChange = (_index) => {
      data.carouselIndex = _index
    }
    // 工作应用列表请求
    const getList = async () => {
      // var mainSelectData = localStorage.getItem(data.user.areaId + 'mainSelectData' + data.user.id) || ''
      // var mainSelect = []
      // if (mainSelectData) {
      //   mainSelect = mainSelectData.split('|,|')
      // }
      // data.isShowLoading = true
      var res = await $api.general.appList({
        parentId: sessionStorage.getItem('historyIndex'),
        areaId: data.user.areaId
      })
      var { data: list } = res
      if (data.newsSpecialsubjects.length === 0) {
        store.dispatch('fetchNewsSpecialsubjects')
      }
      if (data.dataList.length === 0) {
        const params = {
          subjectId: route.query.topic.split(',')[0],
          columnId: route.query.topic.split(',')[1]
        }
        store.dispatch('getSpecialsubjectRelateinfoList', params)
      }
      var lists = []
      if (list && list.length !== 0) {
        lists = list.find(item => item.name === '首页').children || []
        var dataLength = lists ? lists.length : 0
        var newMenu = []
        var allMenu = []
        for (var i = 0; i < dataLength; i++) {
          var item = {}
          var itemData = lists[i]
          item.id = itemData.id
          item.name = itemData.name
          item.url = itemData.iconUrl
          item.appUrl = itemData.infoUrl2
          item.needRecord = itemData.needRecord
          item.type = itemData.type
          item.remarks = itemData.remarks
          item.pointType = 'big'
          item.pointNumber = 0
          if (item.appUrl && item.appUrl !== '#') {
            allMenu.push(item)
          }
        }
        newMenu = allMenu
        // if (mainSelect.length !== 0) { // 有自己设置菜单 否则就按顺序来显示
        //   var delItems = []
        //   mainSelect.forEach(function (_eItem, _eIndex, _eArr) {
        //     var nItem = $general.getItemForKey(_eItem, allMenu, 'id')
        //     if (nItem) {
        //       newMenu.push(nItem)
        //     } else {
        //       delItems.push(_eItem)
        //     }
        //   })
        //   delItems.forEach(function (_eItem, _eIndex, _eArr) { // 先保存要删除的 再删除 再保存
        //     $general.delItemForKey(_eItem, mainSelect)
        //   })
        //   localStorage.setItem(data.user.areaId + 'mainSelectData' + data.user.id, mainSelect.join('|,|'))
        // } else {
        //   newMenu = allMenu
        // }
        if (newMenu.length > 8) { // 大于7个栏目 就只显示7个 加一个更多
          newMenu = newMenu.slice(0, 8)
        }
        data.menuList = newMenu
        data.menuList = data.menuList.filter(item => item.name !== '有事多商量')
        data.pinpaiData = allMenu.filter(item => item.name === '有事多商量')
        if (allMenu.length >= 8) {
          data.menuList.push({
            id: '',
            type: 'other',
            name: '更多',
            url: require('../../assets/img/icon_more.png'),
            pointType: 'big',
            pointNumber: 0,
            appUrl: `moduleMorePage?areaId=${data.user.areaId}`
          })
        }
      }
      // const newData = []
      // list[0].children.forEach(item => {
      //   var _item = {}
      //   _item.id = item.id
      //   _item.url = item.iconUrl
      //   _item.appUrl = item.infoUrl2
      //   _item.name = item.name
      //   _item.remarks = item.remarks
      //   _item.pointType = 'big'// 红点类型
      //   _item.pointNumber = 0// 数量
      //   if (_item.appUrl && _item.appUrl !== '#') {
      //     newData.push(_item)
      //   }
      // })
      // var a = data.menuList.concat(newData)
      // data.menuList = a.filter(item => item.name !== '有事多商量')
      // data.pinpaiData = a.filter(item => item.name === '有事多商量')
      // data.isShowLoading = false
      // data.showSkeleton = false
      // data.loading = false
      // data.refreshing = false
    }
    // 中间栏目点击
    const itemClick = (_item) => {
      console.log(JSON.stringify(_item))
      if (_item.appUrl) {
        var routerStrs2 = _item.appUrl + '?' + _item.remarks
        if (routerStrs2.indexOf('http://172.20.236.51:810') === 0 || routerStrs2.indexOf('http://111.231.25.206:8082') === 0) { // 济事商量
          const mobile = JSON.parse(sessionStorage.getItem('user')).mobile
          console.log('跳转协商平台手机号：', mobile)
          window.location.href = _item.appUrl + '?mobile=' + mobile
        } else if (routerStrs2.indexOf('http://172.20.57.9:99') === 0) { // OA
          const hrefTo = encodeURIComponent(JSON.parse(sessionStorage.getItem('desEncryptPhone')))
          const urlInfo = _item.appUrl.slice(0, 40)
          window.location.href = urlInfo + hrefTo
        } else if (routerStrs2.indexOf('http') === 0) {
          const token = JSON.parse(sessionStorage.getItem('token'))
          if (routerStrs2.indexOf('qdprmobile') !== -1 || routerStrs2.indexOf('zsta') !== -1) {
            window.location.href = _item.appUrl + '?token=' + token.replace(/bearer\s*/, '')
          } else {
            window.location.href = _item.appUrl
          }
        } else {
          var myParams2 = { title: _item.name }
          // console.log('routerStrs2===>', routerStrs2)
          // if (routerStrs2.indexOf('bookHome?A00002=1') === 0) {
          //   router.replace({ path: routerStrs2, query: myParams2 })
          // }
          if (_item.remarks) {
            const a = _item.remarks.split('&')
            a.forEach(element => {
              myParams2[element.split('=')[0]] = element.split('=')[1]
            })
          }
          router.push({ path: routerStrs2, query: myParams2 })
        }
      } else {
        Toast('请配置好H5路由')
      }
    }
    // 获取首页专题轮播图
    // const getSpecialTopic = async () => {
    //   const ret = await $api.news.getSpecialsubjectLists({
    //     pageNo: 1,
    //     pageSize: 100,
    //     module: 1,
    //     isPublish: 1
    //   })
    //   data.newsSpecialsubjects = []
    //   console.log('ret.data===>>', ret.data)
    //   if (ret.data && ret.data.length !== 0) {
    //     ret.data = ret.data.filter(function (item) {
    //       return item.title !== '我为红色胶东代言' && item.title !== '倾听与商量' && item.title !== '优秀提案背后的故事'
    //     })
    //     ret.data.forEach(element => {
    //       var image = element.image || {}
    //       element.url = element.coverImg || image.themeImg || ''// 图片
    //       element.class = element.infoClass || ''
    //       element.relateType = '32'
    //     })
    //     data.newsSpecialsubjects = data.newsSpecialsubjects.concat(ret.data)
    //   } else {
    //     data.newsSpecialsubjects = []
    //   }
    // }
    // 获取聚焦与关注
    // const getSpecialsubjectRelateinfoList = async () => {
    //   const ret = await $api.news.getSpecialsubjectRelateinfoList({
    //     pageNo: 1,
    //     pageSize: 8,
    //     subjectId: route.query.topic.split(',')[0],
    //     columnId: route.query.topic.split(',')[1],
    //     isPublish: 1
    //   })
    //   if (ret) {
    //     var list = ret.data || []
    //     data.dataList = []
    //     list.forEach(item => {
    //       item.type = item.infoType || ''
    //       item.class = item.infoClass || ''
    //       item.id = item.relateRecordId || item.id || ''
    //       item.title = item.title || ''
    //       var image = item.image || {}
    //       item.url = item.leftImage || image.fullUrl || ''
    //       item.source = item.source || ''
    //       item.createBy = item.createBy || ''
    //       item.isTop = item.isTop || '0'
    //       item.externalLinks = item.externalLinks || ''
    //       item.browerCount = item.browseCount || '0'
    //       item.commentCount = item.commentCount || '0'
    //       item.isRead = item.isRead ? '1' : '0'
    //       item.time = item.publishDate || item.createDate || ''
    //     })
    //     data.dataList = data.dataList.concat(list)
    //   }
    // }
    // 顶部轮播图和专题进详情
    const details = (row) => {
      console.log('row===========', row)
      if (row.externalLinks) {
        window.open(row.externalLinks)
      } else {
        // eslint-disable-next-line eqeqeq
        if (row.relateType == '32') {
          router.push({ name: 'newsZTList', query: { id: row.id, title: row.title } })
        } else {
          router.push({ name: 'newsDetails', query: { id: row.id, title: row.title, relateType: row.relateType } })
        }
      }
    }
    return { ...toRefs(data), $general, itemClick, details, carouselChange, dayjs, openScan, openDetails, getModuleRedPointNum }
  }
}
</script>
<style lang="less" scoped>
.module {
  .footer_item_hot {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #f92323;
    border-radius: 50%;
  }

  .footer_item_hot_big {
    position: absolute;
    top: 10px;
    right: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
  }

  // 顶部背景样式
  .bg_default {
    background-position: bottom;
    // background-size: cover;
    background-size: 100% 100%;
    height: 220px;

    .bg_img_text {
      position: absolute;
      top: 0.6rem;
      left: 0.5rem;
      display: flex;
      align-items: center;

      .van-image {
        width: 30px;
      }

      .bg_default_icon {
        margin-left: 5px;
        width: 100px;
      }
    }

    .scan {
      position: absolute;
      top: 0.6rem;
      left: 8.6rem;
      display: flex;
      align-items: center;
    }

    .carouselMap {
      width: 100%;
      height: auto;
      border-radius: 10px;
      // overflow: hidden;
      padding: 10px 14px;
      margin-top: 25%;

      .carousel_img {
        position: relative;
        height: 180px;
        background-size: cover;
        -webkit-background-size: cover;
        background-position: 50%;
        border-radius: 10px;
      }

      .carousel_elBox {
        width: 100%;
        height: 64px;
        padding: 5px 12px;
        position: absolute;
        border-radius: 10px;
        bottom: 0;
        background-image: -webkit-linear-gradient(rgba(0, 0, 0, 0),
            rgba(0, 0, 0, 0.39));
        background-image: -moz-linear-gradient(rgba(0, 0, 0, 0),
            rgba(0, 0, 0, 0.39));
        background-image: -o-linear-gradient(rgba(0, 0, 0, 0),
            rgba(0, 0, 0, 0.39));
        background-image: -webkit-gradient(linear,
            0 100%,
            0 0,
            from(rgba(0, 0, 0, 0)),
            to(rgba(0, 0, 0, 0.39)));
        background-image: linear-gradient(rgba(0, 0, 0, 0),
            rgba(0, 0, 0, 0.39));
      }

      .carousel_title {
        font-weight: bold;
        color: #fff;
        line-height: 1.3;
        margin-bottom: 14px;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
        margin: 20px;
      }

      ::v-deep .van-swipe__indicators {
        bottom: 5px;
      }

      ::v-deep.van-swipe__indicator {
        background-color: #fff;
        opacity: 1;
        width: 5px;
        height: 5px;
        border-radius: 5px;
      }

      ::v-deep.van-swipe__indicator--active {
        width: 14px;
        border-radius: 10px;
      }
    }
  }

  .apply_box {
    background: #fff;
    margin: 10px 0 0 0;
    border-radius: 5px;

    .main_box_item_header_text {
      font-weight: bold;
      color: #333333;
      font-weight: bold;
      margin-left: 5px;
      position: relative;
    }

    .apply_img_text {
      background: #fff;
      height: 100%;
      padding: 12px 10px;

      img {
        width: 18px;
      }
    }
  }

  .apply_box2 {
    background: #fff;
    border-radius: 5px;

    .main_box_item_header_text {
      font-weight: bold;
      color: #333333;
      font-weight: bold;
      margin-left: 5px;
      position: relative;
    }

    .apply_img_text {
      background: #fff;
      height: 100%;
      padding: 12px 10px;

      img {
        width: 18px;
      }
    }
  }

  .van-grid {
    background: #fff;
    // margin-top: 35px;
  }

  .van-grid-item__text {
    margin-top: 7px;
  }

  #app .van-grid-item__content {
    position: relative;
  }

  .van-grid.van-hairline--top::after {
    border-top-width: 0px;
  }

  .pendingMap {
    width: 100%;
    height: auto;
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0px 1px 8px 0px #e6e6e6;
  }

  .pendingMap .van-swipe__indicator {
    background: #f69731;
  }

  // 专题轮播图
  .main_box {
    z-index: 200;
    border-top: 8px solid #f4f4f4;
    background: #fff;
    padding-bottom: 10px;

    .main_box_item_header {
      padding: 12px 12px 0 12px;
    }

    .main_box_item_header_line {
      width: 4px;
      margin-right: 4px;
    }

    .main_box_item_header_text {
      font-weight: bold;
      color: #333;
    }

    .specialTopic_warp {
      width: 100%;
      height: auto;
      border-radius: 8px;
      overflow: hidden;
    }

    .specialTopic_img {
      width: 100%;
      height: 160px;
      object-fit: cover;
      margin-top: 3px;
      border-radius: 8px;
      background-size: 100% 100%;
    }
  }

  // 聚焦与关注
  .focus_item_box {
    z-index: 200;
    border-top: 10px solid #f4f4f4;
    background: #fff;
    padding-bottom: 80px;

    .focus_item_header {
      padding: 12px 12px 0 12px;

      .focus_item_header_line {
        width: 4px;
        margin-right: 4px;
      }

      .focus_item_header_text {
        font-weight: bold;
        color: #333;
      }
    }
  }
}
</style>
