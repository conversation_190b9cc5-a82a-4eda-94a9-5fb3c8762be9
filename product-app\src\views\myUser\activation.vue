<template>
  <div class="activation">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <van-tabs v-model:active="switchs.value"
              @change="onRefresh"
              :color=appTheme
              swipeable
              :title-active-color=appTheme
              :title-inactive-color=appTheme
              sticky>
      <van-tab v-for="item in switchs.data"
               :key="item.id"
               :name="item.value"
               :title="item.label">
        <template v-if="switchs.value ==='1'">
          <div style="height:50px;"></div>
          <div id="main1"
               style="width: 100%;height:300px;"
               :style="$general.loadConfiguration()"></div>
          <div style="height: 40px;"></div>
        </template>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索内容" />
        <!--数据列表-->
        <template v-if="switchs.value == '1'">
          <van-grid>
            <van-grid-item text="序号"
                           :style="$general.loadConfiguration()"></van-grid-item>
            <van-grid-item text="帐号"
                           :style="$general.loadConfiguration()"></van-grid-item>
            <van-grid-item text="登录次数"
                           :style="$general.loadConfiguration()"></van-grid-item>
            <van-grid-item text="排名"
                           :style="$general.loadConfiguration()"></van-grid-item>
          </van-grid>
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad"
                      :immediate-check="false">
              <van-grid v-for="(item,index) in listData"
                        :key="index"
                        :id="item.userId"
                        :class="user.id == item.userId?'myname':''">
                <van-grid-item text=""
                               :style="$general.loadConfiguration()">{{index+1}}</van-grid-item>
                <van-grid-item text=""
                               :style="$general.loadConfiguration()">{{item.userName}}</van-grid-item>
                <van-grid-item text=""
                               :style="$general.loadConfiguration()">{{item.amount}}</van-grid-item>
                <van-grid-item text=""
                               :style="$general.loadConfiguration()">{{item.sort}}</van-grid-item>
              </van-grid>
            </van-list>
          </van-pull-refresh>
        </template>
        <template v-if="switchs.value == '2'">
          <div v-if="rate"
               style="margin:10px 0;text-align: center;"
               :style="$general.loadConfiguration()">
            <van-circle v-model:current-rate="rateNum"
                        size="100"
                        :color="{'0%': '#3fecff','100%': '#6149f6'}"
                        stroke-width="100"
                        :rate="rateNum"
                        :speed="100"
                        :text="rate" />
            <van-cell class="myname"
                      title="安装率"
                      :value="rate"
                      :style="$general.loadConfiguration()">
            </van-cell>
            <van-grid :column-num="3">
              <van-grid-item text="序号"
                             :style="$general.loadConfiguration()"></van-grid-item>
              <van-grid-item text="帐号"
                             :style="$general.loadConfiguration()"></van-grid-item>
              <van-grid-item text="是否安装"
                             :style="$general.loadConfiguration()"></van-grid-item>
            </van-grid>
            <van-pull-refresh v-model="refreshing"
                              @refresh="onRefresh">
              <van-list v-model:loading="loading"
                        :finished="finished"
                        finished-text="没有更多了"
                        offset="52"
                        @load="onLoad"
                        :immediate-check="false">
                <van-grid v-for="(item,index) in installData"
                          :key="index"
                          :style="item.isJoinApp=='否'?'color:#f00;':''"
                          :column-num="3"
                          :id="item.userId"
                          :class="user.id == item.userId?'myname':''">
                  <van-grid-item text=""
                                 :style="$general.loadConfiguration()">{{index+1}}</van-grid-item>
                  <van-grid-item text=""
                                 :style="$general.loadConfiguration()">{{item.userName}}</van-grid-item>
                  <van-grid-item text=""
                                 :style="$general.loadConfiguration()">{{item.isJoinApp}}</van-grid-item>
                </van-grid>
              </van-list>
            </van-pull-refresh>
          </div>
        </template>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>

// import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import * as echarts from 'echarts'
import { Grid, GridItem, Circle } from 'vant'
export default {
  name: 'activation',
  components: {
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [Circle.name]: Circle
  },
  setup () {
    // const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      appTheme: $appTheme,
      user: JSON.parse(sessionStorage.getItem('user')),
      switchs: { value: '1', data: [{ label: '登录情况', value: '1' }, { label: '安装情况', value: '2' }] },
      isshowpic: false,
      names: [],
      pic_value: [],
      user_value: [],
      pageNo: 1,
      pageSize: 20,
      keyword: '',
      listData: [],
      installData: [],
      loading: false,
      finished: false,
      refreshing: false,
      rate: 0, // 安装率
      rateNum: 0, // 安装率
      twoText: null
    })
    onMounted(() => {
      onRefresh()
    })
    const onLoad = () => {
      if (data.twoText === '2') {
        data.pageNo = data.pageNo + 1
        installationSituation()
        return
      }
      data.pageNo = data.pageNo + 1
      loginStatus()
    }
    const onRefresh = (item) => {
      data.twoText = item
      if (data.twoText === '2') {
        data.pageNo = 1
        data.installData = []
        installationSituation()
        return
      }
      data.pageNo = 1
      data.listData = []
      data.keyword = ''
      getAppallogusers()
      loginStatus()
    }
    const search = async () => {
      if (data.twoText === '2') {
        data.pageNo = 1
        data.installData = []
        installationSituation()
        return
      }
      data.pageNo = 1
      data.listData = []
      loginStatus()
    }
    // 统计图数据
    const getAppallogusers = async () => {
      const res = await $api.general.appallogusers({
        days: 10
      })
      data.names = []
      data.pic_value = []
      data.user_value = []
      if (res) {
        var list = res.data || []
        if (list.length !== 0 && list !== null) {
          for (var i = 0; i < list.length; i++) {
            var json = json + i
            json = {}
            var LoginTime = list[i].day || ''
            var countsum = list[i].amount || 0
            var countUserSum = list[i].userAmount || 0
            var time = LoginTime
            data.names.push(time)
            data.pic_value.push(countsum)
            data.user_value.push(countUserSum)
          }
          show()
        }
      }
    }
    // 图表
    const show = () => {
      var myChart1 = echarts.init(document.getElementById('main1'))
      var option1 = {
        title: { text: '', subtext: '' },
        tooltip: { trigger: 'axis' },
        legend: { data: ['登录次数', '登录人数'] },
        toolbox: { show: true, feature: { dataView: { show: false, readOnly: true }, magicType: { show: false, type: ['line', 'bar'] }, mark: { show: false }, restore: { show: false }, saveAsImage: { show: false } } },
        calculable: true,
        xAxis: [{
          type: 'category',
          data: data.names,
          axisLabel: {
            interval: 0,
            rotate: 35 // 倾斜度 -90 至 90 默认为0
          }
        }],
        yAxis: [{ type: 'value', name: '数量' }],
        series: [{ name: '登录次数', type: 'bar', data: data.pic_value, barWidth: 14 }, { name: '登录人数', type: 'line', data: data.user_value }],
        grid: { x: 45 },
        color: ['rgb(255, 0, 0)', 'rgb(12, 120, 183)', '#32cd32', '#6495ed', '#ff69b4', '#ba55d3', '#cd5c5c', '#ffa500', '#40e0d0', '#1e90ff', '#ff6347', '#7b68ee', '#00fa9a', '#ffd700', '#6699FF', '#ff6666', '#3cb371', '#b8860b', '#30e0e0', '#FFFFFF']
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart1.setOption(option1)
    }
    // 登录情况表单
    const loginStatus = async () => {
      const res = await $api.general.applogusers({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      })
      console.log('登录情况表单.', res)
      var { data: list } = res
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
    }
    // 安装情况
    const installationSituation = async () => {
      const res = await $api.general.wholeuserJoinapp({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      })
      console.log('安装情况.', res)
      var { data: list } = res
      data.rate = list.rate
      var datalist = ''
      if (list.rate) {
        data.rateNum = data.rate.replace('%', '')
        datalist = list.users || []
      } else {
        datalist = list || []
      }
      var dataLength = datalist ? datalist.length : 0
      if (datalist && dataLength !== 0) {
        data.installData = data.installData.concat(datalist)
      }
      data.loading = false
      data.refreshing = false
    }
    return {
      ...toRefs(data), $general, onRefresh, search, onLoad
    }
  }
}
</script>
<style lang="less">
.myname {
  color: #08c;
}
</style>
