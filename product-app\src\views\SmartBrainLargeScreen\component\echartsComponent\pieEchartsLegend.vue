<template>
  <div class="pie-echarts-container">
    <div :id="chartId" class="pie-echarts"></div>
    <div class="pie-echarts-legend">
      <div v-for="(item, idx) in dataList" :key="idx" class="pie-echarts-legend-item">
        <span class="pie-echarts-legend-color" :style="{ background: item.color }"></span>
        <span class="pie-echarts-legend-label">{{ item.name }}</span>
        <span class="pie-echarts-legend-value">{{ item.value }}%</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'pieEchartsLegend',
  props: {
    id: {
      type: String,
      required: true
    },
    dataList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '类型分析'
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const renderChart = () => {
      if (!chartInstance) {
        chartInstance = echarts.init(document.getElementById(chartId.value))
      }
      const option = {
        title: {
          text: props.title,
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: { show: false },
        series: [
          {
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: { show: false },
            labelLine: { show: false },
            data: props.dataList.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: { color: item.color }
            }))
          }
        ]
      }
      chartInstance.setOption(option)
    }

    onMounted(() => {
      renderChart()
    })

    onBeforeUnmount(() => {
      chartInstance && chartInstance.dispose()
    })

    watch(() => props.dataList, () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      renderChart()
    }, { deep: true })

    return {
      chartId
    }
  }
})
</script>

<style scoped>
.pie-echarts-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pie-echarts {
  width: 200px;
  height: 200px;
}

.pie-echarts-legend {
  background: #fafbfc;
  border-radius: 10px;
  padding: 20px 15px;
  display: grid;
  grid-template-columns: 2fr 2fr;
  gap: 8px 0;
  width: 328px;
  box-sizing: border-box;
}

.pie-echarts-legend-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;
  min-width: 160px;
  flex: 1;
  margin-bottom: 0;
}

.pie-echarts-legend-color {
  width: 12px;
  height: 6px;
  margin-right: 10px;
  display: inline-block;
}

.pie-echarts-legend-label {
  color: #666666;
  font-size: 14px;
  margin-right: 10px;
}

.pie-echarts-legend-value {
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}
</style>
