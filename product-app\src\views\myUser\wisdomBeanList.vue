<template>
  <div class="wisdomBeanList">
    <img src="../../assets/img/zhihuidou/G_bg.png"
         alt=""
         style="width: 100%;height: 100%;">
    <div class="center_box">
      <div class="totalNum">{{scoreCount}}</div>
      <div class="detailed">智慧豆明细</div>
      <div class="plan_progress_wrap"
           :data-value="scorePercent1">
        <div class="plan_progress_container">
          <div class="plan_progress_bar"></div>
          <div class="text_value">{{scoreNum1}} <span></span></div>
        </div>
      </div>
    </div>
    <div class="Dailytasks">
      <div class="dailyTasks_value">日常任务</div>
      <div class="flex_box flex_align_center flex_justify_content box">
        <div>
          <div class="flex_box flex_align_center">
            <div class="box_textVal">每日首次登录</div>
            <img src="../../assets/img/zhihuidou/dou.png"
                 alt=""
                 style="width: 20px;height:20px;margin-left: 10px;"><span style="color: #E04501;">+1</span>
          </div>
          <div class="upperLimit_text">
            已获{{scoreNum2}}颗每日上限1颗</div>
          <div class="First_login_wrap"
               :data-value="scorePercent2">
            <div class="First_login_container">
              <div class="First_login_bar"></div>
            </div>
          </div>
        </div>
        <div class="flex_placeholder"></div>
        <div :style="$general.loadConfiguration(-2)"
             class="Completed">已完成</div>
      </div>
      <div class="flex_box flex_align_center flex_justify_content box">
        <div>
          <div class="flex_box flex_align_center">
            <div class="box_textVal">阅读文章</div>
            <img src="../../assets/img/zhihuidou/dou.png"
                 alt=""
                 style="width: 20px;height: 20px;margin-left: 10px;"><span style="color: #E04501;">+1</span>
          </div>
          <div class="upperLimit_text">阅读超过一分钟加一颗</div>
          <div class="upperLimit_text">
            已获{{scoreNum3}}颗/每日上限12颗</div>
          <div class="First_login_wrap"
               :data-value="scorePercent3">
            <div class="First_login_container">
              <div class="First_login_bar"></div>
            </div>
          </div>
        </div>
        <div class="flex_placeholder"></div>
        <div :style="$general.loadConfiguration(-2)"
             :class="scoreNum3==12?'Completed':'ToComplete'"
             @click="ReadingTo">
          {{scoreNum3==12?'已完成':'去完成'}}</div>
      </div>
      <div class="flex_box flex_align_center flex_justify_content box">
        <div>
          <div class="flex_box flex_align_center">
            <div class="box_textVal">发布有效评论</div>
            <img src="../../assets/img/zhihuidou/dou.png"
                 alt=""
                 style="width: 20px;height: 20px;margin-left: 10px;"><span style="color: #E04501;">+1</span>
          </div>
          <div class="upperLimit_text">
            已获{{scoreNum4}}颗/每日上限3颗</div>
          <div class="First_login_wrap"
               :data-value="scorePercent4">
            <div class="First_login_container">
              <div class="First_login_bar"></div>
            </div>
          </div>
        </div>
        <div class="flex_placeholder"></div>
        <div :style="$general.loadConfiguration(-2)"
             :class="scoreNum4==3?'Completed':'ToComplete'"
             @click="PostComments">{{scoreNum4==3?'已完成':'去完成'}}</div>
      </div>
      <!-- <div class="flex_box flex_align_center flex_justify_content box">
        <div>
          <div class="flex_box flex_align_center">
            <div class="box_textVal">转发文章</div>
            <img src="../../assets/img/zhihuidou/dou.png"
                 alt=""
                 style="width: 20px;height:20px;margin-left: 10px;"><span style="color: #E04501;">+1</span>
          </div>
          <div class="upperLimit_text">
            已获{{scoreNum5}}颗/每日上限6颗</div>
          <div class="First_login_wrap"
               :data-value="scorePercent5">
            <div class="First_login_container">
              <div class="First_login_bar"></div>
            </div>
          </div>
        </div>
        <div class="flex_placeholder"></div>
        <div :style="$general.loadConfiguration(-2)"
             :class="scoreNum5==6?'Completed':'ToComplete'"
             @click="ForwardArticle">{{scoreNum5==6?'已完成':'去完成'}}</div>
      </div> -->
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
export default {
  name: 'wisdomBeanList',
  components: {
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      scoreCount: 0,
      // 进度条动画计数器
      timers: [],
      scoreNum1: 0,
      scorePercent1: 0,
      scoreNum2: 0,
      scorePercent2: 0,
      scoreNum3: 0,
      scorePercent3: 0,
      scoreNum4: 0,
      scorePercent4: 0,
      scoreNum5: 0,
      scorePercent5: 0
    })
    onMounted(() => {
      getupperlimit()
      getEveryday()
      getReading()
      getPostComments()
      getForwardArticle()
      getNumber()
    })
    // 获取智慧豆
    const getNumber = async () => {
      const res = await $api.general.wisdombeanCount({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      setTimeout(() => {
        getProgressDomList()
      }, 100)
      data.scoreCount = res.data
    }
    // 获取进度条元素集
    const getProgressDomList = () => {
      // 相当于  this.$nextTick
      setTimeout(() => {
        // 进度条不为 0 的元素
        var progressList = $general.domAll('.plan_progress_wrap')
        progressList.forEach(item => {
          console.log('item===>', item.dataset)
          var progressBar = item.querySelector('.plan_progress_bar')
          var progressText = item.querySelector('.plan_progress_text')
          var value = parseInt(item.dataset.value)
          console.log('item.dataset.value===>', item.dataset.value)
          // 进度条初始化
          progressBar.style.width = '0%'
          // 进度动画
          var timer = setInterval(() => {
            setProcess(progressBar, progressText, value)
          }, 100)
          data.timers.push({
            $el: progressBar,
            timer
          })
        })
      }, 0)
      setTimeout(() => {
        // 进度条不为 0 的元素
        var progressList = $general.domAll('.First_login_wrap')
        progressList.forEach(item => {
          console.log('item===========', item)
          var progressBar = item.querySelector('.First_login_bar')
          var progressText = item.querySelector('.First_login_text')
          var value = parseInt(item.dataset.value)
          // 进度条初始化
          progressBar.style.width = '0%'
          // 进度动画
          var timer = setInterval(() => {
            setProcess(progressBar, progressText, value)
          }, 0)
          data.timers.push({
            $el: progressBar,
            timer
          })
        })
      }, 0)
    }
    // 进度条动画
    const setProcess = ($el, $text, value) => {
      var curTimer = data.timers.find(item => item.$el === $el).timer
      $el.style.width = parseInt($el.style.width) + 1 + '%'
      if (parseInt($el.style.width) === value) {
        clearInterval(curTimer)
      }
    }
    // 今日上限20进度条
    const getupperlimit = async () => {
      const ret = await $api.general.wisdombeanToday({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      data.scoreNum1 = ret.data > 20 ? 20 : ret.data
      data.scorePercent1 = ret.data > 20 ? 20 / 20 * 100 : ret.data / 20 * 100
    }
    // 每日首次登录
    const getEveryday = async () => {
      const ret = await $api.general.wisdombeanUserScore1({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      data.scoreNum2 = ret.data > 20 ? 20 : ret.data
      data.scorePercent2 = ret.data > 20 ? 20 / 20 * 100 : ret.data / 1 * 100
    }
    // 获取阅读文章
    const getReading = async () => {
      const ret = await $api.general.wisdombeanUserScore2({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      if (ret.data === 0) {
        data.timers = []
        data.scoreNum3 = ret.data
        data.scorePercent3 = 1
      } else {
        data.scoreNum3 = ret.data
        data.scorePercent3 = ret.data / 12 * 100
      }
    }
    // 发布有效评论
    const getPostComments = async () => {
      const ret = await $api.general.wisdombeanUserScore3({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      if (ret.data === 0) {
        data.timers = []
        data.scoreNum4 = ret.data
        data.scorePercent4 = 1
      } else {
        data.scoreNum4 = ret.data
        data.scorePercent4 = ret.data / 3 * 100
      }
    }
    // 转发文章
    const getForwardArticle = async () => {
      const ret = await $api.general.wisdombeanUserScore4({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      if (ret.data === 0) {
        data.timers = []
        data.scoreNum5 = ret.data
        data.scorePercent5 = 1
      } else {
        data.scoreNum5 = ret.data
        data.scorePercent5 = ret.data / 6 * 100
      }
    }
    // 跳转阅读文章
    const ReadingTo = () => {
      router.push({ name: 'bookHome' })
    }
    // 跳转发布评论
    const PostComments = () => {
      router.push({ name: 'NetworkpoliticsList' })
    }
    // 跳转转发文章
    // const ForwardArticle = () => {
    //   router.push({ name: 'newsList', query: { module: 1 } })
    // }
    return { ...toRefs(data), $general, ReadingTo, PostComments }
  }
}
</script>
<style lang="less">
.wisdomBeanList {
  width: 100%;
  height: 100%;
  .center_box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 8%;
    left: 0;
    width: 100%;
    .totalNum {
      color: #ffffff;
      font-size: 48px;
    }

    .detailed {
      color: #ffffff;
      font-size: 13px;
    }
    .plan_progress_wrap {
      display: flex;
      align-items: center;
      margin-top: 10px;
      .plan_progress_container {
        position: relative;
        height: 8px;
        border-radius: 25px;
        background: #ffa95f;
        .plan_progress_bar {
          position: absolute;
          left: 0;
          top: 0;
          width: 50px;
          height: 100%;
          border-radius: 25px;
          background-image: linear-gradient(to right, #ffffff, #ffffff);
        }
        .text_value {
          font-size: 10px;
          margin-top: 12px;
          color: #fff;
          span {
            font-size: 12px;
            margin-left: 116px;
          }
        }
      }
    }
  }
  .Dailytasks {
    background: #fff;
    border-radius: 8px;
    margin: 32px 12px;
    padding: 18px;
    position: relative;
    bottom: 155px;
    .dailyTasks_value {
      color: #333333;
      font-weight: bold;
      font-size: 19px;
    }
    .box {
      margin-top: 24px;
      .box_textVal {
        color: #333333;
        font-size: 16px;
      }
      .upperLimit_text {
        color: #bbbbbb;
        margin-top: 5px;
        font-size: 12px;
      }
      .First_login_wrap {
        display: flex;
        align-items: center;
        margin-top: 4px;
        width: 64px;
        .First_login_container {
          position: relative;
          width: 100%;
          height: 6px;
          border-radius: 25px;
          background: #eceff3;
          .First_login_bar {
            position: absolute;
            left: 0;
            top: 0;
            width: 0%;
            height: 100%;
            border-radius: 25px;
            background-image: linear-gradient(to right, #ff3637, #ff3637);
          }
        }
      }
      .Completed {
        background: #f4f4f4;
        padding: 3px 10px;
        border-radius: 18px;
        color: #999999;
      }
      .ToComplete {
        background: #ffecec;
        padding: 3px 10px;
        border-radius: 18px;
        color: #ff3637;
      }
    }
  }
}
</style>
