<template>
  <div class="meetingPersonnel">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      success-text="刷新成功">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div style="margin: 20px;justify-content: space-between;"
             v-for="(item,index) in listData"
             :key="index">
          <div class="flex_box Personnel_box">
            <img :src="item.headImg"
                 alt="">
            <div>
              <div class="flex_box Personnel_list">
                <div class="Personnel_list_one">
                  <div :style="$general.loadConfiguration()"
                       class="Personnel_list_name">{{item.userName}}
                  </div>
                  <div :style="$general.loadConfiguration(-3)"
                       class="Personnel_list_group">{{item.group}}</div>
                </div>
                <div class="Personnel_list_two">
                  <div :style="$general.loadConfiguration(-3)"
                       class="Personnel_list_dele">{{item.dele}}</div>
                  <div :style="$general.loadConfiguration(-3)"
                       class="Personnel_list_phone">{{item.phone}}</div>
                </div>
              </div>
              <div :style="$general.loadConfiguration(-2)"
                   class="Personnel_list_position">{{item.position}}</div>
            </div>
          </div>
          <div style="border-bottom: 1px solid #EBEBEB;margin-bottom: 15px;"></div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog } from 'vant'
export default {
  name: 'meetingPersonnel',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appFontSize: 16,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      loading: false,
      finished: false,
      refreshing: false,
      listData: []
    })
    onMounted(() => {
      getListData()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getListData()
    }
    // 获取人员列表
    const getListData = async () => {
      const res = await $api.conferenceActivitiesFile.getAttendanceListVos({
        pageNo: 1,
        pageSize: 99,
        conferenceId: data.id,
        code: 1
      })
      var { data: list, total } = res
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, $general, onRefresh }
  }
}
</script>
<style lang="less" scoped>
.meetingPersonnel {
  background: #fff;
  .Personnel_box {
    margin-bottom: 15px;
  }
  .Personnel_box img {
    width: 62px;
    height: 70px;
  }

  .Personnel_list {
    justify-content: space-between;
    margin-top: 2px;
  }

  .Personnel_list_one {
    margin-left: 10px;
  }
  .Personnel_list_name {
    color: #333333;
    font-weight: 500;
  }
  .Personnel_list_group {
    color: #999999;
    font-weight: 500;
    margin-top: 5px;
  }

  .Personnel_list_two {
    position: absolute;
    right: 20px;
  }
  .Personnel_list_dele {
    color: #999999;
    font-weight: 500;
  }
  .Personnel_list_phone {
    color: #999999;
    font-weight: 500;
    margin-top: 5px;
  }
  .Personnel_list_position {
    color: #999999;
    font-weight: 500;
    margin-left: 10px;
    margin-top: 5px;
  }
}
</style>
