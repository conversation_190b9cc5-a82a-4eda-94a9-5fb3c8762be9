<template>
  <div class="homePageZX">
    <div class="homePageZXHead">
      <div class="portrait">
        <img :src="user.headImg"
             alt="">
      </div>
      <van-search v-model="keyword"
                  placeholder="请输入搜索关键词" />
      <div class="portrait"></div>
    </div>
    <div class="homePageZXTab scrollBar">
      <div class="homePageZXTabItem homePageZXTabItemA">测试1</div>
      <div class="homePageZXTabItem">测试2</div>
      <div class="homePageZXTabItem">测试3</div>
      <div class="homePageZXTabItem">测试4</div>
      <div class="homePageZXTabItem">测试5</div>
      <div class="homePageZXTabItem">测试6</div>
      <div class="homePageZXTabItem">测试7</div>
      <div class="homePageZXTabItem">测试8</div>
      <div class="homePageZXTabItem">测试9</div>
      <div class="homePageZXTabItem">测试10</div>
      <div class="homePageZXTabItem">测试11</div>
    </div>
    <van-swipe class="homePageZXSwipe"
               :autoplay="3000">
      <van-swipe-item v-for="item in swipeList"
                      :key="item.id">
        <div class="homePageZXSwipeBox">
          <div class="homePageZXSwipeImg">
            <img :src="item.url"
                 alt="">
          </div>
          <div class="homePageZXSwipeTextBox">
            <div class="headlinesIcon"></div>
            <div class="homePageZXSwipeText ellipsisTwo">习近平总书记在湖北省考察新冠肺炎疫 情防控工作时的讲话。</div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
    <div class="informationBox">
      <div class="informationItem">
        <div class="title ellipsisTwo">十三届全国人大常委会举行第九十六次委员长会议，决定将海南自有贸易港法草案、反港法草案港法草案</div>
        <div class="content ellipsis">新华社区北京6月10日电十三届全国人大常委会第九反港法草案港法草案</div>
        <div class="otherBox">
          <div class="other"><span>2020-02-06</span> <span>新华网</span></div>
          <div></div>
        </div>
      </div>
      <div class="informationItem">
        <div class="title ellipsisTwo">十三届全国人大常委会举行第九十六次委员长会议，决定将海南自有贸易港法草案、反港法草案港法草案</div>
        <div class="content ellipsis">新华社区北京6月10日电十三届全国人大常委会第九反港法草案港法草案</div>
        <div class="otherBox">
          <div class="other"><span>2020-02-06</span> <span>新华网</span></div>
          <div></div>
        </div>
      </div>
    </div>
    <div class="informationBox">
      <div class="informationItem">
        <div class="title ellipsisTwo">十三届全国人大常委会举行第九十六次委员长会议，决定将海南自有贸易港法草案、反港法草案港法草案</div>
        <div class="content ellipsis">新华社区北京6月10日电十三届全国人大常委会第九反港法草案港法草案</div>
        <div class="otherBox">
          <div class="other"><span>2020-02-06</span> <span>新华网</span></div>
          <div></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { useRouter, useRoute } from 'vue-router'
import { onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'homePageZX',
  setup () {
    // const route = useRoute()
    // const router = useRouter()
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      swipeList: [
        { id: '1', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20678832/images/c226d7fb6495461b9ef47acf24c3a4c5.jpg' },
        { id: '2', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20661109/images/d0ca25e53deb498d96b2cefd5f6633eb.jpg' },
        { id: '3', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20539278/images/f5ac427b124e4f7b8e0128a495df19ee.jpg' },
        { id: '4', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20680279/images/285de3b71f1d47a29c8a9c67a5b4843e.jpg' },
        { id: '5', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20678646/images/eb21ba319a9a4b768cb865302d3bc0fb.png' },
        { id: '6', url: 'https://www.hunanzx.gov.cn/hnzx/wzsyszx/jdxw/202109/20680279/images/285de3b71f1d47a29c8a9c67a5b4843e.jpg' }
      ]
    })
    onMounted(() => {
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less">
.homePageZX {
  width: 100%;
  padding-top: 44px;
  padding-bottom: 50px;
  background-color: #eee;
  .homePageZXHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3088fe;
    .portrait {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
      }
    }
    .van-search {
      padding: 0;
      width: 267px;
      margin: 0 12px;
      background-color: rgba(255, 255, 255, 0.3);
      .van-cell {
        padding: 3px 0;
      }
      .van-icon {
        color: #fff;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        background-color: rgba(255, 255, 255, 0.3);
        .van-field__body {
          font-size: 14px;
          .van-field__control {
            color: #fff;
            &::-webkit-input-placeholder {
              color: #fff;
            }
          }
        }
      }
    }
  }
  .homePageZXTab {
    width: 100%;
    height: 44px;
    overflow-y: hidden;
    overflow-x: scroll;
    white-space: nowrap;
    background-color: #fff;
    .homePageZXTabItem {
      height: 44px;
      line-height: 44px;
      margin: 0 20px;
      display: inline-block;
      position: relative;
      &::after {
        content: "";
        width: 100%;
        height: 3px;
        background: transparent;
        position: absolute;
        bottom: 6px;
        left: 0;
      }
    }
    .homePageZXTabItem + .homePageZXTabItem {
      margin-left: 0;
    }
    .homePageZXTabItemA {
      font-weight: 600;
      &::after {
        background: #3088fe;
      }
    }
  }
  .homePageZXSwipe {
    width: 100%;
    .homePageZXSwipeBox {
      width: 375px;
      height: 262px;
      position: relative;
      background-color: #fff;
      .homePageZXSwipeImg {
        width: 100%;
        height: 210px;
        background-color: #39a9ed;
        overflow: hidden;
        img {
          width: 100%;
        }
      }
      .homePageZXSwipeTextBox {
        width: 343px;
        height: 58px;
        background-color: #ccc;
        position: absolute;
        top: 210px;
        left: 50%;
        transform: translate(-50%, -60%);
        background: #ffffff;
        box-shadow: 0px 2px 25px rgba(24, 64, 118, 0.1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        .headlinesIcon {
          width: 20px;
          height: 35px;
          background: url("../../assets/img/headlinesIcon.png") no-repeat;
          background-size: 100% 100%;
        }
        .homePageZXSwipeText {
          width: 299px;
          height: 100%;
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
        }
      }
    }
    .van-swipe__indicator {
      width: 24px;
      height: 4px;
      border-radius: 0;
      color: #eee;
      opacity: 1;
    }
  }
  .informationBox {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
    .informationItem {
      width: 100%;
      padding: 10px 16px;
      .title {
        line-height: 20px;
        color: #333333;
        margin-bottom: 10px;
        font-family: PingFang SC;
      }
      .content {
        font-size: 14px;
        line-height: 20px;
        color: #666666;
        margin-bottom: 5px;
      }
      .otherBox {
        width: 100%;
        display: flex;
        align-items: center;
        .other {
          span {
            font-size: 12px;
            line-height: 17px;
            color: #999999;
          }
          span + span {
            margin-left: 26px;
          }
        }
      }
    }
  }
}
</style>
