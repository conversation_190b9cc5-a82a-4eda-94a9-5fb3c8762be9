const superFile = () => import('@/components/superFile/superFile')
const modules = () => import('@/views/module/module.vue')
const moduleMorePage = () => import('@/views/module/moduleMorePage.vue')
const myUser = () => import('@/views/myUser/myUser.vue')
const personalDataView = () => import('@/views/myUser/personalDataView.vue') // 个人资料页面
const myCollection = () => import('@/views/myUser/myCollection.vue') // 我的收藏
const activation = () => import('@/views/myUser/activation.vue') // 活跃度
const helpManual = () => import('@/views/myUser/help/helpManual.vue') // 帮助手册
const helpManualDetails = () => import('@/views/myUser/help/helpManualDetails.vue') // 帮助手册
const wisdomBeanList = () => import('@/views/myUser/wisdomBeanList.vue') // 智慧豆
const scan = () => import('@/views/module/scan.vue') // 扫一扫
const userFeedback = () => import('@/views/userFeedback/userFeedback.vue')
const userFeedbackAdd = () => import('@/views/userFeedback/userFeedbackAdd.vue')
const news = [{
  path: '/superFile',
  name: 'superFile',
  component: superFile,
  meta: {
    title: '文件',
    keepAlive: true
  }
}, {
  path: '/module',
  name: 'module',
  component: modules,
  meta: {
    title: '模块',
    keepAlive: true
  }
}, {
  path: '/moduleMorePage',
  name: 'moduleMorePage',
  component: moduleMorePage,
  meta: {
    title: '更多模块页面',
    keepAlive: true
  }
}, {
  path: '/myUser',
  name: 'myUser',
  component: myUser,
  meta: {
    title: '我的',
    keepAlive: true
  }
}, {
  path: '/personalDataView',
  name: 'personalDataView',
  component: personalDataView,
  meta: {
    title: '个人资料',
    keepAlive: true
  }
}, {
  path: '/myCollection',
  name: 'myCollection',
  component: myCollection,
  meta: {
    title: '我的收藏',
    keepAlive: true
  }
}, {
  path: '/activation',
  name: 'activation',
  component: activation,
  meta: {
    title: '活跃度',
    keepAlive: true
  }
}, {
  path: '/helpManual',
  name: 'helpManual',
  component: helpManual,
  meta: {
    title: '帮助手册列表',
    keepAlive: true
  }
}, {
  path: '/helpManualDetails',
  name: 'helpManualDetails',
  component: helpManualDetails,
  meta: {
    title: '帮助手册详情',
    keepAlive: true
  }
}, {
  path: '/wisdomBeanList',
  name: 'wisdomBeanList',
  component: wisdomBeanList,
  meta: {
    title: '智慧豆',
    keepAlive: true
  }
}, {
  path: '/scan',
  name: 'scan',
  component: scan,
  meta: {
    title: '扫一扫',
    keepAlive: true
  }
}, {
  path: '/userFeedback',
  name: 'userFeedback',
  component: userFeedback,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/userFeedbackAdd',
  name: 'userFeedbackAdd',
  component: userFeedbackAdd,
  meta: {
    title: '',
    keepAlive: true
  }
}]
export default news
