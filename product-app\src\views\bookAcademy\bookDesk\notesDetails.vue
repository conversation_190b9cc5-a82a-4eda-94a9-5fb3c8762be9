<template>
  <div class="bookNotice">
    <!--搜索-->
    <van-sticky>
      <div class="btnLeft_box flex_box"
           v-if="isShowHead">
        <div class="btnLeft_box flex_box">
          <div @click="onClickLeft()"
               class="min_header_close flex_box flex_align_center">
            <div class="min_header_close_item flex_box flex_align_center flex_justify_content">
              <van-icon name="arrow-left"></van-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="flex_placeholder flex_box flex_justify_content"
           style="background:#fff;">
        <ul class="header_box flex_box flex_align_center">
          <div v-for="(item,index) in switchs.data"
               :key="index"
               @click="switchHeader(index)"
               style="padding: 8px 10px;">
            <li :style="(switchs.value==index?'font-size:18px;':'font-size:14px;')"
                class="header_item text_one2"
                :class="switchs.value==index?'active':''">{{item.label}}</li>
          </div>
        </ul>
      </div>
      <div id="search"
           class="search_box">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                   :placeholder="'搜索'"
                   maxlength="100"
                   type="search"
                   @keyup.enter="btnSearch"
                   v-model="keyword" /></form>
        </div>
      </div>
    </van-sticky>

    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul v-if="dataList.length != 0">
          <van-swipe-cell v-for="(item,index) in dataList"
                          :key="index"
                          class="van-hairline--bottom">
            <li class="item_body_warp">
              <div class="item_body ">
                <div class="flex_box">
                  <img @click="openPersonalDataDetails({id:item.userId})"
                       :style="'width:40px;height:40px;'+'object-fit: contain;border-radius:50%;'"
                       :src="item.url" />
                  <div class="flex_placeholder itemNotes_item_box">
                    <div class="flex_box flex_align_center">
                      <div class="flex_placeholder text_one2"
                           :style="'font-size:14px;'+'color:'+appTheme"
                           v-html="item.name"></div>
                      <div v-if="isMine"
                           class="flex_box flex_align_center">
                        <div class="flex_placeholder"></div>
                        <div class="text_one2"
                             :style="'font-size:14px;'+'color:#101010;lline-height: 1.2;margin-right:5px;'"
                             v-html="'公开'"></div>
                        <van-switch @change="setPublic(item)"
                                    v-if="isMine"
                                    :active-color="appTheme"
                                    v-model="item.isPrivacy"
                                    :size="18"></van-switch>
                      </div>
                      <div v-else
                           @click="clickLickBtn(item)"
                           class="flex_box flex_align_end"
                           style="padding: 5px 0;">
                        <van-icon :size="18"
                                  :color="item.islike?appTheme:'#999'"
                                  name="good-job"></van-icon>
                        <span :style="'font-size:12px;'+'color:'+(item.islike?appTheme:'#999')">{{item.likeNum}}</span>
                      </div>
                    </div>
                    <div class="flex_box flex_align_center">
                      <div class="flex_placeholder text_one2"
                           :style="'font-size:12px;'+'color:#A5A5A5;'"
                           v-html="item.time"></div>
                      <div v-if="isMine"
                           @click="clickLickBtn(item)"
                           class="flex_box flex_align_end"
                           style="padding: 5px 0;">
                        <van-icon :size="18"
                                  :color="item.islike?appTheme:'#999'"
                                  name="good-job"></van-icon>
                        <span :style="'font-size:12px;'+'color:'+(item.islike?appTheme:'#999')">{{item.likeNum}}</span>
                      </div>
                    </div>
                    <div class="flex_box">
                      <div class="itemWords_content_hint"
                           :style="'color:#A5A5A5;'">“</div>
                      <div @click="showNoteDetails(item)"
                           class="itemNotes_content"
                           :style="'font-size:12px;'"
                           v-html="item.showAll?item.allContent:item.content"></div>
                    </div>
                  </div>
                </div>
                <div class="flex_box flex_align_center">
                  <div :style="'font-size:15px;'+'color:#5E646D;font-weight: bold;'"
                       v-html="'引用'"></div>
                  <div style="width: 1px;height: 19px;margin: 0 14px;"
                       :style="'background:'+appTheme"></div>
                  <div @click="openBookDetails(item)"
                       class="flex_placeholder"
                       :style="'font-size:12px;'+'color:#A5A5A5;'"
                       v-html="item.bookName+'——'+item.author"></div>
                  <img @click="previewImg(item)"
                       :style="'width:34px;height:34px;'+'object-fit: contain;'"
                       :src="item.img" />
                </div>
              </div>
            </li>
            <!--不能换行 会有多的空间-->
            <template v-if="isMine"
                      v-slot:right>
              <div :style="'height:100%;padding: 9px 0;'">
                <van-button style="border-radius:14px 0 0 14px;"
                            color="#404040"
                            @click="editNote(item)"
                            square
                            type="warning"
                            text="编辑"></van-button>
                <van-button @click="delNote(item)"
                            color="#fa5051"
                            square
                            type="danger"
                            text="删除"></van-button>
              </div>
            </template>
          </van-swipe-cell>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Sticky, Circle, Progress, ImagePreview } from 'vant'
// import moment from 'moment'
export default {
  name: 'bookNotice',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [Sticky.name]: Sticky,
    [Circle.name]: Circle,
    [Progress.name]: Progress,
    [Dialog.Component.name]: Dialog.Component,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      switchs: {
        value: 0,
        data: [
          { label: '所有', type: 'notes_all' },
          { label: '我的', type: 'notes_my' }
        ]
      },
      isMine: false // 是否查询个人笔记
    })
    watch(() => data.listSelect, (newName, oldName) => {
      console.log(data.listSelect)
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      onRefresh()
    })
    if (data.title) {
      document.title = data.title
    }
    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    // 切换头部监听
    const switchHeader = (_index) => {
      data.switchs.value = _index
      if (_index === 0) {
        data.isMine = false
      } else {
        data.isMine = true
      }
      onRefresh()
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        bookId: data.id
      }
      if (data.isMine) {
        datas.isMine = 1
      }
      const { data: list, total } = await $api.bookAcademy.getReadingNotesList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.screenshotUrl || '' } }
        item.img = _eItem.screenshotUrl || ''
        item.url = ''
        item.noteId = _eItem.id || ''
        item.id = _eItem.bookId || ''

        getCoverImg(item)// 截图功能没有做好前先用图书封面显示

        item.url = _eItem.headImg || ''
        item.name = _eItem.createBy || ''
        item.userId = _eItem.userId || ''
        item.time = _eItem.createDate || ''
        item.islike = _eItem.hasClick
        item.likeNum = _eItem.likenum
        item.content = _eItem.noteContent || ''
        item.allContent = ''
        item.showAll = false
        item.bookName = _eItem.bookName || ''
        item.author = _eItem.authorName || ''
        // eslint-disable-next-line eqeqeq
        item.isPrivacy = _eItem.isPrivacy == 1
        newData.push(item)
      })
      setTimeout(() => {
        data.dataList = data.dataList.concat(newData)
      }, 500)
      console.log(data.dataList)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const getCoverImg = async (item) => {
      var { data: info } = await $api.bookAcademy.getBookInfo({ id: item.id })
      item.img = info.coverImgUrl || ''
    }
    const showNoteDetails = async (_item, _type) => {
      var ret = await $api.bookAcademy.syReadingNotesInfo({ id: _item.noteId })
      if (ret && ret.data) {
        _item.allContent = ret.data.noteContent
      }
      if (!_type) {
        _item.showAll = ret && ret.data
      } else {
        editNote(_item, 'ok')
      }
    }
    const editNote = (_item) => {
      router.push({ name: 'notesEdit', query: { id: _item.id, noteId: _item.noteId } })
    }
    const delNote = async (_item) => {
      const res = await $api.bookAcademy.delsReadingNotes({ ids: _item.noteId })
      if (res) {
        // eslint-disable-next-line eqeqeq
        Toast('删除' + (res.errcode == 200 ? '成功' : '失败'))
        $general.delItemForKey(_item.noteId, data.dataList, 'noteId')
        if (data.dataList.length === 0) {
          onRefresh()
        }
      }
    }
    const openBookNotes = (row) => {
      console.log(row)
      router.push({ name: 'notesDetails', query: { id: row.id } })
    }
    // 点击 书本
    const clickBook = (_item, _index) => {
      if (data.isSetting) {
        return
      }
      openBookDetails(_item)
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    // 设置是否公开
    const previewImg = async (_item) => {
      ImagePreview({
        images: [_item.img],
        closeable: true
      })
    }
    // 设置是否公开
    const setPublic = async (_item) => {
      _item.islike = !_item.islike
      var datas = {
        ids: _item.noteId
      }
      const res = await $api.bookAcademy.setPublic(datas, _item.isPrivacy)
      if (res) {
        Toast('设置' + (_item.isPrivacy ? '公开' : '不公开') + (res.errcode === 200 ? '成功' : '失败'))
      }
    }
    // 点赞 取消点赞
    const clickLickBtn = async (_item, _type) => {
      _item.islike = !_item.islike
      var datas = {}
      if (_item.islike) {
        _item.likeNum++
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.addClick(datas, _type)
      } else {
        _item.likeNum--
        datas = {
          businessId: _item.noteId
        }
        await $api.bookAcademy.cansalClick(datas, _type)
      }
    }
    const openPersonalDataDetails = (_item) => {
      router.push({ name: 'personData', query: { id: _item.id } })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onLoad, switchHeader, onClickLeft, delNote, clickLickBtn, setPublic, previewImg, editNote, showNoteDetails, openBookNotes, btnSearch, goBookReader, openBookDetails, clickBook, openPersonalDataDetails }
  }
}
</script>
<style lang="less" scoped>
.bookNotice {
  width: 100%;
  background: #f9f9f9;
  position: relative;
  .itemNotes_item_box {
    padding: 0 0 15px 10px;
  }
  .itemNotes_content {
    color: #5e646d;
    padding: 10px 0 0 3px;
  }
  .itemWords_content_hint {
    font-size: 30px;
    font-family: Helvetica;
  }
  .header_box {
    padding: 0 5px;
  }
  .header_item {
    color: #a5a5a5;
    max-width: 250px;
  }
  .header_item.active {
    font-weight: bold;
    color: #101010;
  }
  .max_header {
    position: relative;
  }
  .btnLeft_box {
    position: absolute;
  }
  .min_header_close_item {
    padding: 10px 10px;
  }
}
</style>
