import {
  HTTP
} from '../http.js'
class bookAcademy extends HTTP {
  // 编辑笔记
  editReadingNotes (params) {
    return this.request({
      url: '/syReadingNotes/edit',
      data: params
    })
  }

  // 删除笔记
  delsReadingNotes (params) {
    return this.request({
      url: '/syReadingNotes/dels',
      data: params
    })
  }

  // 获取笔记详细列表
  getReadingNotesList (params) {
    return this.request({
      url: '/syReadingNotes/list',
      data: params
    })
  }

  // 获取笔记列表
  getNotesList (params) {
    return this.request({
      url: '/syMyBook/notes',
      data: params
    })
  }

  // 删除笔记
  clearNotes (params) {
    return this.request({
      url: '/syReadingNotes/clearnotes',
      data: params
    })
  }

  // 修改背景图
  editmainpage (params) {
    return this.request({
      url: '/library/user/editmainpage',
      data: params
    })
  }

  // 新增推荐
  addCommissar (params) {
    return this.request({
      url: '/syReadingNotes/addRecommend',
      data: params
    })
  }

  // 委员推荐列表
  getCommissarRecommendlList (params) {
    return this.request({
      url: '/syReadingNotes/recommendList',
      data: params
    })
  }

  // 推荐列表
  recommendlList (params) {
    return this.request({
      url: '/recommendsort/contents',
      data: params
    })
  }

  // 检查是否在书架
  existBook (params) {
    return this.request({
      url: '/syMyBook/existBook',
      data: params
    })
  }

  // 获取所有分类
  getALlTypeList (params) {
    return this.request({
      url: '/syMyType/list',
      data: params
    })
  }

  // 点击添加到分类里
  addBookToType (params) {
    return this.request({
      url: '/syMyBook/add',
      data: params
    })
  }

  // 删除分类
  delType (params) {
    return this.request({
      url: '/syMyType/del',
      data: params
    })
  }

  // 新增分类
  addType (params) {
    return this.request({
      url: '/syMyType/add',
      data: params
    })
  }

  // 修改分类
  editType (params) {
    return this.request({
      url: '/syMyType/edit',
      data: params
    })
  }

  // 点击 展示 笔记
  syReadingNotesInfo (params) {
    return this.request({
      url: '/syReadingNotes/info',
      data: params
    })
  }

  // 书院增加统计
  addCounter (params) {
    return this.request({
      url: '/counter/add/count_0030',
      data: params
    })
  }

  // 书院增加统计
  addCounter0010 (params) {
    return this.request({
      url: '/counter/add/count_0010',
      data: params
    })
  }

  // 编辑群组书籍
  editGroupBook (params) {
    return this.request({
      url: '/syBook/talkgroup/join',
      data: params
    })
  }

  // 获取书籍列表
  getBookList (params) {
    return this.request({
      url: '/syBook/list',
      data: params
    })
  }

  // 获取书库分类
  getTypefourList (params) {
    return this.request({
      url: '/syBook/list/typefour?',
      data: params,
      method: 'GET'
    })
  }

  // 获取滚动推荐
  getRollBookList (params) {
    return this.request({
      url: '/syRollBook/list',
      data: params
    })
  }

  // 获取分类详情
  getTypeList (params) {
    return this.request({
      url: '/syType/info',
      data: params
    })
  }

  // 获取二级分类详情
  getSyTypeTree (params) {
    return this.request({
      url: '/syType/getSyTypeTree',
      data: params
    })
  }

  // 获取书籍详情
  getBookInfo (params) {
    return this.request({
      url: '/syBook/info',
      data: params
    })
  }

  // 获取书籍阅读详情
  getReadusers (params) {
    return this.request({
      url: '/syBook/readusers',
      data: params
    })
  }

  // 点赞
  addClick (params, _type) {
    return this.request({
      url: '/feeling/click/add/' + (_type || 'library_book_note') + '?businessId=' + params.businessId,
      data: params,
      method: 'GET'
    })
  }

  // 设置是否公开
  setPublic (params, _type) {
    return this.request({
      url: '/syReadingNotes/' + (_type ? 'delsOpen' : 'delsPrivacy'),
      data: params
    })
  }

  // 取消点赞
  cansalClick (params, _type) {
    return this.request({
      url: '/feeling/click/cansal/' + (_type || 'library_book_note') + '?businessId=' + params.businessId,
      data: params,
      method: 'GET'
    })
  }

  // 评论新增
  addComment (params) {
    return this.request({
      url: '/feeling/comment/add/library_book',
      data: params
    })
  }

  // 删除评论
  delComment (params) {
    return this.request({
      url: '/feeling/comment/del/library_book',
      data: params
    })
  }

  // 获取评论列表
  getCommentList (params) {
    return this.request({
      url: '/feeling/comment/find/library_book',
      data: params
    })
  }

  // 把书箱从书库中删除
  delByBookIds (params) {
    return this.request({
      url: '/syMyBook/delByBookIds',
      data: params
    })
  }

  // 保存进度
  updateStatus (params) {
    return this.request({
      url: '/syMyBook/updateStatus',
      data: params
    })
  }

  // 拿云端书架记录
  readbookinfo (params) {
    return this.request({
      url: '/syMyBook/readbookinfo',
      data: params
    })
  }

  // 获取通知列表
  getNoticeList (params) {
    return this.request({
      url: '/syNotice/list',
      data: params
    })
  }

  // 获取通知详情
  getNoticeDetails (params) {
    return this.request({
      url: '/syNotice/info?id=' + params.id,
      data: params,
      method: 'GET'
    })
  }

  // 获取通知红点
  getHasnew (params) {
    return this.request({
      url: '/syNotice/hasnew',
      data: params
    })
  }

  // 清除通知
  clearFlushnew (params) {
    return this.request({
      url: '/syNotice/flushnew',
      data: params
    })
  }

  // 获取我的书籍列表
  getMyBookList (params) {
    return this.request({
      url: '/syMyBook/list',
      data: params
    })
  }

  // 书籍类型更新
  updateType (params) {
    return this.request({
      url: '/syMyBook/updateType',
      data: params
    })
  }

  // 删除类型中的书籍
  delsTypeMyBook (params) {
    return this.request({
      url: '/syMyBook/dels',
      data: params
    })
  }

  // 拿云端书架记录
  getReadPosition (params) {
    return this.request({
      url: 'https://s.zhangyue.com/getReadPosition?bid=' + params.id + '&' + params.addUrl + params.signUrl,
      data: params,
      method: 'GET'
    })
  }
}
export {
  bookAcademy
}
