import { HTTP } from '../http.js'
class notice extends HTTP {
  general (url, params) {
    return this.request({ url: url, data: params })
  }

  // 通知公告列表
  noticeList (params) {
    return this.request({ url: '/notice/list?', data: params })
  }

  // 通知公告详情
  noticeInfo (params) {
    return this.request({ url: `/notice/info/${params}` })
  }

  // 通知公告阅读人数详情
  readingDetail (params) {
    return this.request({ url: '/notice/readingDetail?', data: params })
  }

  // 通知公告回执选项
  noticereturnoptionList (params) {
    return this.request({ url: '/noticereturnoption/list', data: params })
  }

  // 通知公告回执
  noticereturnAdd (params) {
    return this.request({ url: '/noticereturn/add', data: params })
  }

  // 通知公告增加浏览量
  browseSave (params) {
    return this.request({ url: '/browse/save', data: params })
  }
}
export {
  notice
}
