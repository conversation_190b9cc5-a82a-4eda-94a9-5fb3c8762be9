<template>
  <div class="questionnaireDetails">
    <div v-if="themeImg.url"
         :style="$general.loadConfiguration()">
      <van-image style="display:block;"
                 width="100%"
                 fit="cover"
                 :src="themeImg.url"></van-image>
    </div>
    <div v-if="content"
         class="n_details_content"
         :style="$general.loadConfiguration(-1)"
         v-html="content"></div>
    <ul class="questions_box"
        v-if="listData.length != 0">
      <li v-for="(item,index) in listData"
          :key="index"
          class="questions_item"
          :id="'questions_'+item.id">
        <div class="questions_title"
             :class="item.isMust==1?'questions_isMust':''"
             :style="$general.loadConfiguration(-1)">{{index+1}}.{{item.question}}({{item.answerType == 1?'单选':item.answerType == 2?'多选':'文本'}})</div>
        <div class="questions_answers_box">
          <!--单选框-->
          <template v-if="item.answerType == 1 && item.answersList != 0">
            <van-radio-group v-model="item.answersValue"
                             :disabled="status!='进行中'">
              <div v-for="(nItem,nIndex) in item.answersList"
                   :key="nIndex"
                   class="questions_items"
                   :class="status=='进行中'?'click':''"
                   :style="$general.loadConfiguration(-2)"
                   @click="if(status=='进行中')item.answersValue = nItem.questionId;">
                <van-radio v-if="hasAnswer"
                           :name="nItem.questionId"
                           :icon-size="(($general.appFontSize+2)*0.01)+'rem'"
                           :checked-color="appTheme">{{nItem.choiceText}}</van-radio>
                <template v-else>
                  <div class="answersProgress"
                       :style="'background:#'+(nIndex%2!=1?'f2f2f2':'e4f1ff')+';width:'+(nItem.num/item.num*100)+'%;'"></div>
                  <div class="flex_box flex_align_center inherit"
                       style="position: relative;">
                    <div class="inherit flex_placeholder"
                         style="color: #333;">{{nIndex+1}}.{{nItem.choiceText}}</div>
                    <div class="inherit"
                         style="color: #333;">{{nItem.num}}票</div>
                  </div>
                </template>
              </div>
            </van-radio-group>
          </template>
          <!--多选框-->
          <template v-else-if="item.answerType == 2 && item.answersList != 0">
            <van-checkbox-group v-model="item.answersValue"
                                :disabled="status!='进行中'">
              <div v-for="(nItem,nIndex) in item.answersList"
                   :key="nIndex"
                   class="questions_items"
                   :class="status=='进行中'?'click':''"
                   :style="$general.loadConfiguration(-2)"
                   @click="if(status=='进行中')$refs['checkbox'+item.id][nIndex].toggle();">
                <van-checkbox v-if="hasAnswer"
                              :name="nItem.questionId"
                              :label-disabled="true"
                              :ref="'checkbox'+item.id"
                              shape="square"
                              :icon-size="(($general.appFontSize+2)*0.01)+'rem'"
                              :checked-color="appTheme">{{nItem.choiceText}}</van-checkbox>
                <template v-else>
                  <div class="answersProgress"
                       :style="'background:#'+(nIndex%2!=1?'f2f2f2':'e4f1ff')+';width:'+(nItem.num/item.num*100)+'%;'"></div>
                  <div class="flex_box flex_align_center inherit"
                       style="position: relative;">
                    <div class="inherit flex_placeholder"
                         style="color: #333;">{{nIndex+1}}.{{nItem.choiceText}}</div>
                    <div class="inherit"
                         style="color: #333;">{{nItem.num}}票</div>
                  </div>
                </template>
              </div>
            </van-checkbox-group>
          </template>
          <template v-else-if="item.answerType == 3">
            <div :style="$general.loadConfiguration(-2)">
              <van-field v-if="hasAnswer"
                         v-model="item.answersValue"
                         :readonly="status!='进行中'"
                         rows="3"
                         autosize
                         type="textarea"
                         placeholder="请输入..."></van-field>
              <div class="inherit"
                   style="color: #333;padding: 12px;">{{item.answersText}}</div>
            </div>
          </template>
        </div>
      </li>
    </ul>
    <footer class="footer"
            :style="$general.loadConfiguration() ">
      <div class="flex_box">
        <div :style="$general.loadConfiguration(-2)+'width:100%;padding: 0 0rem;'">
          <div class="footer_btn flex_box flex_align_center flex_justify_content"
               @click="answer()"
               :style="'background:'+(status=='进行中'?appTheme:'#ccc')">
            <div :style="$general.loadConfiguration(-1)+'color: #FFF;font-weight: 600;'">{{status=='进行中'?'提交':status}}</div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
<script>
/* eslint-disable */
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Image as VanImage, Toast, Dialog } from 'vant'
export default {
  name: 'questionnaireDetails',
  components: {
    [Toast.name]: Toast,
    [Dialog.Component.name]: Dialog.Component,
    [VanImage.name]: VanImage
  },
  setup (context) {
    const route = useRoute()
    // const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: sessionStorage.getItem('appTheme'),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      id: route.query.id,
      title: '', // 页面标题
      themeImg: { url: '' }, // 有背景图
      content: '',
      listData: [],
      status: '',
      ifLoading: false, // 是否提交中
      hasAnswer: false
    })

    onMounted(() => {
      hasAnswer()
      redPointSign()
    })
    // 去除红点
    const redPointSign = async () => {
      const res = await $api.Networkpolitics.redPointSign({
        id: data.id,
        type: 'questionnaire'
      })
      console.log('去除红点', res)
    }
    // 获取是否可以答题
    const hasAnswer = async () => {
      const res = await $api.questionnaire.questionnaireIsAnswerApp({
        questionnaireId: data.id
      })
      data.hasAnswer = (res ? res.data : false)
      getInfo()
    }

    // 获取详情
    const getInfo = async () => {
      const res = await $api.questionnaire.questionnaireDetails(data.id)
      var { data: info } = res
      var attachmentList = info.attachmentList || []
      var bigImageforQSBG = $general.getItemForKey('bigImageforQSBG', attachmentList, 'moduleType')
      data.themeImg.url = bigImageforQSBG ? bigImageforQSBG.filePath : ''
      data.title = info.title || ''
      data.content = info.description || ''
      data.listData = []
      var questionListVo = info.questionListVo || []
      questionListVo.forEach(function (_eItem, _eIndex, _eArr) {
        var item = {};
        item.id = _eItem.id || "";
        item.answerType = _eItem.answerType || "";
        item.question = _eItem.question || "";
        item.isMust = _eItem.isMust || "";
        item.answersValue = item.answerType == 2 ? [] : "";
        item.answersText = _eItem.textContent || "";//回答的字段 
        item.num = _eItem.num;
        var answersListVo = _eItem.answersListVo || [];
        item.answersList = [];
        answersListVo.forEach(function (_nItem, _nIndex, _nArr) {
          item.answersList.push({ questionId: _nItem.id, choiceText: _nItem.choiceText, num: _nItem.num });
        })
        data.listData.push(item)
      })
      var startTime = info.startTime || "";
      var endTime = info.endTime || "";
      if (dayjs().isBefore(dayjs(startTime))) {//现在在开始之前 
        data.status = "未开始";
      } else if (dayjs().isAfter(dayjs(endTime))) {//现在在结束之后 
        data.status = "已结束";
      } else {
        data.status = data.hasAnswer ? "进行中" : "已答题";
      }
    }

    // 提交
    const answer = async (_submit) => {
      if (data.status != '进行中' || data.ifLoading) {
        return;
      }
      var listData = data.listData || []
      if (listData.length == 0) {
        Toast('题目为空，不能提交')
        return;
      }
      //提示必填
      for (var i = 0; i < listData.length; i++) {
        if (listData[i].isMust == 1 && (listData[i].answerType == 2 ? listData[i].answersValue.length == 0 : !listData[i].answersValue)) {
          // $("html, body").animate({ scrollTop: $api.offset($api.byId("questions_" + listData[i].id)).t }, 300);
          Toast((listData[i].answerType == 3 ? '请输入' : '请选择') + listData[i].question);
          return;
        }
      }
      var postParam = {
        questionnaireId: data.id,
        commitContent: []
      }
      for (var i = 0; i < listData.length; i++) {
        postParam.commitContent.push({
          questionId: listData[i].id,
          answerType: listData[i].answerType,
          content: (listData[i].answerType == 2 ? listData[i].answersValue.join(',') : listData[i].answersValue)
        })
      }
      console.log('postParam===>>', postParam)
      if (!_submit) {
        Dialog.confirm({
          title: '提示',
          message: '确定提交吗?'
        }).then(() => {
          answer(true);
        }).catch(() => {
          console.log('点击取消了')
        })
        return;
      }
      var hintWord = '提交'
      data.ifLoading = true
      const res = await $api.questionnaire.questionnaireCommit(postParam)
      console.log('提交成功了……===', res)
      data.ifLoading = false
      if (res) {
        var code = res.errcode || ''
        if (code == 200) {
          Toast(hintWord + '成功')
          data.themeImg.url = ''
        } else {
          Toast(res.errmsg || res.data || (hintWord + '失败'))
        }
      }
    }
    return { ...toRefs(data), dayjs, $general, answer }
  }
}
</script>

<style lang="less" >
.questionnaireDetails {
  background: #fff;
  width: 100%;
  .n_details_content {
    padding: 32px 15px 8px 16px;
    color: #999999;
    line-height: 1.46;
  }
  .questions_item {
    padding: 26px 16px 0 16px;
  }
  .questions_title {
    font-weight: bold;
    color: #333333;
    line-height: 1.46;
    position: relative;
  }
  .questions_isMust::before {
    position: absolute;
    left: -6px;
    color: #ff0000;
    font-size: 12px;
    content: "*";
  }
  .questions_answers_box {
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    overflow: hidden;
    min-height: 40px;
    margin-top: 12px;
  }
  .questions_box {
    padding-bottom: 100px;
  }
  .questions_items {
    padding: 12px;
    position: relative;
  }
  .questions_items + .questions_items {
    border-top: 1px solid #e3e3e3;
  }
  .answersProgress {
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 0;
  }
  #app .van-radio__label,
  #app .van-checkbox__label {
    margin-left: 10px;
  }
  #app .questions_answers_box .van-cell {
    padding: 10px;
  }
  .footer {
    padding: 8px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }
  .footer_btn {
    width: 100%;
    padding: 10px 0;
    background: #ffffff;
    border-radius: 17px;
  }
}
</style>

