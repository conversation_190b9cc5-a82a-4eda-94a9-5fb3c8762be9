<template>
  <div class="chooseHandleUnit">
    <div class="searchUnit">
      <van-search v-model="keyword"
                  show-action
                  placeholder="请输入搜索关键词"
                  @search="onSearch">
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    <van-empty v-if="!filterUnit.length"
               description="暂无数据" />
    <van-collapse v-model="activeNames">
      <van-collapse-item :title="item.value"
                         v-for="item in filterUnit"
                         :key="item.id"
                         :name="item.id">
        <van-checkbox-group v-model="checked"
                            direction="horizontal">
          <van-checkbox v-for="unit in item.idValueVoList"
                        :key="unit.id"
                        icon-size="16px"
                        @click="change(unit)"
                        :name="unit.id">{{unit.value}}</van-checkbox>
        </van-checkbox-group>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>
<script>
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'chooseHandleUnit',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const $api = inject('$api')
    const data = reactive({
      keyword: '',
      activeNames: [],
      unitData: [],
      filterUnit: [],
      checked: [],
      selectedUnit: []
    })
    onMounted(() => {
      obtain()
      chooseList()
    })
    const obtain = () => {
      var arr = []
      props.data.forEach(item => {
        arr.push(item.id)
      })
      data.checked = arr
      data.selectedUnit = props.data
    }
    const chooseList = async (ids) => {
      const res = await $api.general.chooseList({
        pageNo: 0,
        pageSize: 0,
        isUse: 1
      })
      var { data: unitData } = res
      unitData.forEach(item => {
        item.id = item.typeId
        item.value = item.typeValue
      })
      data.unitData = unitData
      data.filterUnit = unitData
    }
    const onSearch = () => {
      data.filterUnit = filterTree(data.unitData, shopfilterNode) || []
    }
    const filterTree = (nodes, predicate) => {
      if (!nodes || !nodes.length) return void 0  // eslint-disable-line
      const children = []
      for (let node of nodes) {
        node = Object.assign({}, node)
        const sub = filterTree(node.idValueVoList, predicate)
        if ((sub && sub.length) || predicate(node)) {
          sub && (node.idValueVoList = sub)
          children.push(node)
        }
      }
      return children.length ? children : void 0  // eslint-disable-line
    }
    const shopfilterNode = (item) => {
      if (!item.value) return false
      return item.value.includes(data.keyword)
    }
    const change = (row) => {
      if (data.checked.includes(row.id)) {
        data.selectedUnit.push(row)
      } else {
        data.selectedUnit = data.selectedUnit.filter(tab => tab.id !== row.id)
      }
    }
    return { ...toRefs(data), onSearch, change }
  }
}
</script>
<style lang="less">
.chooseHandleUnit {
  width: 100%;
  .van-search {
    padding: 16px;
    .van-field__body {
      font-size: 14px;
      .van-field__control {
        color: #888;
        &::-webkit-input-placeholder {
          color: #888;
        }
      }
    }
    .van-search__action {
      div {
        font-size: 14px;
      }
    }
  }
  .van-cell__title {
    font-size: 14px;
  }
  .van-checkbox {
    margin-right: 16px;
    margin-bottom: 9px;
  }
  .van-checkbox__label {
    font-size: 14px;
  }
}
</style>
