
import {
  HTTP,
  loginUc
} from '../http.js'
class conferenceActivitiesFile extends HTTP {
  // 获取全会数据
  findAppConferenceParents (params) {
    return this.request({
      url: '/conferenceparent/findAppConferenceParents?',
      data: params
    })
  }

  // 获取首页会议待办
  getPendingConferenceLists (params) {
    return this.request({
      url: '/conference/getPendingConferenceLists?',
      data: params
    })
  }

  // 获取普通会议数据
  findAppConferences (params) {
    return this.request({
      url: '/conference/findAppConferences?',
      data: params
    })
  }

  // 会议详情
  conferencenfo (params) {
    return this.request({
      url: `/conference/appInfo/${params}`
    })
  }

  // 会议详情资料附件
  conferencematerialList (params) {
    return this.request({
      url: '/conferencematerial/list?',
      data: params
    })
  }

  // 扫码登录
  apptoken (params) {
    return this.request({
      url: `${loginUc}/receipt/apptoken?`,
      data: params
    })
  }

  // 会议详情资料附件2
  conferencematerialGetFileVos (params) {
    return this.request({
      url: '/conferencematerial/getFileVos?',
      data: params
    })
  }

  // 会议报名数据统计
  conferenceGetAttendanceNum (params) {
    return this.request({
      url: '/conference/getAttendanceNum?',
      data: params
    })
  }

  // 会议出勤率统计
  conferenceGetSignUpNum (params) {
    return this.request({
      url: '/conference/getSignUpNum?',
      data: params
    })
  }

  // 报名参会
  conferenceAddMySignUp (params) {
    return this.request({
      url: '/conference/addMySignUp?',
      data: params
    })
  }

  // 参会成功弹框确定
  conferenceUpdateUserInfo (params) {
    return this.request({
      url: '/conference/updateUserInfo?',
      data: params
    })
  }

  // 口令签到
  conferenceAddMySignIn (params) {
    return this.request({
      url: '/conference/addMySignIn?',
      data: params
    })
  }

  // 请假提交
  conferenceAddLeave (params) {
    return this.request({
      url: '/conference/addLeave',
      data: params
    })
  }

  // 会议请假详情
  conferenceleaveInfo (params) {
    return this.request({
      url: `/conferenceleave/info/${params}`
    })
  }

  // 会议请假审核表头数据
  getCopyAttendanceHeader (params) {
    return this.request({
      url: '/conference/getCopyAttendanceHeader',
      data: params
    })
  }

  // 会议请假审核人员列表  || 参会人员信息
  getAttendanceListVos (params) {
    return this.request({
      url: '/conference/getAttendanceListVos',
      data: params
    })
  }

  // 会议请假审核人员确定
  conferenceleaveDels (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 会议资料管理是否公开
  updateIsRelease (ids, isRelease) {
    return this.request({
      url: `/conferencematerial/updateIsRelease/?ids=${ids}&isRelease=${isRelease}`
    })
  }

  // 会议考勤管理表头数据
  getAttendanceHeader (params) {
    return this.request({
      url: '/conference/getAttendanceHeader',
      data: params
    })
  }

  // 待办活动
  getPendingActivityLists (params) {
    return this.request({
      url: '/activityupgrade/getPendingActivityLists',
      data: params
    })
  }

  // 活动列表数据
  findAppActivitys (params) {
    return this.request({
      url: '/activityupgrade/findAppActivitys?',
      data: params
    })
  }

  // 活动通知详情
  activityupgradeetActivityDetail (params) {
    return this.request({
      url: '/activityupgrade/getActivityDetail?',
      data: params
    })
  }

  // 活动详情附件
  getMaterainfoList (params) {
    return this.request({
      url: '/activityupgrade/getMaterainfoList?',
      data: params
    })
  }

  // 活动请假
  activityAddLeave (params) {
    return this.request({
      url: '/activityupgrade/addLeave',
      data: params
    })
  }

  // 活动请假详情
  activityleaveInfo (params) {
    return this.request({
      url: `/leave/info/${params}`
    })
  }

  // 活动报名
  activityaddMySignUp (params) {
    return this.request({
      url: '/activityupgrade/addMySignUp',
      data: params
    })
  }

  // 活动签到
  activityaddMySignIn (params) {
    return this.request({
      url: '/activityupgrade/addMySignIn',
      data: params
    })
  }

  // 活动出勤率
  getSignUpNum (params) {
    return this.request({
      url: '/activityupgrade/getAttendanceNum',
      data: params
    })
  }

  // 活动考勤数据统计
  getAttendanceNum (params) {
    return this.request({
      url: '/activityupgrade/getSignUpNum',
      data: params
    })
  }

  // 活动资料管理是否公开
  editIsAppShow (ids, isAppShow) {
    return this.request({
      url: `/materiainfo/editIsAppShow/?ids=${ids}&isAppShow=${isAppShow}`
    })
  }

  // 活动请假审核表头数据
  activityAttendanceHeader (params) {
    return this.request({
      url: '/activityupgrade/getAttendanceHeader',
      data: params
    })
  }

  // 活动请假审核人员列表  || 参会人员信息
  activityAttendanceListVos (params) {
    return this.request({
      url: '/activityupgrade/getAttendanceListVos',
      data: params
    })
  }

  // 获取签名信息
  signature (params) {
    return this.request({
      url: '/shandongAccess/signature',
      data: params
    })
  }
}
export {
  conferenceActivitiesFile
}
