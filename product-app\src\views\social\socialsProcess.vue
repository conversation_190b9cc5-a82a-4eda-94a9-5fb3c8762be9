<template>
  <div class="socialsProcess">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <!-- 是否处理 -->
        <van-field name="radio"
                   label="是否处理"
                   required
                   :rules="rules.ishandle">
          <template #input>
            <van-radio-group v-model="form.ishandle"
                             direction="horizontal">
              <van-radio v-for="item in handleData"
                         :key="item.value"
                         :name="item.value"
                         icon-size="16">{{item.text}}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <!-- 审核意见 -->
        <van-field v-if="form.ishandle=== '0'"
                   v-model="form.ReviewComments"
                   name="审核意见"
                   show-word-limit
                   label="审核意见"
                   placeholder="请输入审核意见" />
        <!-- 稿件有效性 -->
        <van-field v-if="form.ishandle=== '1'"
                   name="radio"
                   label="稿件有效性"
                   required
                   :rules="rules.validity">
          <template #input>
            <van-radio-group v-model="form.validity"
                             direction="horizontal">
              <van-radio v-for="item in validityData"
                         :key="item.value"
                         :name="item.value"
                         icon-size="16">{{item.text}}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <!-- 采用情况 -->
        <van-field v-if="form.ishandle=== '1' && form.validity === '1'"
                   name="radio"
                   label="采用情况"
                   required
                   :rules="rules.isAdoption">
          <template #input>
            <van-radio-group v-model="form.isAdoption"
                             direction="horizontal">
              <van-radio v-for="item in AdoptionData"
                         :key="item.value"
                         :name="item.value"
                         icon-size="16">{{item.text}}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <!-- 报送方式 -->
        <van-field v-if="form.ishandle=== '1' && form.validity === '1' && form.isAdoption === '41'"
                   name="checkboxGroup"
                   label="报送方式">
          <template #input>
            <van-checkbox-group v-model="form.isMission"
                                direction="horizontal">
              <van-checkbox v-for="item in SubmissionMethod"
                            :name="item.value"
                            :key="item.value"
                            :checked-color="appTheme"
                            shape="square">{{item.text}}</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <!-- 反馈情况 -->
        <van-field name="checkboxGroup"
                   v-if="form.ishandle=== '1' && form.validity === '1' && form.isAdoption === '41'"
                   label="反馈情况">
          <template #input>
            <van-checkbox-group v-model="form.isFeedback"
                                direction="horizontal">
              <van-checkbox v-for="item in Feedback"
                            :name="item.value"
                            :key="item.value"
                            :checked-color="appTheme"
                            shape="square">{{item.text}}</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <van-field name="checkboxGroup"
                   label="短信通知">
          <template #input>
            <van-checkbox-group v-model="form.SMSnotice"
                                direction="horizontal">
              <van-checkbox :name="item.value"
                            v-for="item in SMSnotification"
                            :key="item.value"
                            shape="square">{{item.text}}</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
      </van-cell-group>
      <div class="newButton">
        <div class="flex_placeholder">
          <van-button round
                      size="large"
                      color="#3088FE"
                      native-type="submit">
            保存</van-button>
        </div>
      </div>
    </van-form>
  </div>
</template>
<script>
import { onMounted, reactive, ref, toRefs, inject } from 'vue'
import { DatetimePicker, DropdownMenu, DropdownItem, Toast, Dialog, Popup } from 'vant'
import { useRouter, useRoute } from 'vue-router'
export default {
  name: 'socialsProcess',
  components: {
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem

  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $general = inject('$general')
    const $api = inject('$api')
    const user = ref(null)
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      form: {
        ishandle: '0', // 是否处理
        ReviewComments: '', // 审核意见
        validity: '0', // 稿件有效性
        isAdoption: '', // 采用情况
        SMSnotice: [], // 短信通知
        isMission: [], // 报送方式
        isFeedback: [] // 反馈情况
      },
      handleData: [
        { text: '是', value: '1' },
        { text: '否', value: '0' }
      ],
      validityData: [
        { text: '有效', value: '1' },
        { text: '无效', value: '0' }
      ],
      AdoptionData: [
        { text: '采用', value: '41' },
        { text: '留存', value: '39' }
      ],
      SubmissionMethod: [],
      Feedback: [],
      SMSnotification: [
        { text: '发送短信通知处理情况', value: '1' }
      ],
      id: route.query.id,
      rules: {
        ishandle: [{ required: true, message: '请选择是否处理' }],
        validity: [{ required: true, message: '请选择稿件有效性' }],
        isAdoption: [{ required: true, message: '请选择采用情况' }]
      }
    })
    onMounted(() => {
      getInfo()
      getSubmissionMethod()
      getFeedback()
    })
    // 详情
    const getInfo = async () => {
      const res = await $api.social.statusinfo(data.id)
      var info = res.data || {}
      data.form.ishandle = info.hasHandle ? '1' : '0'
      data.form.validity = info.isValid ? '1' : '0'
      data.form.isAdoption = info.processStatus === 41 ? '41' : info.processStatus === 39 ? '39' : info.processStatus === 40 ? '39' : ''
      var journalIds = []
      var journals = info.journals || []
      journals.forEach(function (_eItem, _eIndex, _eArr) {
        journalIds.push(_eItem.id)
      })
      data.form.isMission = journalIds
      var approveIds = []
      var approves = info.approves || []
      approves.forEach(function (_eItem, _eIndex, _eArr) {
        approveIds.push(_eItem.id)
      })
      data.form.isFeedback = approveIds
      data.form.SMSnotice = info.needSendMessage ? ['1'] : []
      data.form.ReviewComments = info.handleRemarks || ''
    }
    // 请求报送方式
    const getSubmissionMethod = async (values) => {
      const res = await $api.general.pubkvs({
        types: 'qdzx_social_report_type'
      })
      data.SubmissionMethod = res.data.qdzx_social_report_type.map((v) => {
        return {
          text: v.value,
          value: v.id
        }
      })
    }
    // 请求反馈情况
    const getFeedback = async (values) => {
      const res = await $api.general.pubkvs({
        types: 'opinion_approve'
      })
      data.Feedback = res.data.opinion_approve.map((v) => {
        return {
          text: v.value,
          value: v.id
        }
      })
    }
    // 提交
    const onSubmit = async (values) => {
      console.log('data.id---------', data.id)
      if (data.id) {
        const res = await $api.social.editqdstatus({
          empty: '1',
          id: data.id,
          hasHandle: data.form.ishandle,
          isValid: data.form.validity,
          processStatus: data.form.isAdoption,
          journalIds: data.form.isMission.join(','),
          approveIds: data.form.isFeedback.join(','),
          needSendMessage: data.form.SMSnotice,
          handleRemarks: data.form.ReviewComments
        })
        var { errcode, errmsg } = res
        if (errcode === 200) {
          Toast.success(errmsg)
          router.go(-1)
        }
      } else {
        const res = await $api.social.editqdstatus({
          empty: '1',
          id: data.id,
          hasHandle: data.form.ishandle,
          isValid: data.form.validity,
          processStatus: data.form.isAdoption,
          journalIds: data.form.isMission,
          approveIds: data.form.isFeedback,
          needSendMessage: data.form.SMSnotice,
          handleRemarks: data.form.ReviewComments
        })
        console.log(res)
        var { errcode: errcodes, errmsg: errmsgs } = res
        if (errcodes === 200) {
          Toast.success(errmsgs)
          router.go(-1)
        }
      }
    }
    return { ...toRefs(data), $general, user, onSubmit }
  }
}
</script>
<style lang="less" >
.socialsProcess {
  background: #fff;
  .van-form {
    width: 100%;
    .van-cell-group--inset {
      margin: 0;
      .van-field {
        margin: 20px 0;
      }
    }
    .newContent {
      flex-wrap: wrap;
      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }
      .van-field__body {
        background-color: #f5f5f5;
        padding: 6px 12px;
      }
    }
    .van-field__control {
      font-size: 14px;
    }
    .time-box {
      .van-cell__title {
        width: 120px;
      }
    }
    .activity-box {
      .van-cell__title {
        width: 220px !important;
      }
    }
  }
  .titleStyle {
    background-color: #fff;
    height: 48px;
    // width: 100%;
    width: 50%;
  }
  .van-dropdown-menu__title {
    margin-right: 15px;
  }
  --van-dropdown-menu-box-shadow: 0;
  .newButton {
    display: flex;
    justify-content: space-around;
    padding: 36px 18px;
    // padding-bottom: 88px;
    .van-button {
      // width: 128px;
      // height: 36px;
    }
  }
}
</style>>
