<template>
  <div class="activity-type-chart" :id="chartId"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ActivityTypeChart',
  props: {
    id: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const initChart = () => {
      // 定义交替的颜色配置
      const colors = [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#FFFFFF' },
            { offset: 1, color: '#EF817C' }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#FFFFFF' },
            { offset: 1, color: '#559FFF' }
          ]
        }
      ]
      const lineY = []
      const lineT = []
      var chartDataLabel = props.data.map(v => v.name)
      var chartDataValue = props.data.map(v => v.value)
      for (var i = 0; i < chartDataLabel.length; i++) {
        // 使用索引来交替选择颜色
        const colorIndex = i % 2
        var data = {
          name: chartDataLabel[i],
          value: chartDataValue[i],
          barGap: '-100%',
          itemStyle: {
            color: colors[colorIndex]
          }
        }
        var data1 = {
          value: chartDataValue[i],
          label: {
            show: true,
            position: 'right',
            color: '#999',
            fontSize: 14,
            distance: 10
          },
          itemStyle: {
            color: colors[colorIndex]
          }
        }
        lineY.push(data)
        lineT.push(data1)
      }
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: (p) => {
              if (p.seriesName === 'total') {
                return ''
              }
              return `${p.name}<br/>${p.value}`
            }
          },
          grid: {
            left: '2%',
            right: '20%',
            top: '3%',
            bottom: '2%',
            containLabel: true
          },
          yAxis: {
            type: 'category',
            inverse: true,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false,
              inside: false
            },
            data: chartDataLabel
          },
          xAxis: {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          },
          series: [
            {
              name: 'total',
              type: 'bar',
              barGap: '-100%',
              barWidth: 10,
              data: lineT,
              legendHoverLink: false
            },
            {
              name: 'bar',
              type: 'bar',
              barWidth: 10,
              data: lineY,
              label: {
                normal: {
                  show: true,
                  fontSize: '12px',
                  color: '#999',
                  position: [0, '-20px'],
                  formatter: '{b}'
                }
              }
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.activity-type-chart {
  width: 100%;
  height: 100%;
}
</style>
