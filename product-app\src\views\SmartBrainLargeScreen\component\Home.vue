<template>
  <div class="home_page">
    <div class="map_section">
      <MapQingdao :mapData="mapData" />
    </div>
    <!-- 委员统计 -->
    <div class="home_committee_statistics">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">委员统计</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">{{ circles }}</span>
        </div>
      </div>
      <div class="committee_statistics_box">
        <div class="statistics_card card_blue">
          <div class="card_content">
            <div class="card_number">{{ cppccMemberNum }}</div>
            <div class="card_label">政协委员(人)</div>
          </div>
        </div>
        <div class="statistics_card card_yellow">
          <div class="card_content">
            <div class="card_number">{{ standingCommitteeNum }}</div>
            <div class="card_label">政协常委(人)</div>
          </div>
        </div>
      </div>
      <div class="circles_box">
        <div class="circles_top_header">
          <span class="circles_top_header_title">界别分布</span>
          <span class="circles_top_header_more" style="">查看全部</span>
        </div>
        <horizontalBarEcharts id="circles" :barList="barList" colorStart="#FFFFFF" colorEnd="#EF817C"
          style="height: 260px;" />
      </div>
    </div>
    <!-- 提案统计 -->
    <div class="home_proposal_statistics">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">提案统计</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">{{ circles }}</span>
        </div>
      </div>
      <div class="proposal_statistics_box">
        <div class="proposal_card" v-for="(item, index) in proposalStatsNum" :key="index">
          <div class="proposal_number" :style="{ color: item.color }">{{ item.value }}</div>
          <div class="proposal_label">{{ item.label }}</div>
        </div>
      </div>
      <div class="proposal_type_analysis">
        <pieEchartsLegend id="typeAnalysisPie" :dataList="typeAnalysisList" title="类型分析" />
      </div>
    </div>
    <!-- 工作动态 -->
    <div class="home_work_dynamics">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">工作动态</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">本年</span>
        </div>
      </div>
      <div class="work_dynamics_list">
        <div class="work_dynamics_item" v-for="(item, idx) in workDynamicsList" :key="idx">
          <div class="work_dynamics_title">{{ item.title }}</div>
          <div class="work_dynamics_date">{{ item.date }}</div>
        </div>
      </div>
    </div>
    <!-- 社情民意 -->
    <div class="home_social">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">社情民意</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">本年</span>
        </div>
      </div>
      <div class="social_box">
        <!-- 总数卡片 -->
        <div class="social_card total_card" :style="{ background: socialStats[0].bg }">
          <div class="total_card_number" :style="{ color: socialStats[0].color }">{{ socialStats[0].total }}</div>
          <div class="total_card_label">{{ socialStats[0].label }}</div>
        </div>
        <!-- 报送卡片 -->
        <div class="social_card report_card" v-for="(item, idx) in socialStats.slice(1)" :key="idx"
          :style="{ background: item.bg }">
          <div class="report_row">
            <span class="report_label">总数</span>
            <span class="report_total" :style="{ color: item.color }">{{ item.total }}</span>
          </div>
          <div class="report_row">
            <span class="report_label">采用</span>
            <span class="report_adopted" :style="{ color: item.adoptedColor }">{{ item.adopted }}</span>
          </div>
          <div class="report_card_label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <!-- 会议活动 -->
    <div class="home_meetting_activity">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">会议活动</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">本年</span>
        </div>
      </div>
      <div class="meetting_activity_box">
        <div v-for="(item, idx) in meettingActivityList" :key="idx" class="meetting_activity_card"
          :class="item.cardClass">
          <img :src="item.icon" class="activity_card_iconimg" />
          <div class="meetting_activity_card_content">
            <div class="meetting_activity_card_label">{{ item.label1 }}</div>
            <div class="meetting_activity_card_value" :style="item.value1Style">{{ item.value1 }}</div>
            <div class="meetting_activity_card_label">{{ item.label2 }}</div>
            <div class="meetting_activity_card_value" :style="item.value2Style">{{ item.value2 }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 网络议政 -->
    <div class="home_discussions">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">网络议政</span>
        </div>
      </div>
      <div class="discussions_box">
        <div class="discussion_card" v-for="(item, idx) in discussionsList" :key="idx"
          :style="{ backgroundImage: `url(${item.bg})` }">
          <div class="discussion_card_number">{{ item.number }}<span class="discussion_card_unit">{{ item.unit }}</span>
          </div>
          <div class="discussion_card_label">{{ item.label }}</div>
        </div>
      </div>
      <div class="hot_topics">
        <div class="hot_topics_title">最热话题</div>
        <div class="hot_topics_list">
          <div class="hot_topic_item" v-for="(topic, idx) in hotTopics" :key="idx">
            <span class="hot_topic_index" :class="'hot_topic_index_' + (idx + 1)">{{ idx + 1 }}</span>
            <span class="hot_topic_text">{{ topic.title }}</span>
            <span class="hot_topic_tag" :class="'hot_topic_tag_' + (idx + 1)">热</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 履职统计 -->
    <div class="home_performance_statistics">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">履职统计</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">{{ circles }}</span>
        </div>
      </div>
      <div class="performance_statistics_box">
        <div class="performance_table">
          <div class="performance_table_header">
            <span>姓名</span>
            <span>会议活动</span>
            <span>政协提案</span>
            <span>社情民意</span>
          </div>
          <div class="performance_table_row" v-for="(item, idx) in performanceStatistics" :key="item.name"
            :class="{ 'row-alt': idx % 2 === 1 }">
            <span>{{ item.name }}</span>
            <span>{{ item.meeting }}</span>
            <span>{{ item.proposal }}</span>
            <span>{{ item.opinion }}</span>
          </div>
          <div class="performance_table_footer">
            <button class="view-all-btn">查看全部</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
import MapQingdao from './echartsComponent/MapQingdao.vue'
import horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'
import pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'

export default {
  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },
  setup () {
    const data = reactive({
      mapData: [
        { name: '市南区', value: 80 },
        { name: '市北区', value: 60 },
        { name: '李沧区', value: 50 },
        { name: '崂山区', value: 40 },
        { name: '城阳区', value: 70 },
        { name: '黄岛区', value: 90 },
        { name: '即墨区', value: 30 },
        { name: '胶州市', value: 55 },
        { name: '平度市', value: 20 },
        { name: '莱西市', value: 10 }
      ],
      circles: '十一届二次',
      cppccMemberNum: '10095',
      standingCommitteeNum: '8742',
      barList: [
        { name: '教育界', value: 35 },
        { name: '医药卫生界', value: 15 },
        { name: '经济界', value: 14 },
        { name: '工商联界', value: 21 },
        { name: '民革界', value: 15 },
        { name: '特邀界', value: 21 },
        { name: '妇联界', value: 8 },
        { name: '工会界', value: 8 },
        { name: '社会福利与社会保障界', value: 14 }
      ],
      proposalStatsNum: [
        { value: 873, label: '提案总数', color: '#2386F9' },
        { value: 456, label: '委员提案', color: '#2CA6F9' },
        { value: 354, label: '界别提案', color: '#3AC86B' },
        { value: 221, label: '组织提案', color: '#F96C9C' }
      ],
      typeAnalysisList: [
        { name: '发改财政', value: 22.52, color: '#3DC3F0' },
        { name: '民政市场', value: 18.33, color: '#4AC6A8' },
        { name: '公安司法', value: 12.5, color: '#F9C846' },
        { name: '区市政府', value: 11.34, color: '#6DD3A0' },
        { name: '科技工信', value: 9.56, color: '#7B8DF9' },
        { name: '教育文化', value: 8.09, color: '#F97C9C' },
        { name: '派出机构', value: 4.21, color: '#F9A846' },
        { name: '驻青单位', value: 3.71, color: '#F97C46' },
        { name: '住建交通', value: 3.65, color: '#A97CF9' },
        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },
        { name: '其他机构', value: 1.86, color: '#BFBFBF' },
        { name: '党群其他', value: 1.02, color: '#F9C8C8' }
      ],
      workDynamicsList: [
        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },
        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },
        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },
        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },
        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }
      ],
      socialStats: [
        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },
        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },
        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }
      ],
      meettingActivityList: [
        {
          cardClass: 'card_meeting',
          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),
          label1: '会议次数',
          value1: 201,
          value1Style: { color: '#308FFF' },
          label2: '会议人数',
          value2: 2412,
          value2Style: { color: '#308FFF' }
        },
        {
          cardClass: 'card_activity',
          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),
          label1: '活动次数',
          value1: 310,
          value1Style: { color: '#1FC6FF' },
          label2: '活动人数',
          value2: 4015,
          value2Style: { color: '#1FC6FF' }
        }
      ],
      discussionsList: [
        {
          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),
          number: '72',
          unit: '个',
          label: '发布议题',
          desc: ''
        },
        {
          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),
          number: '39301',
          unit: '次',
          label: '累计参与人次',
          desc: ''
        },
        {
          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),
          number: '12308',
          unit: '条',
          label: '累计征求意见',
          desc: ''
        }
      ],
      hotTopics: [
        { title: '推进黄河国家文化公园建设' },
        { title: '持续推进黄河流域生态保护修复，助力…' },
        { title: '全面加强新时代中小学劳动教育' }
      ],
      performanceStatistics: [
        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },
        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },
        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },
        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },
        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },
        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },
        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },
        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },
        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },
        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }
      ]
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less" scoped>
.home_page {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }
    }
  }

  .map_section {
    background: #fff;
    border-radius: 8px;
    padding: 10px;
  }

  .home_committee_statistics {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .committee_statistics_box {
      display: flex;
      gap: 15px;
      padding: 20px 15px 10px 15px;

      .statistics_card {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5faff;
        position: relative;
        height: 86px;

        .card_content {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin-left: 55px;
          margin-top: 5px;

          .card_number {
            font-size: 20px;
            color: #4AA3FF;
          }

          .card_label {
            font-size: 14px;
            color: #666;
            margin-top: 2px;
          }
        }
      }

      .card_blue {
        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');
        background-size: 100% 100%;
        background-position: center;
      }

      .card_yellow {
        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');
        background-size: 100% 100%;
        background-position: center;

        .card_number {
          color: #E6B800 !important;
        }
      }
    }

    .circles_box {
      border-radius: 6px;
      margin: 10px 12px;

      .circles_top_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .circles_top_header_title {
          font-size: 14px;
          color: #000;
          font-family: Source Han Serif SC, Source Han Serif SC;
        }

        .circles_top_header_more {
          font-size: 14px;
          color: #0271E3;
          border-radius: 14px;
          border: 1px solid #0271E3;
          padding: 3px 10px;
        }
      }
    }
  }

  .home_proposal_statistics {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .proposal_statistics_box {
      display: flex;
      justify-content: space-between;
      padding: 20px 15px 10px 15px;

      .proposal_card {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        .proposal_number {
          font-size: 24px;
        }

        .proposal_label {
          font-size: 14px;
          color: #999;
          margin-top: 4px;
        }
      }
    }

    .proposal_type_analysis {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .home_work_dynamics {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .work_dynamics_list {
      padding: 8px 15px;
      background: #fff;

      .work_dynamics_item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .work_dynamics_title {
          flex: 1;
          font-size: 14px;
          color: #666666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .work_dynamics_date {
          font-size: 14px;
          color: #bdbdbd;
          flex-shrink: 0;
        }
      }
    }
  }

  .home_social {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .social_box {
      display: flex;
      gap: 16px;
      padding: 20px 15px;

      .social_card {
        flex: 1;
        width: 97px;
        height: 94px;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        box-shadow: none;
        padding: 15px;
        background-clip: padding-box;
      }

      .total_card {
        justify-content: center;

        .total_card_number {
          font-size: 20px;
          color: #3B91FB;
          margin-bottom: 5px;
        }

        .total_card_label {
          font-size: 14px;
          color: #666666;
        }
      }

      .report_card {
        justify-content: flex-start;

        .report_row {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: baseline;
          margin-bottom: 2px;

          .report_label {
            font-size: 14px;
            color: #999;
            margin-right: 2px;
          }

          .report_total {
            font-size: 15px;
          }

          .report_adopted {
            font-size: 15px;
          }
        }

        .report_card_label {
          margin-top: 5px;
          font-size: 15px;
          color: #666;
          font-family: Source Han Serif SC, Source Han Serif SC;
        }
      }
    }
  }

  .home_meetting_activity {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .meetting_activity_box {
      display: flex;
      gap: 16px;
      padding: 20px 15px;

      .meetting_activity_card {
        flex: 1;
        display: flex;
        align-items: flex-start;
        box-sizing: border-box;
        width: 157px;
        height: 140px;
        padding: 14px 20px;

        .activity_card_iconimg {
          width: 32px;
          height: 32px;
          margin-right: 15px;
          margin-top: 4px;
        }

        .meetting_activity_card_content {
          display: flex;
          flex-direction: column;
          justify-content: center;
          flex: 1;

          .meetting_activity_card_label {
            font-size: 14px;
            color: #999;
            margin-bottom: 5px;
          }

          .meetting_activity_card_value {
            font-size: 20px;
            color: #308FFF;
            margin-bottom: 8px;
          }
        }
      }

      .card_meeting {
        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');
        background-size: 100% 100%;
        background-position: center;
      }

      .card_activity {
        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');
        background-size: 100% 100%;
        background-position: center;
      }
    }
  }

  .home_discussions {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .discussions_box {
      display: flex;
      gap: 10px;
      padding: 20px 15px;
      justify-content: flex-start;

      .discussion_card {
        flex: 1;
        width: 103px;
        height: 77px;
        background-size: 100% 100%;
        background-position: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;

        .discussion_card_number {
          font-size: 20px;
          margin-bottom: 2px;

          .discussion_card_unit {
            font-size: 12px;
            font-weight: normal;
            margin-left: 2px;
          }
        }

        .discussion_card_label {
          font-size: 14px;
          font-weight: 400;
          margin-bottom: 2px;
        }
      }
    }

    .hot_topics {
      padding: 0 15px 15px 15px;

      .hot_topics_title {
        font-size: 14px;
        color: #000;
        margin-bottom: 10px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }

      .hot_topics_list {
        .hot_topic_item {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #f0f0f0;
          padding: 12px 0;

          .hot_topic_index {
            font-size: 14px;
            margin-right: 10px;
          }

          .hot_topic_index_1 {
            color: #FF4D4F;
          }

          .hot_topic_index_2 {
            color: #FF9900;
          }

          .hot_topic_index_3 {
            color: #FFD600;
          }

          .hot_topic_text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            color: #666;
          }

          .hot_topic_tag {
            font-size: 14px;
            color: #fff;
            border-radius: 2px;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .hot_topic_tag_1 {
            background: #FB3030;
          }

          .hot_topic_tag_2 {
            background: #FF833E;
          }

          .hot_topic_tag_3 {
            background: #FFD978;
          }
        }
      }
    }
  }

  .home_performance_statistics {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .performance_statistics_box {
      margin-top: 15px;

      .performance_table {
        width: 100%;
        background: #fff;

        .performance_table_header,
        .performance_table_row {
          display: flex;
          align-items: center;
          padding: 8px 0;

          span {
            flex: 1;
            text-align: center;
          }
        }

        .performance_table_header {
          background: #F1F8FF;
          font-weight: bold;
          color: #222;
          font-size: 14px;
        }

        .performance_table_row {
          background: #fff;
          color: #222;
          font-size: 14px;

          &.row-alt {
            background: #F1F8FF;
          }
        }

        .performance_table_footer {
          display: flex;
          justify-content: center;
          padding: 10px 0;
          background: #fff;

          .view-all-btn {
            border: 1px solid #0271e3;
            color: #0271e3;
            background: #fff;
            border-radius: 16px;
            padding: 4px 14px;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
