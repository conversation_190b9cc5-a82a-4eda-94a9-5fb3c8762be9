<template>
  <div class="forward">
    <van-sticky>
      <van-nav-bar title="转发"
                   left-text=""
                   right-text=""
                   :left-arrow="isShowHead"
                   @click-left="onClickLeft"
                   @click-right="onClickRight">
        <template #right>
          <van-icon name="guide-o"
                    size="20" />&nbsp;确定
        </template>
      </van-nav-bar>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <li v-for="(item,index) in fixedData"
            :key="index"
            @click="selectFixedItem(item)"
            class="recent_item flex_box flex_align_center click">
          <div class="flex_placeholder text_one2"
               :style="'padding: 10px 10px;'">{{item.name}}</div>
          <van-icon :size="16"
                    :color="'#666'"
                    :name="'arrow'"></van-icon>
        </li>
        <ul v-if="dataList.length != 0"
            class="recent_box">
          <div class="recent_hint">最近</div>
          <van-checkbox-group v-model="sendInfo">
            <li v-for="(item,index) in dataList"
                :key="index"
                class="recent_item flex_box flex_align_center click">
              <van-checkbox :icon-size="26"
                            :name="item">
                <div class="recent_item flex_box flex_align_center click">
                  <img :style="'width:41px;height:41px;object-fit: contain;margin: 0 0.1px;border-radius:50%;'"
                       :src="item.img" />
                  <div class="flex_placeholder text_one2">{{item.name}}</div>
                </div>
              </van-checkbox>
            </li>
          </van-checkbox-group>
        </ul>

        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton">
          <van-skeleton round
                        v-for="(item,index) in 3"
                        :key="index"
                        title
                        :row="3"></van-skeleton>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import * as RongIMLib from '@rongcloud/imlib-next'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { NavBar, Sticky, CellGroup, Button, Icon, Image as VanImage, Divider, ActionSheet, Toast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { getConversionTime } from '../../../assets/js/date.js'
export default {
  name: 'chatRoom',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Divider.name]: Divider
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      sendInfoMsg: JSON.parse(route.query.sendInfo),
      user: JSON.parse(sessionStorage.getItem('user')),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      loading_more: require('../../../assets/img/loading_more.gif'),
      icon_fail: require('../../../assets/img/icon_fail.png'),
      icon_notificationStatus: require('../../../assets/img/icon_notificationStatus.png'),
      seachPlaceholder: '请输入搜索内容',
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      startTime: 0, // 可选项）以此时间戳为基准，根据 order 向前或向后查询，当值是 0 时为边界（order 值是 0 时为最大时间戳，order 值是 1 时为最小时间戳）
      count: 10, // （可选项）获取消息的数量，范围: 1-20
      order: 0, // 可选项）查询消息的方向，值为 0或1，0 为以 timestamp 为基准向前查询，1 为以 timestamp 为基准向后查询
      fixedData: [
        // { type: 'PRIVATE', onlyPage: '0', name: '通讯录', unit: '人' },
        // { type: 'GROUP', onlyPage: '1', name: '群组', unit: '群' }
      ],
      dataList: [
        // { id: '1', url: 'https://img01.yzcdn.cn/vant/logo.png', name: '张三' },
        // { id: '2', url: 'https://img01.yzcdn.cn/vant/logo.png', name: '李四' }
      ], // 列表数据

      sendInfo: []
    })
    onMounted(() => {
      // RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
      // RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
      //   console.log(res)
      //   onRefresh()
      // })
    })

    const openGroupList = () => {
      router.push({ name: 'groupList', query: {} })
    }
    const selectFixedItem = () => {
      // router.push({ name: 'groupList', query: {} })
    }
    const getHistory = () => {
      RongIMLib.getConversationList({
        count: data.count,
        startTime: data.startTime,
        order: data.order
      }).then(res => {
        if (res.code === 0) {
          console.log(res.code, res.data)
          data.loading = false
          const showDatas = []
          res.data.forEach(element => {
            getUserMsg(element)
            element.latestMessage.content.extra = element.latestMessage.content.extra ? JSON.parse(element.latestMessage.content.extra) : ''
            getUserInfo(element.latestMessage)
            element.sentTime = getConversionTime(new Date(element.latestMessage.sentTime), new Date(), 1)
            element.objectName = element.latestMessage.messageType
            showDatas.push(element)
          })
          setTimeout(() => {
            data.dataList = data.dataList.concat(showDatas)
          }, 1000)
          data.loading = false
          data.refreshing = false
          // 数据全部加载完成
          if (res.data.length < data.count) {
            data.finished = true
          }
        } else {
          console.log(res.code, res.msg)
        }
      })
    }
    const getUserInfo = async (_item) => {
      var datas = {
        id: _item.senderUserId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      var { data: userInfo } = await $api.rongCloud.getUserInfo(datas)
      _item.img = userInfo.fullImgUrl || ''
      _item.name = userInfo.userName || ''
    }
    // 获取当前的用户的信息
    const getUserMsg = async (_item, _index) => {
      // 每次请求 可放else里面减少请求
      var datas = {
        id: _item.targetId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      // 1: 单聊3: 群聊4: 聊天室5: 客服会话6: 系统消息7: 默认关注的公众号8: 手动关注的公众号9: RTCLib 房间
      if (_item.conversationType === 1) {
        var { data: userInfo } = await $api.rongCloud.getUserInfo(datas)
        _item.img = userInfo.fullImgUrl || ''
        _item.name = userInfo.userName || ''
        _item.position = userInfo.position || ''
      } else if (_item.conversationType === 3) {
        var { data: gruopInfo } = await $api.rongCloud.getGroupInfo(datas)
        _item.img = gruopInfo.groupImgUrl || ''
        _item.name = gruopInfo.groupName || ''
        _item.groupOwner = gruopInfo.groupOwner || ''
        _item.groupType = gruopInfo.groupType || ''
        _item.openSay = gruopInfo.openSay || ''
      }
      // that.getChatInfo(_item, function (ret) {
      //   if (ret.isDel) {
      //     that.removeConversation(_item, _index)
      //   }
      // })
    }
    const onRefresh = () => {
      data.order = 0
      data.startTime = 0
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getHistory()
    }

    const onLoad = () => {
      // getHistory()
    }
    const onClickLeft = () => history.back()
    const onClickRight = () => {
      console.log(data.sendInfo)
      console.log(data.sendInfoMsg)
      if (data.sendInfo.length !== 0) {
        Toast('转发成功')
        data.sendInfo.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
          switch (data.sendInfoMsg.type) {
            case 'sendRichContentMessage':// 自定义消息
              sendRichContentMessage('[书籍]', data.sendInfoMsg.info, _eItem)
              break
          }
        })
        setTimeout(() => {
          onClickLeft()
        }, 1000)
      } else {
        Toast('请选择一个转发对象')
      }
    }

    const sendRichContentMessage = (sendType, content, _eItem) => {
      const conversation = { conversationType: _eItem.conversationType, targetId: _eItem.targetId }
      // 实例化待发送消息，RongIMLib.TextMessage 为内置文本型消息
      const message = new RongIMLib.RichContentMessage({
        content: sendType + ',' + JSON.stringify(content),
        title: sendType,
        description: sendType + ',' + JSON.stringify(content),
        extra: JSON.stringify({ userInfo: { nickName: data.user.userName.userName, photo: data.user.userName.headImg } })
      })
      console.log(conversation)
      // 发送
      RongIMLib.sendMessage(conversation, message).then(res => {
        if (res) {
          Toast('发送成功')
          setTimeout(() => {
            onClickLeft()
          }, 1000)
        } else {
          Toast('发送失败')
        }
      })
    }

    return { ...toRefs(data), onClickLeft, onClickRight, onRefresh, onLoad, onMounted, getUserInfo, getUserMsg, openGroupList, selectFixedItem }
  }
}
</script>
<style lang="less" scoped>
@import "./forward.less";
</style>
