const memberReportList = () => import('@/views/memberReport/memberReportList')
const memberReportDetails = () => import('@/views/memberReport/memberReportDetails')
const memberReportNew = () => import('@/views/memberReport/memberReportNew')
const memberReport = [{
  path: '/memberReportList',
  name: 'memberReportList',
  component: memberReportList,
  meta: {
    title: '列表',
    keepAlive: true
  }
},
{
  path: '/memberReportDetails',
  name: 'memberReportDetails',
  component: memberReportDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
},
{
  path: '/memberReportNew',
  name: 'memberReportNew',
  component: memberReportNew,
  meta: {
    title: '新增',
    keepAlive: true
  }
}]
export default memberReport
