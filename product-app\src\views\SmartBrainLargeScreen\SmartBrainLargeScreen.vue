<template>
  <div class="SmartBrainLargeScreen">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="智慧云脑" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <!-- 顶部图 -->
    <img src="../../assets/img/largeScreen/icon_top_bg.png" alt=""
      style="width: 100%;height: 100%;padding: 12px;box-sizing: border-box;">
    <div class="tab_box">
      <div class="tab_scroll">
        <div v-for="(tab, idx) in tabList" :key="tab.name" :class="['tab_item', { active: idx === activeIndex }]"
          @click="onTabClick(idx)">
          {{ tab.name }}
        </div>
      </div>
    </div>
    <div class="tab_content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>
<script>
import { inject, reactive, toRefs, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { NavBar, Sticky } from 'vant'
import Home from './component/Home.vue'
import MemberInfo from './component/MemberInfo.vue'
import PublicOpinion from './component/PublicOpinion.vue'
import NetworkPolitics from './component/NetworkPolitics.vue'
import ProposalWork from './component/ProposalWork.vue'
import MemberPerformance from './component/MemberPerformance.vue'
export default {
  name: 'SmartBrainLargeScreen',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    Home,
    MemberInfo,
    PublicOpinion,
    NetworkPolitics,
    ProposalWork,
    MemberPerformance
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '重点工作',
      user: JSON.parse(sessionStorage.getItem('user')),
      tabList: [
        { id: '1', name: '首页' },
        { id: '2', name: '委员信息' },
        { id: '3', name: '社情民意' },
        { id: '4', name: '网络议政' },
        { id: '5', name: '提案工作' },
        { id: '6', name: '委员履职' }
        // { id: '7', name: '有事多商量' }
      ],
      activeIndex: 0
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = true
    } else {
      data.hasApi = true
    }
    onMounted(() => {
    })
    const onClickLeft = () => {
      if (typeof (api) === 'undefined') return history.back()
      // eslint-disable-next-line no-undef
      api.closeWin()
    }
    const onTabClick = idx => { data.activeIndex = idx }
    const componentMap = [
      'Home',
      'MemberInfo',
      'PublicOpinion',
      'NetworkPolitics',
      'ProposalWork',
      'MemberPerformance'
    ]
    return {
      ...toRefs(data),
      $api,
      onClickLeft,
      $general,
      onTabClick,
      currentComponent: computed(() => componentMap[data.activeIndex])
    }
  }
}
</script>
<style lang="less">
.SmartBrainLargeScreen {
  width: 100%;
  height: 100%;
  background: #F4F4F4;

  .van-nav-bar {
    padding-top: 35px;
    background: rgb(2, 113, 227);

    .van-icon {
      color: #fff;
    }

    .van-nav-bar__title {
      font-size: 17px;
      color: #fff;
      font-family: simplified;
    }
  }

  .tab_box {
    width: 100%;
    overflow-x: auto;

    .tab_scroll {
      display: flex;
      flex-direction: row;
      padding: 0 12px;

      .tab_item {
        flex-shrink: 0;
        padding: 0 10px;
        width: 60px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border-radius: 8px;
        color: #333;
        text-align: center;
        transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        margin-right: 12px;
        box-shadow: 0 2px 8px rgba(2, 113, 227, 0.04);
        font-size: 14px;
      }

      .tab_item.active {
        background: #0271e3;
        color: #fff;
        box-shadow: 0 4px 16px rgba(2, 113, 227, 0.10);
      }

      .tab_item:last-child {
        margin-right: 0;
      }
    }
  }

  .tab_content {
    margin: 12px;
  }
}
</style>
