import { tabbarStore } from './modules/tabbar'
import {
  createStore
} from 'vuex'
import api from '@/api'
export default createStore({
  state: {
    speechShow: false,
    speechStauts: false,
    speechContent: '测试',
    onlinePoliticalDiscussionRedDots: 0,
    ModuleRedDotsNumber1: 0,
    ModuleRedDotsNumber2: 0,
    ModuleRedDotsNumber3: 0,
    ModuleRedDotsNumber4: 0,
    ModuleRedDotsNumber5: 0,
    ModuleRedDotsNumber6: 0,
    newsSpecialsubjects: [],
    dataList: []
  },
  mutations: {
    setStatus (state, stauts) {
      state.speechStauts = stauts
    },
    setSpeechContent (state, content) {
      state.speechContent = content
    },
    setSpeechShow (state, show) {
      state.speechShow = show
    },
    setOnlinePoliticalDiscussionRedDotsNumber (state, num) {
      state.onlinePoliticalDiscussionRedDots = num
    },
    setModuleRedDotsNumber1 (state, num) {
      state.ModuleRedDotsNumber1 = num
    },
    setModuleRedDotsNumber2 (state, num) {
      state.ModuleRedDotsNumber2 = num
    },
    setModuleRedDotsNumber3 (state, num) {
      state.ModuleRedDotsNumber3 = num
    },
    setModuleRedDotsNumber4 (state, num) {
      state.ModuleRedDotsNumber4 = num
    },
    setModuleRedDotsNumber5 (state, num) {
      state.ModuleRedDotsNumber5 = num
    },
    setModuleRedDotsNumber6 (state, num) {
      state.ModuleRedDotsNumber6 = num
    },
    setNewsSpecialsubjects (state, data) {
      state.newsSpecialsubjects = data
    },
    setDataList (state, data) {
      state.dataList = data
    }
  },
  actions: {
    async fetchNewsSpecialsubjects ({ commit }) {
      const ret = await api.news.getSpecialsubjectLists({
        pageNo: 1,
        pageSize: 100,
        module: 1,
        isPublish: 1
      })
      if (ret.data && ret.data.length !== 0) {
        ret.data = ret.data.filter(item => item.title !== '我为红色胶东代言' && item.title !== '倾听与商量' && item.title !== '优秀提案背后的故事')
        ret.data.forEach(element => {
          var image = element.image || {}
          element.url = element.coverImg || image.themeImg || ''
          element.class = element.infoClass || ''
          element.relateType = '32'
        })
        commit('setNewsSpecialsubjects', ret.data)
      } else {
        commit('setNewsSpecialsubjects', [])
      }
    },
    async getSpecialsubjectRelateinfoList ({ commit }, params) {
      const ret = await api.news.getSpecialsubjectRelateinfoList({
        pageNo: 1,
        pageSize: 8,
        subjectId: params.subjectId,
        columnId: params.columnId,
        isPublish: 1
      })
      if (ret) {
        var list = ret.data || []
        commit('dataList', [])
        list.forEach(item => {
          item.type = item.infoType || ''
          item.class = item.infoClass || ''
          item.id = item.relateRecordId || item.id || ''
          item.title = item.title || ''
          var image = item.image || {}
          item.url = item.leftImage || image.fullUrl || ''
          item.source = item.source || ''
          item.createBy = item.createBy || ''
          item.isTop = item.isTop || '0'
          item.externalLinks = item.externalLinks || ''
          item.browerCount = item.browseCount || '0'
          item.commentCount = item.commentCount || '0'
          item.isRead = item.isRead ? '1' : '0'
          item.time = item.publishDate || item.createDate || ''
        })
        commit('setDataList', list)
      }
    }
  },
  modules: {
    tabbarStore
  }
})
