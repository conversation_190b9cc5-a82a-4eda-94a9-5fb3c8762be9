<template>
  <div class="WorkNoticeDetails">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="noticeDetailsBox">
        <div class="noticeTitle">{{ details.title }}</div>
        <div class="noticeInfo">
          <div>{{ details.publishDate }}</div>
          <div>{{ details.publishDept }}</div>
        </div>
        <div class="noticeContent" @click="setImgBigger" v-html="details.content"></div>
        <template v-if="attachInfo.data && attachInfo.data.length != 0">
          <div class="general_attach" style="background-color: #fff;">
            <div v-for="(item, index) in attachInfo.data" :key="index"
              class="general_attach_item flex_box flex_align_center click" @click="annexClick(item, false)">
              <img class="general_attach_icon" :style="$general.loadConfigurationSize([5, 7])"
                :src="require(`../../../assets/fileicon/${item.iconInfo.name}`)" />
              <div class="flex_placeholder flex_box flex_align_center">
                <div class="general_attach_name text_one2" style="font-size: 14px;display: -webkit-box;">{{ item.name }}
                </div>
                <div class="general_attach_size" style="font-size: 12px;">{{ $general.getFileSize(item.size) }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'WorkNoticeDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false,
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        noticeInfo()
      }, 520)
    }
    const annexClick = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.host === '*************') {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://*************/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        } else {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://www.cszysoft.com/appShare/qdzx/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        }
      } else {
        var param = {
          id: item.id,
          url: item.url,
          name: item.name
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    // 详情请求
    const noticeInfo = async () => {
      const res = await $api.ImportantWork.info(data.id)
      var { data: details } = res
      data.details = details
      data.attachInfo.data = []
      var attachmentList = data.details.fileListVo || []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.id = _eItem.id || ''
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          data.attachInfo.data.push(item)
        })
      }
      if (!details.content && data.attachInfo.data.length > 0) {
        annexClick(data.attachInfo.data[0])
      }
      data.refreshing = false
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.noticeContent img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview({ images: img, startPosition: nowIndex, closeable: true })
      }
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), $general, onRefresh, setImgBigger, annexClick, onClickLeft }
  }
}
</script>
<style lang="less">
.WorkNoticeDetails {
  width: 100%;
  min-height: 100%;
  background: #eee;

  .noticeDetailsBox {
    width: 100%;
    height: 800px;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 10px;

    .noticeTitle {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }

    .noticeInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 0;

      div {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }

    .noticeContent {
      width: 100%;
      line-height: 30px;

      img {
        width: 100%;
      }
    }
  }

  .readUserBox {
    padding: 16px;
    padding-bottom: 52px;
    background-color: #fff;

    .number {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 20px;
      color: #333333;
      position: relative;
      padding-left: 6px;
      margin-bottom: 6px;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #3088fe;
        opacity: 1;
        border-radius: 10px;
      }
    }

    .readUser {
      overflow: hidden;

      span {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 20px;
        color: #666666;
      }
    }

    .viewMore {
      font-size: 10px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 14px;
      color: #3088fe;
      text-align: right;
    }
  }

  .receipt {
    position: fixed;
    top: 68%;
    right: 16px;
    width: 44px;
    height: 44px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 44px;
    color: #ffffff;
    text-align: center;
  }

  .van-popup {
    .van-icon-cross {
      font-size: 16px;
    }

    .noticeTitlePopup {
      height: 44px;
      line-height: 40px;
      padding-top: 4px;
      text-align: center;
      position: relative;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 343px;
        height: 0px;
        border-bottom: 1px solid #999999;
        opacity: 0.2;
      }
    }

    .van-field__body {
      font-size: 14px;
    }

    .van-radio-group {
      padding: 16px;

      .van-radio {
        padding: 6px;
      }

      .van-radio__label {
        font-size: 14px;
      }
    }

    .van-checkbox-group {
      padding: 16px;

      .van-checkbox {
        padding: 6px;
      }

      .van-checkbox__label {
        font-size: 14px;
      }
    }

    .noticeButton {
      width: 100%;
      padding-top: 52px;
      padding-bottom: 82px;
      display: flex;
      justify-content: center;

      .van-button {
        width: 82px;
      }

      .van-button__text {
        font-size: 14px;
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
