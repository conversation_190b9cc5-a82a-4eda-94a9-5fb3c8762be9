.zxBookmain {
    width: 100%;
    overflow-y: auto;

    // .topColor {
    //     width: 100%;
    //     height: 40px;
    //     background: #9CD4F8;
    // }
    .topImg {
        width: 100%;
        height: 200px;
        position: relative;

        .bgimg {
            width: 100%;
            height: 100%;
        }

        .topback {
            position: absolute;
            width: 10px;
            height: 17px;
            left: 15px;
            top: 20px;
        }
    }

    .margintop15 {
        margin-top: 15px;
    }

    .centerbook {
        justify-content: space-evenly;

        .centerbook-box {
            background: -webkit-linear-gradient(left, #FFFFFF, #DBF1FF, #9CD4F8);
            /* Safari 5.1 - 6.0 */
            background: -o-linear-gradient(right, #FFFFFF, #DBF1FF, #9CD4F8);
            /* Opera 11.1 - 12.0 */
            background: -moz-linear-gradient(right, #FFFFFF, #DBF1FF, #9CD4F8);
            /* Firefox 3.6 - 15 */
            background: linear-gradient(to right, #FFFFFF, #DBF1FF, #9CD4F8);
            /* 标准的语法 */
            width: 164px;
            height: 72px;
            padding-left: 24px;
            padding-top: 17px;
            position: relative;

            .text {
                font-family: 'PingFang';
                font-size: 14px;
                color: #333333;
                font-weight: 1000;
                margin-bottom: 2px;
            }

            .text2 {
                font-family: 'PingFang';
                font-size: 10px;
                color: #8498AA;
            }

            .yx-all {
                background: #ffffff;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                position: absolute;
                right: 24px;
                top: 16px;
                line-height: 50px;
                text-align: center;

                >img {
                    width: 21px;
                    height: 21px;
                }
            }

            .bookimg {
                position: absolute;
                width: 26px;
                height: 26px;
                right: 20px;
                top: 25px;
            }
        }
    }

    .centerwy {
        border-radius: 76px;
        background: #ffffff;
        width: 345px;
        height: 74px;
        margin: 0 auto;
        margin-top: 15px;

        .font {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-left: 12px;

            .text {
                font-family: 'PingFang';
                font-size: 14px;
                color: #333333;
                font-weight: 1000;
                margin-bottom: 5px;

                .yx-number {
                    width: 28px;
                    height: 18px;
                    line-height: 18px;
                    border-radius: 19px;
                    background: #DD4C4C;
                    color: #ffffff;
                    text-align: center;
                    font-size: 10px;
                    margin-left: 5px;
                }
            }

            .text2 {
                font-family: 'PingFang';
                font-size: 12px;
                color: #8498AA;
            }
        }

        .yx-all-wy {
            background: #D6EFFF;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            margin-left: 10px;
            line-height: 65px;
            text-align: center;

            >img {
                width: 27px;
                height: 27px;
            }
        }
    }

    .yx-10 {
        border-radius: 10px;
    }

    .flexwarp {
        flex-wrap: wrap;
    }

    #view {
        position: fixed;
        bottom: 70px;
        z-index: 9999;
        right: 50px;
    }
}