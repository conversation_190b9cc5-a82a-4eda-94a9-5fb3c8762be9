<template>
  <div class="activityNew">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.meetName"
                   required
                   name="标题"
                   show-word-limit
                   label="标题"
                   placeholder="请输入标题"
                   :rules="rules.meetName" />
        <van-field v-model="form.spokesman"
                   name="提交人"
                   label="提交人"
                   disabled
                   placeholder="请输入提交人" />
        <van-field v-model="form.spokesman"
                   name="加分委员"
                   label="加分委员"
                   disabled
                   placeholder="请输入加分委员" />
        <van-field size="large"
                   class="time-box"
                   input-align="right"
                   label="提交时间"
                   readonly
                   v-model="form.signBeginTime"
                   placeholder="请选择时间"
                   :rules="rules.signBeginTime"
                   required
                   @click="openTimeFn()"
                   right-icon="clock-o" />
        <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="正文"
                   rows="6"
                   maxlength="2000"
                   show-word-limit
                   type="textarea"
                   placeholder="请输入正文"
                   :rules="rules.content" />
      </van-cell-group>
      <div class="newButton">
        <div class="flex_placeholder">
          <van-button round
                      size="large"
                      color="#3088FE"
                      native-type="submit">
            提交</van-button>
        </div>
      </div>
    </van-form>

    <van-popup v-model:show="signBeginTimeShow"
               position="bottom">
      <van-datetime-picker v-model="signBeginTime"
                           title="选择时间"
                           :formatter="formatter"
                           type='datetime'
                           @confirm="signBeginTimeConfirm(signBeginTime)"
                           @cancel="signBeginTimeShow = false">
      </van-datetime-picker>
    </van-popup>
  </div>
</template>
<script>
import { onMounted, reactive, ref, toRefs, inject } from 'vue'
import { DatetimePicker, DropdownMenu, DropdownItem, Toast, Dialog } from 'vant'
import { useRouter, useRoute } from 'vue-router'
export default {
  name: 'activityNew',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem

  },
  setup () {
    const route = useRoute()
    const dayjs = require('dayjs')
    const router = useRouter()
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      form: {
        typeSmall: '', // 会议名称
        meetName: '', // 主题
        content: '', // 内容
        signBeginTime: '', // 时间
        spokesman: '' // 发言人
      },
      id: route.query.id,
      rules: {
        meetName: [{ required: true, message: '请输入标题' }],
        signBeginTime: [{ required: true, message: '请选择时间' }],
        content: [{ required: true, message: '请输入内容' }]
      },
      typeSmall: [],
      signBeginTimeShow: false,
      signBeginTime: ''
    })
    const $api = inject('$api')
    const user = ref(null)

    onMounted(() => {
      data.form.spokesman = data.user.userName
      data.form.signBeginTime = dayjs().format('YYYY-MM-DD HH:mm')
    })
    // 提交
    const onSubmit = async (values) => {
      var datas = data.form
      const res = await $api.memberReport.MemberReportAdd({
        empty: '1',
        isCheck: 1,
        awardedMarksMember: data.user.id,
        title: datas.meetName,
        submissionTime: datas.signBeginTime,
        content: datas.content
      })
      console.log(res)
      var { errcode: errcodes, errmsg: errmsgs } = res
      if (errcodes === 200) {
        Toast.success(errmsgs)
        router.go(-1)
      }
    }
    // 打开时间控件
    const signBeginTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.signBeginTime = new Date(_item.value.replace(/-/g, '/'))
      data.form.signBeginTime = _item.value
      data.signBeginTimeShow = false
    }
    // 点击打开时间  没有初始时间时 设置当前时间
    const openTimeFn = () => {
      if (!data.signBeginTime) {
        data.signBeginTime = new Date()
      } else {
        data.signBeginTime = new Date(data.form.signBeginTime.replace(/-/g, '/'))
      }
      console.log('data.signBeginTime', data.signBeginTime)
      data.signBeginTimeShow = true
    }
    // 格式化时间
    const formatter = (type, value) => {
      if (type === 'year') {
        return value + '年'
      } else if (type === 'month') {
        return value + '月'
      } else if (type === 'day') {
        return value + '日'
      } else if (type === 'hour') {
        return value + '时'
      } else if (type === 'minute') {
        return value + '分'
      }
      return value
    }
    return { ...toRefs(data), $general, user, onSubmit, close, signBeginTimeConfirm, formatter, openTimeFn }
  }
}
</script>
<style lang="less" >
.activityNew {
  background: #fff;
  .van-form {
    width: 100%;
    .van-cell-group--inset {
      margin: 0;
      .van-field {
        margin: 20px 0;
      }
    }
    .newContent {
      flex-wrap: wrap;
      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }
      .van-field__body {
        background-color: #f5f5f5;
        padding: 6px 12px;
      }
    }
    .van-field__control {
      font-size: 14px;
    }
    .time-box {
      .van-cell__title {
        width: 120px;
      }
    }
    .activity-box {
      .van-cell__title {
        width: 220px !important;
      }
    }
  }
  .titleStyle {
    background-color: #fff;
    height: 48px;
    // width: 100%;
    width: 50%;
  }
  .van-dropdown-menu__title {
    margin-right: 15px;
  }
  --van-dropdown-menu-box-shadow: 0;
  .marginTop {
    margin-top: 20px;
  }
  .newButton {
    display: flex;
    justify-content: space-around;
    padding: 36px 18px;
    // padding-bottom: 88px;
    .van-button {
      // width: 128px;
      // height: 36px;
    }
  }
}
</style>>
