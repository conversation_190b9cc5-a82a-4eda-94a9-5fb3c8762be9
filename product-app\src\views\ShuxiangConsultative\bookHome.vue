<template>
  <div class="home">
    <router-view />
    <van-tabbar v-model="active"
                :active-color="appTheme"
                @change="onChange">
      <van-tabbar-item :icon="active==item.id?item.selectIconUrl:item.iconUrl"
                       v-for="item in tabbarList"
                       :key="item.id"
                       :to="item.infoUrl"
                       :badge="item.pointNumber>0?item.pointNumber:''"
                       :name="item.id">{{item.name}}</van-tabbar-item>
    </van-tabbar>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
export default {
  name: 'bookHome',
  setup () {
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const homePageImg11 = require('../../assets/img/11.png')
    const homePageImg12 = require('../../assets/img/12.png')
    const homePageImg21 = require('../../assets/img/21.png')
    const homePageImg22 = require('../../assets/img/22.png')
    const router = useRouter()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      tabbarList: [
        { id: '335633278637703168', name: '阅读', iconUrl: homePageImg12, selectIconUrl: homePageImg11, infoUrl: '/recommendHome', pointNumber: 0 },
        { id: '335633278637703169', name: '书库', iconUrl: homePageImg12, selectIconUrl: homePageImg11, infoUrl: '/librarys', pointNumber: 0 },
        { id: '335633485739851776', name: '书桌', iconUrl: homePageImg22, selectIconUrl: homePageImg21, infoUrl: '/bookDeskHome', pointNumber: 0 }
      ]
    })
    onMounted(() => {
      const historyIndex = sessionStorage.getItem('bookhistoryIndex') || ''
      if (historyIndex) {
        data.active = historyIndex
        data.tabbarList.forEach((element, index) => {
          if (element.id === data.active) {
            router.replace({ path: data.tabbarList[index].infoUrl })
          }
        })
      } else {
        data.active = data.tabbarList[0].id
        router.replace({ path: data.tabbarList[0].infoUrl })
      }
      // init()
      // appList()
    })

    const onChange = (index) => {
      sessionStorage.setItem('bookhistoryIndex', index)
    }
    return { ...toRefs(data), onChange }
  }
}
</script>
<style lang="less">
.home {
  width: 100%;
  .van-tabbar {
    .van-tabbar-item__text {
      font-size: 10px;
      line-height: 12px;
    }
  }
}
</style>
