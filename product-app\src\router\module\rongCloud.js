const msgList = () => import('@/views/rongCloud/msgList/msgList')
const chatRoom = () => import('@/views/rongCloud/chatRoom/chatRoom')
const forward = () => import('@/views/rongCloud/forward/forward')
const chatSetting = () => import('@/views/rongCloud/chatSetting/chatSetting')
const groupSayEdit = () => import('@/views/rongCloud/chatSetting/groupSayEdit')
const memberList = () => import('@/views/rongCloud/chatSetting/memberList')
const groupList = () => import('@/views/rongCloud/groupList/groupList')
const addressBook = () => import('@/views/rongCloud/addressBook/addressBook')
const personData = () => import('@/views/rongCloud/personData/personData')
const addGroupBook = () => import('@/views/rongCloud/addGroupBook/addGroupBook')
const chatFile = () => import('@/views/rongCloud/chatFile/chatFile')

const rongCloud = [{
  path: '/msgList',
  name: 'msgList',
  component: msgList,
  meta: {
    title: '消息列表',
    keepAlive: true
  }
}, {
  path: '/forward',
  name: 'forward',
  component: forward,
  meta: {
    title: '转发',
    keepAlive: true
  }
}, {
  path: '/chatSetting',
  name: 'chatSetting',
  component: chatSetting,
  meta: {
    title: '聊天设置',
    keepAlive: true
  }
}, {
  path: '/groupSayEdit',
  name: 'groupSayEdit',
  component: groupSayEdit,
  meta: {
    title: '群公告',
    keepAlive: true
  }
}, {
  path: '/memberList',
  name: 'memberList',
  component: memberList,
  meta: {
    title: '群成员列表',
    keepAlive: true
  }
}, {
  path: '/chatRoom',
  name: 'chatRoom',
  component: chatRoom,
  meta: {
    title: '聊天室',
    keepAlive: true
  }
}, {
  path: '/groupList',
  name: 'groupList',
  component: groupList,
  meta: {
    title: '群组列表',
    keepAlive: true
  }
}, {
  path: '/addressBook',
  name: 'addressBook',
  component: addressBook,
  meta: {
    title: '通讯录',
    keepAlive: true
  }
}, {
  path: '/personData',
  name: 'personData',
  component: personData,
  meta: {
    title: '用户详情',
    keepAlive: true
  }
}, {
  path: '/addGroupBook',
  name: 'addGroupBook',
  component: addGroupBook,
  meta: {
    title: '添加群书籍',
    keepAlive: true
  }
}, {
  path: '/chatFile',
  name: 'chatFile',
  component: chatFile,
  meta: {
    title: '群文件',
    keepAlive: true
  }
}]
export default rongCloud
