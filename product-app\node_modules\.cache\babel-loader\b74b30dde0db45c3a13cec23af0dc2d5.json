{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=template&id=4b900e74", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755082716031}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUJsb2NrIGFzIF9jcmVhdGVCbG9jaywgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIHdpdGhDdHggYXMgX3dpdGhDdHgsIGNyZWF0ZVZOb2RlIGFzIF9jcmVhdGVWTm9kZSwgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jaywgRnJhZ21lbnQgYXMgX0ZyYWdtZW50IH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBjbGFzczogImF0dHJhY3RJbnZlc3RtZW50RGV0YWlsc0NvcHkiCn07CmNvbnN0IF9ob2lzdGVkXzIgPSB7CiAgY2xhc3M6ICJkZXRhaWxzLWNvbnRhaW5lciIKfTsKY29uc3QgX2hvaXN0ZWRfMyA9IHsKICBjbGFzczogInRpdGxlIgp9Owpjb25zdCBfaG9pc3RlZF80ID0gewogIGNsYXNzOiAiaGVhZGVyLWluZm8iCn07CmNvbnN0IF9ob2lzdGVkXzUgPSB7CiAgY2xhc3M6ICJkYXRlIgp9Owpjb25zdCBfaG9pc3RlZF82ID0gewogIGNsYXNzOiAidGFnIgp9Owpjb25zdCBfaG9pc3RlZF83ID0gewogIGNsYXNzOiAiaW5mby1zZWN0aW9uIgp9Owpjb25zdCBfaG9pc3RlZF84ID0gewogIGtleTogMCwKICBjbGFzczogImluZm8taXRlbSIKfTsKY29uc3QgX2hvaXN0ZWRfOSA9IHsKICBjbGFzczogInZhbHVlIgp9Owpjb25zdCBfaG9pc3RlZF8xMCA9IHsKICBrZXk6IDEsCiAgY2xhc3M6ICJpbmZvLWl0ZW0iCn07CmNvbnN0IF9ob2lzdGVkXzExID0gewogIGNsYXNzOiAidmFsdWUiCn07CmNvbnN0IF9ob2lzdGVkXzEyID0gewogIGtleTogMiwKICBjbGFzczogImluZm8taXRlbSIKfTsKY29uc3QgX2hvaXN0ZWRfMTMgPSB7CiAgY2xhc3M6ICJ2YWx1ZSIKfTsKY29uc3QgX2hvaXN0ZWRfMTQgPSB7CiAga2V5OiAzLAogIGNsYXNzOiAiaW5mby1pdGVtIgp9Owpjb25zdCBfaG9pc3RlZF8xNSA9IHsKICBjbGFzczogInZhbHVlIgp9Owpjb25zdCBfaG9pc3RlZF8xNiA9IHsKICBrZXk6IDQsCiAgY2xhc3M6ICJpbmZvLWl0ZW0iCn07CmNvbnN0IF9ob2lzdGVkXzE3ID0gewogIGNsYXNzOiAidmFsdWUiCn07CmNvbnN0IF9ob2lzdGVkXzE4ID0gewogIGtleTogNSwKICBjbGFzczogImluZm8taXRlbSIKfTsKY29uc3QgX2hvaXN0ZWRfMTkgPSB7CiAgY2xhc3M6ICJ2YWx1ZSIKfTsKY29uc3QgX2hvaXN0ZWRfMjAgPSB7CiAga2V5OiA2LAogIGNsYXNzOiAiaW5mby1pdGVtIgp9Owpjb25zdCBfaG9pc3RlZF8yMSA9IFsiaW5uZXJIVE1MIl07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF92YW5fbmF2X2JhciA9IF9yZXNvbHZlQ29tcG9uZW50KCJ2YW4tbmF2LWJhciIpOwogIGNvbnN0IF9jb21wb25lbnRfdmFuX3N0aWNreSA9IF9yZXNvbHZlQ29tcG9uZW50KCJ2YW4tc3RpY2t5Iik7CiAgY29uc3QgX2NvbXBvbmVudF92YW5fcHVsbF9yZWZyZXNoID0gX3Jlc29sdmVDb21wb25lbnQoInZhbi1wdWxsLXJlZnJlc2giKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfdmFuX3N0aWNreSwgbnVsbCwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jdHguaGFzQXBpID8gKF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfdmFuX25hdl9iYXIsIHsKICAgICAga2V5OiAwLAogICAgICB0aXRsZTogIuivpuaDhSIsCiAgICAgICJsZWZ0LXRleHQiOiAiIiwKICAgICAgImxlZnQtYXJyb3ciOiAiIiwKICAgICAgb25DbGlja0xlZnQ6ICRzZXR1cC5vbkNsaWNrTGVmdAogICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJvbkNsaWNrTGVmdCJdKSkgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSldKSwKICAgIF86IDEgLyogU1RBQkxFICovCiAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X3Zhbl9wdWxsX3JlZnJlc2gsIHsKICAgIG1vZGVsVmFsdWU6IF9jdHgucmVmcmVzaGluZywKICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSAkZXZlbnQgPT4gX2N0eC5yZWZyZXNoaW5nID0gJGV2ZW50KSwKICAgIG9uUmVmcmVzaDogJHNldHVwLm9uUmVmcmVzaAogIH0sIHsKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8yLCBbX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOagh+mimCAiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMywgX3RvRGlzcGxheVN0cmluZyhfY3R4LmRldGFpbHMudGl0bGUgfHwgJ+Wtn+W6huaWjOS4u+W4reWcqOaIkOmDveW4guaLnOiuv+a4heWNjuWbm+W3neiDvea6kOS6kuiBlOe9keeglOeptumZoicpLCAxIC8qIFRFWFQgKi8pLCBfY3JlYXRlQ29tbWVudFZOb2RlKCIg5pel5pyf5ZKM5qCH562+ICIpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF80LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNSwgX3RvRGlzcGxheVN0cmluZygkc2V0dXAuZm9ybWF0RGF0ZShfY3R4LmRldGFpbHMucHVibGlzaERhdGUpIHx8ICcyMDI1LTA0LTAyJyksIDEgLyogVEVYVCAqLyksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzYsIF90b0Rpc3BsYXlTdHJpbmcoJHNldHVwLmdldFR5cGVUZXh0KF9jdHguZGV0YWlscy50eXBlKSB8fCAn5aSW5Ye65ouc6K6/JyksIDEgLyogVEVYVCAqLyldKSwgX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOivpuaDheS/oeaBryAiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNywgW19jdHguZGV0YWlscy5wdWJsaXNoRGVwdCA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzgsIFtfY2FjaGVbMV0gfHwgKF9jYWNoZVsxXSA9IF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJsYWJlbCIKICAgIH0sICLlnLDngrnvvJoiLCAtMSAvKiBIT0lTVEVEICovKSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzksIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC5kZXRhaWxzLnB1Ymxpc2hEZXB0KSwgMSAvKiBURVhUICovKV0pKSA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKSwgX2N0eC5kZXRhaWxzLnByb2plY3RXYXkgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xMCwgW19jYWNoZVsyXSB8fCAoX2NhY2hlWzJdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImxhYmVsIgogICAgfSwgIuW4pumYn+mihuWvvOWnk+WQjeWPiuiBjOWKoe+8miIsIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMTEsIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC5kZXRhaWxzLnByb2plY3RXYXkpLCAxIC8qIFRFWFQgKi8pXSkpIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpLCBfY3R4LmRldGFpbHMuY29tcGFueU51bWJlciA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzEyLCBbX2NhY2hlWzNdIHx8IChfY2FjaGVbM10gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5LyB5Lia5bqP5Y+377yaIiwgLTEgLyogSE9JU1RFRCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xMywgX3RvRGlzcGxheVN0cmluZyhfY3R4LmRldGFpbHMuY29tcGFueU51bWJlciksIDEgLyogVEVYVCAqLyldKSkgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSksIF9jdHguZGV0YWlscy5wcm9qZWN0QXJlYU5hbWUgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xNCwgW19jYWNoZVs0XSB8fCAoX2NhY2hlWzRdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImxhYmVsIgogICAgfSwgIuaLnOiuv+S8geS4muWQjeensO+8miIsIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMTUsIF90b0Rpc3BsYXlTdHJpbmcoX2N0eC5kZXRhaWxzLnByb2plY3RBcmVhTmFtZSksIDEgLyogVEVYVCAqLyldKSkgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSksIF9jdHguZGV0YWlscy5wcm9qZWN0U3VnZ2VzdGlvbiA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzE2LCBbX2NhY2hlWzVdIHx8IChfY2FjaGVbNV0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5rS96LCI6aG555uu5ZCN56ew77yaIiwgLTEgLyogSE9JU1RFRCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xNywgX3RvRGlzcGxheVN0cmluZyhfY3R4LmRldGFpbHMucHJvamVjdFN1Z2dlc3Rpb24pLCAxIC8qIFRFWFQgKi8pXSkpIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpLCBfY3R4LmRldGFpbHMucHJvamVjdEFwcGVhbCA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soImRpdiIsIF9ob2lzdGVkXzE4LCBbX2NhY2hlWzZdIHx8IChfY2FjaGVbNl0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5rS96LCI6aG555uu5Lu35YC877yaIiwgLTEgLyogSE9JU1RFRCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF8xOSwgX3RvRGlzcGxheVN0cmluZyhfY3R4LmRldGFpbHMucHJvamVjdEFwcGVhbCksIDEgLyogVEVYVCAqLyldKSkgOiBfY3JlYXRlQ29tbWVudFZOb2RlKCJ2LWlmIiwgdHJ1ZSksIF9jdHguZGV0YWlscy5idXNpbmVzc05hbWUgPyAoX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8yMCwgW19jYWNoZVs3XSB8fCAoX2NhY2hlWzddID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImxhYmVsIgogICAgfSwgIua0veiwiOaDheWGte+8miIsIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogInZhbHVlIGNvbnRlbnQtdGV4dCIsCiAgICAgIGlubmVySFRNTDogX2N0eC5kZXRhaWxzLmJ1c2luZXNzTmFtZQogICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfMjEpXSkpIDogX2NyZWF0ZUNvbW1lbnRWTm9kZSgidi1pZiIsIHRydWUpLCBfY3JlYXRlQ29tbWVudFZOb2RlKCIg5aaC5p6c5rKh5pyJ5pWw5o2u77yM5pi+56S66buY6K6k5YaF5a65ICIpLCAhX2N0eC5kZXRhaWxzLnB1Ymxpc2hEZXB0ICYmICFfY3R4LmRldGFpbHMucHJvamVjdFdheSAmJiAhX2N0eC5kZXRhaWxzLnByb2plY3RBcmVhTmFtZSA/IChfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCB7CiAgICAgIGtleTogNwogICAgfSwgW19jYWNoZVs4XSB8fCAoX2NhY2hlWzhdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImluZm8taXRlbSIKICAgIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5Zyw54K577yaIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJ2YWx1ZSIKICAgIH0sICLljJfkuqwiKV0sIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NhY2hlWzldIHx8IChfY2FjaGVbOV0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAiaW5mby1pdGVtIgogICAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJsYWJlbCIKICAgIH0sICLluKbpmJ/pooblr7zlp5PlkI3lj4rogYzliqHvvJoiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogInZhbHVlIgogICAgfSwgIuWFtuS7lu+8jOW4guaUv+WNj+WJr+S4u+W4re+8jOW4guW3peWVhuiBlOS4u+W4rSIpXSwgLTEgLyogSE9JU1RFRCAqLykpLCBfY2FjaGVbMTBdIHx8IChfY2FjaGVbMTBdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImluZm8taXRlbSIKICAgIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5LyB5Lia5bqP5Y+377yaIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJ2YWx1ZSIKICAgIH0sICIxIildLCAtMSAvKiBIT0lTVEVEICovKSksIF9jYWNoZVsxMV0gfHwgKF9jYWNoZVsxMV0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAiaW5mby1pdGVtIgogICAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJsYWJlbCIKICAgIH0sICLmi5zorr/kvIHkuJrlkI3np7DvvJoiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogInZhbHVlIgogICAgfSwgIua4heWNjuWbm+W3neiDvea6kOS6kuiBlOe9keeglOeptumZoiIpXSwgLTEgLyogSE9JU1RFRCAqLykpLCBfY2FjaGVbMTJdIHx8IChfY2FjaGVbMTJdID0gX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImluZm8taXRlbSIKICAgIH0sIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAibGFiZWwiCiAgICB9LCAi5rS96LCI6aG555uu5ZCN56ew77yaIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJ2YWx1ZSIKICAgIH0sICLlu7rorr7kuIDkvZPpo57ooYzlubPlj7Dlj4rlj5HlsZXmuLjmiI/pobnnm64iKV0sIC0xIC8qIEhPSVNURUQgKi8pKSwgX2NhY2hlWzEzXSB8fCAoX2NhY2hlWzEzXSA9IF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJpbmZvLWl0ZW0iCiAgICB9LCBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogImxhYmVsIgogICAgfSwgIua0veiwiOmhueebruS7t+WAvO+8miIpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAidmFsdWUiCiAgICB9LCAi5YKo5aSH5Zyo6LCIIildLCAtMSAvKiBIT0lTVEVEICovKSksIF9jYWNoZVsxNF0gfHwgKF9jYWNoZVsxNF0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICAgIGNsYXNzOiAiaW5mby1pdGVtIgogICAgfSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgICAgY2xhc3M6ICJsYWJlbCIKICAgIH0sICLmtL3osIjmg4XlhrXvvJoiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgewogICAgICBjbGFzczogInZhbHVlIGNvbnRlbnQtdGV4dCIKICAgIH0sICIg6YeN54K55Zu057uV5L2O56m657uP5rWO55Sf5oCB5omT6YCg77yM5Zyo6Z2S5bKb5Zyw5Yy65byA5bGV6aOe6KGM5bmz5Y+w5Y+K5Y+R5bGV5ri45oiP6aG555uu562J5pa56Z2i6L+b6KGM5LqG5rex5YWl5Lqk5rWB44CC5Y+M5pa55bCG5oyB57ut6aOe6KGM57O757uf562J6YOo5YiG5Lqn5ZOB77yM5o6o5Yqo6aG555uu6JC95Zyw44CC5pyA57uI77yM5Y+M5pa55bCx6Z2S5bKb5L2O56m657uP5rWO5bqU55So6YCa6YGTIildLCAtMSAvKiBIT0lTVEVEICovKSldLCA2NCAvKiBTVEFCTEVfRlJBR01FTlQgKi8pKSA6IF9jcmVhdGVDb21tZW50Vk5vZGUoInYtaWYiLCB0cnVlKV0pXSldKSwKICAgIF86IDEgLyogU1RBQkxFICovCiAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIiwgIm9uUmVmcmVzaCJdKV0pOwp9"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_sticky", "_ctx", "has<PERSON><PERSON>", "_createBlock", "_component_van_nav_bar", "title", "onClickLeft", "$setup", "_component_van_pull_refresh", "refreshing", "$event", "onRefresh", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_toDisplayString", "details", "_hoisted_4", "_hoisted_5", "formatDate", "publishDate", "_hoisted_6", "getTypeText", "type", "_hoisted_7", "publishDept", "_hoisted_8", "_hoisted_9", "projectWay", "_hoisted_10", "_hoisted_11", "companyNumber", "_hoisted_12", "_hoisted_13", "projectAreaName", "_hoisted_14", "_hoisted_15", "projectSuggestion", "_hoisted_16", "_hoisted_17", "projectAppeal", "_hoisted_18", "_hoisted_19", "businessName", "_hoisted_20", "innerHTML", "_Fragment", "key"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" title=\"详情\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n      <div class=\"details-container\">\r\n        <!-- 标题 -->\r\n        <div class=\"title\">{{ details.title || '孟庆斌主席在成都市拜访清华四川能源互联网研究院' }}</div>\r\n\r\n        <!-- 日期和标签 -->\r\n        <div class=\"header-info\">\r\n          <div class=\"date\">{{ formatDate(details.publishDate) || '2025-04-02' }}</div>\r\n          <div class=\"tag\">{{ getTypeText(details.type) || '外出拜访' }}</div>\r\n        </div>\r\n\r\n        <!-- 详情信息 -->\r\n        <div class=\"info-section\">\r\n          <div class=\"info-item\" v-if=\"details.publishDept\">\r\n            <div class=\"label\">地点：</div>\r\n            <div class=\"value\">{{ details.publishDept }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectWay\">\r\n            <div class=\"label\">带队领导姓名及职务：</div>\r\n            <div class=\"value\">{{ details.projectWay }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.companyNumber\">\r\n            <div class=\"label\">企业序号：</div>\r\n            <div class=\"value\">{{ details.companyNumber }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAreaName\">\r\n            <div class=\"label\">拜访企业名称：</div>\r\n            <div class=\"value\">{{ details.projectAreaName }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectSuggestion\">\r\n            <div class=\"label\">洽谈项目名称：</div>\r\n            <div class=\"value\">{{ details.projectSuggestion }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAppeal\">\r\n            <div class=\"label\">洽谈项目价值：</div>\r\n            <div class=\"value\">{{ details.projectAppeal }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.businessName\">\r\n            <div class=\"label\">洽谈情况：</div>\r\n            <div class=\"value content-text\" v-html=\"details.businessName\"></div>\r\n          </div>\r\n\r\n          <!-- 如果没有数据，显示默认内容 -->\r\n          <template v-if=\"!details.publishDept && !details.projectWay && !details.projectAreaName\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">地点：</div>\r\n              <div class=\"value\">北京</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">带队领导姓名及职务：</div>\r\n              <div class=\"value\">其他，市政协副主席，市工商联主席</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">企业序号：</div>\r\n              <div class=\"value\">1</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">拜访企业名称：</div>\r\n              <div class=\"value\">清华四川能源互联网研究院</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目名称：</div>\r\n              <div class=\"value\">建设一体飞行平台及发展游戏项目</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目价值：</div>\r\n              <div class=\"value\">储备在谈</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈情况：</div>\r\n              <div class=\"value content-text\">\r\n                重点围绕低空经济生态打造，在青岛地区开展飞行平台及发展游戏项目等方面进行了深入交流。双方将持续飞行系统等部分产品，推动项目落地。最终，双方就青岛低空经济应用通道</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </van-pull-refresh>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { NavBar, Sticky, PullRefresh } from 'vant'\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [PullRefresh.name]: PullRefresh\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const $general = inject('$general')\r\n    const $api = inject('$api')\r\n    const $appTheme = inject('$appTheme')\r\n    const $isShowHead = inject('$isShowHead')\r\n\r\n    const data = reactive({\r\n      hasApi: false,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {},\r\n      refreshing: false\r\n    })\r\n\r\n    if (data.title) {\r\n      document.title = data.title\r\n    }\r\n\r\n    if (typeof (window.api) === 'undefined') {\r\n      data.hasApi = false\r\n    } else {\r\n      data.hasApi = true\r\n    }\r\n\r\n    onMounted(() => {\r\n      getInfo()\r\n    })\r\n\r\n    const onRefresh = () => {\r\n      setTimeout(() => {\r\n        getInfo()\r\n      }, 520)\r\n    }\r\n\r\n    // 详情请求\r\n    const getInfo = async () => {\r\n      try {\r\n        const res = await $api.ImportantWork.info(data.id)\r\n        var { data: details } = res\r\n        data.details = details\r\n        data.refreshing = false\r\n      } catch (error) {\r\n        console.error('获取详情失败:', error)\r\n        data.refreshing = false\r\n      }\r\n    }\r\n\r\n    // 格式化日期\r\n    const formatDate = (dateStr) => {\r\n      if (!dateStr) return ''\r\n      const date = new Date(dateStr)\r\n      return date.getFullYear() + '-' +\r\n        String(date.getMonth() + 1).padStart(2, '0') + '-' +\r\n        String(date.getDate()).padStart(2, '0')\r\n    }\r\n\r\n    // 获取类型文本\r\n    const getTypeText = (type) => {\r\n      const typeMap = {\r\n        1: '外出拜访',\r\n        2: '在青接待',\r\n        3: '自主举办'\r\n      }\r\n      return typeMap[type] || '外出拜访'\r\n    }\r\n\r\n    const onClickLeft = () => {\r\n      history.back()\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      $general,\r\n      onRefresh,\r\n      onClickLeft,\r\n      formatDate,\r\n      getTypeText\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n\r\n  .details-container {\r\n    padding: 20px;\r\n\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      margin-bottom: 16px;\r\n      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n    }\r\n\r\n    .header-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #F4F4F4;\r\n\r\n      .date {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n      }\r\n\r\n      .tag {\r\n        background: rgba(0, 122, 255, 0.12);\r\n        border: 1px solid #007AFF;\r\n        border-radius: 4px;\r\n        padding: 4px 8px;\r\n        font-size: 12px;\r\n        color: #007AFF;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .info-section {\r\n      .info-item {\r\n        margin-bottom: 20px;\r\n\r\n        .label {\r\n          font-size: 16px;\r\n          color: #999999;\r\n          font-weight: 600;\r\n          margin-bottom: 8px;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #333333;\r\n          font-weight: 600;\r\n          line-height: 1.5;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n\r\n          &.content-text {\r\n            line-height: 1.6;\r\n            font-weight: normal;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 导航栏样式\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA8B;;EAKhCA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAO;;EAGbA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAK;;EAIbA,KAAK,EAAC;AAAc;;;EAClBA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAO;;;EAGfA,KAAK,EAAC;;;;;;;uBA/CnBC,mBAAA,CA6FM,OA7FNC,UA6FM,GA5FJC,YAAA,CAEaC,qBAAA;sBADX,MAA0F,CAAvEC,IAAA,CAAAC,MAAM,I,cAAzBC,YAAA,CAA0FC,sBAAA;;MAA/DC,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,EAAE;MAAC,YAAU,EAAV,EAAU;MAAEC,WAAU,EAAEC,MAAA,CAAAD;;;MAE7EP,YAAA,CAwFmBS,2BAAA;gBAxFQP,IAAA,CAAAQ,UAAU;+DAAVR,IAAA,CAAAQ,UAAU,GAAAC,MAAA;IAAGC,SAAO,EAAEJ,MAAA,CAAAI;;sBAC/C,MAsFM,CAtFNC,mBAAA,CAsFM,OAtFNC,UAsFM,GArFJC,mBAAA,QAAW,EACXF,mBAAA,CAAyE,OAAzEG,UAAyE,EAAAC,gBAAA,CAAnDf,IAAA,CAAAgB,OAAO,CAACZ,KAAK,+CAEnCS,mBAAA,WAAc,EACdF,mBAAA,CAGM,OAHNM,UAGM,GAFJN,mBAAA,CAA6E,OAA7EO,UAA6E,EAAAH,gBAAA,CAAxDT,MAAA,CAAAa,UAAU,CAACnB,IAAA,CAAAgB,OAAO,CAACI,WAAW,mCACnDT,mBAAA,CAAgE,OAAhEU,UAAgE,EAAAN,gBAAA,CAA5CT,MAAA,CAAAgB,WAAW,CAACtB,IAAA,CAAAgB,OAAO,CAACO,IAAI,4B,GAG9CV,mBAAA,UAAa,EACbF,mBAAA,CA0EM,OA1ENa,UA0EM,GAzEyBxB,IAAA,CAAAgB,OAAO,CAACS,WAAW,I,cAAhD7B,mBAAA,CAGM,OAHN8B,UAGM,G,0BAFJf,mBAAA,CAA4B;MAAvBhB,KAAK,EAAC;IAAO,GAAC,KAAG,sBACtBgB,mBAAA,CAAkD,OAAlDgB,UAAkD,EAAAZ,gBAAA,CAA5Bf,IAAA,CAAAgB,OAAO,CAACS,WAAW,iB,wCAGdzB,IAAA,CAAAgB,OAAO,CAACY,UAAU,I,cAA/ChC,mBAAA,CAGM,OAHNiC,WAGM,G,0BAFJlB,mBAAA,CAAmC;MAA9BhB,KAAK,EAAC;IAAO,GAAC,YAAU,sBAC7BgB,mBAAA,CAAiD,OAAjDmB,WAAiD,EAAAf,gBAAA,CAA3Bf,IAAA,CAAAgB,OAAO,CAACY,UAAU,iB,wCAGb5B,IAAA,CAAAgB,OAAO,CAACe,aAAa,I,cAAlDnC,mBAAA,CAGM,OAHNoC,WAGM,G,0BAFJrB,mBAAA,CAA8B;MAAzBhB,KAAK,EAAC;IAAO,GAAC,OAAK,sBACxBgB,mBAAA,CAAoD,OAApDsB,WAAoD,EAAAlB,gBAAA,CAA9Bf,IAAA,CAAAgB,OAAO,CAACe,aAAa,iB,wCAGhB/B,IAAA,CAAAgB,OAAO,CAACkB,eAAe,I,cAApDtC,mBAAA,CAGM,OAHNuC,WAGM,G,0BAFJxB,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,sBAC1BgB,mBAAA,CAAsD,OAAtDyB,WAAsD,EAAArB,gBAAA,CAAhCf,IAAA,CAAAgB,OAAO,CAACkB,eAAe,iB,wCAGlBlC,IAAA,CAAAgB,OAAO,CAACqB,iBAAiB,I,cAAtDzC,mBAAA,CAGM,OAHN0C,WAGM,G,0BAFJ3B,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,sBAC1BgB,mBAAA,CAAwD,OAAxD4B,WAAwD,EAAAxB,gBAAA,CAAlCf,IAAA,CAAAgB,OAAO,CAACqB,iBAAiB,iB,wCAGpBrC,IAAA,CAAAgB,OAAO,CAACwB,aAAa,I,cAAlD5C,mBAAA,CAGM,OAHN6C,WAGM,G,0BAFJ9B,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,sBAC1BgB,mBAAA,CAAoD,OAApD+B,WAAoD,EAAA3B,gBAAA,CAA9Bf,IAAA,CAAAgB,OAAO,CAACwB,aAAa,iB,wCAGhBxC,IAAA,CAAAgB,OAAO,CAAC2B,YAAY,I,cAAjD/C,mBAAA,CAGM,OAHNgD,WAGM,G,0BAFJjC,mBAAA,CAA8B;MAAzBhB,KAAK,EAAC;IAAO,GAAC,OAAK,sBACxBgB,mBAAA,CAAoE;MAA/DhB,KAAK,EAAC,oBAAoB;MAACkD,SAA6B,EAArB7C,IAAA,CAAAgB,OAAO,CAAC2B;iFAGlD9B,mBAAA,mBAAsB,E,CACLb,IAAA,CAAAgB,OAAO,CAACS,WAAW,KAAKzB,IAAA,CAAAgB,OAAO,CAACY,UAAU,KAAK5B,IAAA,CAAAgB,OAAO,CAACkB,eAAe,I,cAAvFtC,mBAAA,CAoCWkD,SAAA;MAAAC,GAAA;IAAA,I,0BAnCTpC,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAA4B;MAAvBhB,KAAK,EAAC;IAAO,GAAC,KAAG,GACtBgB,mBAAA,CAA2B;MAAtBhB,KAAK,EAAC;IAAO,GAAC,IAAE,E,gDAGvBgB,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAAmC;MAA9BhB,KAAK,EAAC;IAAO,GAAC,YAAU,GAC7BgB,mBAAA,CAAyC;MAApChB,KAAK,EAAC;IAAO,GAAC,kBAAgB,E,kDAGrCgB,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAA8B;MAAzBhB,KAAK,EAAC;IAAO,GAAC,OAAK,GACxBgB,mBAAA,CAA0B;MAArBhB,KAAK,EAAC;IAAO,GAAC,GAAC,E,kDAGtBgB,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,GAC1BgB,mBAAA,CAAqC;MAAhChB,KAAK,EAAC;IAAO,GAAC,cAAY,E,kDAGjCgB,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,GAC1BgB,mBAAA,CAAwC;MAAnChB,KAAK,EAAC;IAAO,GAAC,iBAAe,E,kDAGpCgB,mBAAA,CAGM;MAHDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAAgC;MAA3BhB,KAAK,EAAC;IAAO,GAAC,SAAO,GAC1BgB,mBAAA,CAA6B;MAAxBhB,KAAK,EAAC;IAAO,GAAC,MAAI,E,kDAGzBgB,mBAAA,CAIM;MAJDhB,KAAK,EAAC;IAAW,IACpBgB,mBAAA,CAA8B;MAAzBhB,KAAK,EAAC;IAAO,GAAC,OAAK,GACxBgB,mBAAA,CACwF;MADnFhB,KAAK,EAAC;IAAoB,GAAC,mFACkD,E", "ignoreList": []}]}