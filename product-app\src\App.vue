<template>
  <router-view />
  <speechContent />
  <!-- <div v-if="scrollTop>=100"
       class="goTop"
       @click="goTop">
    <van-icon :size="41"
              name="upgrade"></van-icon>

  </div> -->
</template>
<script>
import api from './api'
import utils from './assets/js/utils'
import general from './assets/js/general'
import speechContent from './components/speechContent/speechContent'
import { onMounted, provide, reactive, toRefs } from 'vue'
export default {
  name: 'App',
  components: { speechContent },
  setup () {
    provide('$api', api)
    provide('$utils', utils)
    provide('$general', general)
    provide('$appTheme', '#3A77FF')// 主题颜色
    provide('$isShowHead', false)// 是否显示头部
    const data = reactive({
      scrollTop: 0
    })
    onMounted(() => {
      general.setWaterMark()
      general.appGrayscale()
    })
    window.onscroll = function () {
      data.scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    }
    const goTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
    return { ...toRefs(data), goTop }
  }
}
</script>
<style lang="less">
.goTop {
  position: fixed;
  text-align: center;
  width: 40px;
  // left: 0;
  right: 40px;
  // margin: auto;
  bottom: 120px;
}
#app {
  width: 100%;
  min-height: 100vh;
  background: #f8f8f8;
}
</style>
