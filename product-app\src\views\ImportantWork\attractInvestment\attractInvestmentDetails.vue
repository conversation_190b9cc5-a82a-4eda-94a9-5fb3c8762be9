<template>
  <div class="attractInvestmentDetails">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="attractInvestmentDetailsBox">
        <div class="investment_title">{{ details.title }}</div>
        <div class="investment_bottom">
          <span class="investment_date">{{ details.publishDate }}</span>
          <span class="investment_type_btn">
            {{ details.type == '1' ? '外出拜访' : details.type == '2' ? '在青接待' : details.type == '3' ? '自主举办' : '' }}
          </span>
        </div>
        <!-- 新增内容：根据type显示不同字段 -->
        <div class="investment_content">
          <!-- 外出拜访 -->
          <template v-if="details.type == '1'">
            <div class="item" v-if="details.publishDept">
              <div class="label">地点：</div>
              <div class="value">{{ details.publishDept }}</div>
            </div>
            <div class="item" v-if="details.projectWay">
              <div class="label">带队领导姓名及职务：</div>
              <div class="value">{{ details.projectWay }}</div>
            </div>
            <div class="item" v-if="details.projectAreaName">
              <div class="label">拜访企业名称：</div>
              <div class="value">{{ details.projectAreaName }}</div>
            </div>
            <div class="item" v-if="details.projectSuggestion">
              <div class="label">洽谈项目名称：</div>
              <div class="value">{{ details.projectSuggestion }}</div>
            </div>
            <div class="item" v-if="details.projectAppeal">
              <div class="label">洽谈阶段：</div>
              <div class="value">{{ details.projectAppeal }}</div>
            </div>
            <div class="item" v-if="details.businessName">
              <div class="label">洽谈情况：</div>
              <div class="value" v-html="details.businessName"></div>
            </div>
          </template>
          <!-- 在青接待 -->
          <template v-else-if="details.type == '2'">
            <div class="item" v-if="details.projectWay">
              <div class="label">接待领导姓名及职务：</div>
              <div class="value">{{ details.projectWay }}</div>
            </div>
            <div class="item" v-if="details.businessName">
              <div class="label">来访企业名称：</div>
              <div class="value">{{ details.businessName }}</div>
            </div>
            <div class="item" v-if="details.projectSuggestion">
              <div class="label">洽谈项目名称：</div>
              <div class="value">{{ details.projectSuggestion }}</div>
            </div>
            <div class="item" v-if="details.projectAppeal">
              <div class="label">洽谈情况：</div>
              <div class="value" v-html="details.projectAppeal"></div>
            </div>
          </template>
          <!-- 自主举办 -->
          <template v-else-if="details.type == '3'">
            <div class="item" v-if="details.projectWay">
              <div class="label">出席领导姓名及职务：</div>
              <div class="value">{{ details.projectWay }}</div>
            </div>
            <div class="item" v-if="details.businessName">
              <div class="label">活动名称：</div>
              <div class="value">{{ details.businessName }}</div>
            </div>
            <div class="item" v-if="details.publishDept">
              <div class="label">主办单位：</div>
              <div class="value">{{ details.publishDept }}</div>
            </div>
            <div class="item" v-if="details.projectAreaName">
              <div class="label">承办单位：</div>
              <div class="value">{{ details.projectAreaName }}</div>
            </div>
            <div class="item" v-if="details.projectSuggestion">
              <div class="label">活动简介：</div>
              <div class="value" v-html="details.projectSuggestion"></div>
            </div>
            <div class="item" v-if="details.projectAppeal">
              <div class="label">活动成效：</div>
              <div class="value" v-html="details.projectAppeal"></div>
            </div>
          </template>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'attractInvestmentDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        noticeInfo()
      }, 520)
    }
    // 详情请求
    const noticeInfo = async () => {
      const res = await $api.ImportantWork.info(data.id)
      var { data: details } = res
      data.details = details
      data.refreshing = false
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), $general, onRefresh, onClickLeft }
  }
}
</script>
<style lang="less">
.attractInvestmentDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .attractInvestmentDetailsBox {
    padding: 16px;

    .investment_title {
      font-family: Source Han Serif SC, Source Han Serif SC;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      margin-bottom: 12px;
    }

    .investment_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .investment_date {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-size: 14px;
        color: #666666;
      }

      .investment_type_btn {
        background: rgba(0, 122, 255, 0.12);
        border: 1px solid #007AFF;
        border-radius: 2px;
        font-family: Source Han Serif SC, Source Han Serif SC;
        font-weight: 500;
        font-size: 12px;
        color: #007AFF;
        padding: 3px;
      }
    }

    .investment_content {
      margin-top: 18px;
      font-size: 15px;
      color: #333;
      line-height: 2;

      .item {
        margin-bottom: 18px;

        .label {
          color: #999999;
          font-size: 16px;
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 600;
        }

        .value {
          color: #333333;
          font-size: 16px;
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 600;
        }
      }
    }
  }

}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
