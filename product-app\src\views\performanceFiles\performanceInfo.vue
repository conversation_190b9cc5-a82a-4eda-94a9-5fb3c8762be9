<template>
  <div class="performanceInfo">
    <template v-if="performanceInfoList&&performanceInfoList.length !== 0">
      <div v-for="(item,index) in performanceInfoList"
           :key="index"
           class="vue_newslist3_warp">
        <!--  @click="openDetails(item)" -->
        <van-cell clickable
                  class="vue_newslist3_item">
          <div class="historName"
               v-html="item"></div>
        </van-cell>
      </div>
    </template>
    <template v-else>
      <div style="text-align: center;color: #878787;padding-top: 30px;">暂无数据</div>
    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
             showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage } from 'vant'
export default {
  name: 'performanceInfo',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    // const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      year: route.query.year,
      userId: route.query.id,
      performanceInfoList: [],
      entList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      console.log('data.userId', data.userId)
      console.log('data.year', data.year)
      getDutyInfoConfigChineseNum()
    })
    // 列表
    const getDutyInfoConfigChineseNum = async () => {
      const res = await $api.performanceFiles.getDutyInfoConfigChineseNum({
        year: data.year,
        areaId: sessionStorage.getItem('areaId'),
        userIds: data.userId
      })
      console.log('res====>>', res)
      var { data: list } = res
      for (const i in list) {
        data.entList.push({
          name: i,
          infoText: list[i]
        })
      }
      data.entList.forEach(_item => {
        if (_item.name === data.title) {
          var dataMessage = _item.infoText.map((item, i) => {
            if (item) {
              return `${i + 1}、 ${item}`
            }
          })
          if (dataMessage[0]) {
            data.performanceInfoList = dataMessage
          } else {
            data.performanceInfoList = []
          }
        }
      })
    }
    // 详情
    const openDetails = (row) => {
      console.log('row===>>', row)
    }
    const onRefresh = () => {
    }
    const onLoad = () => {
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, dayjs, openDetails }
  }
}
</script>
<style lang="less" scoped>
.performanceInfo {
  width: 100%;
  .historName {
    font-size: 0.42rem;
  }
  .historTime {
    font-size: 0.35rem;
    color: #8d8c8c;
    margin-top: 0.2rem;
  }
}
</style>
