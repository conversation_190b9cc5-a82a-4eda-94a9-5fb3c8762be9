<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="play">
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="'font-size:20px;'"
           v-html="title"></div>
      <div class="n_details_more_box flex_box">
        <!--来源-->
        <!-- <div class="n_details_name flex_placeholder"
             :style="'font-size:12px;'">{{source?('来源：'+source):''}}</div> -->
        <div class="flex_placeholder"></div>
        <!--资讯类型 可点击更多-->
        <div v-if="IBSName.show && IBSName.value"
             class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_name"
               :style="'font-size:12px;'+'margin-right: 6px;color:'+appTheme">{{IBSName.value}}</div>
        </div>
      </div>
      <div class="n_details_more_box">
        <div v-if="browerCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">{{browerCount.hint}}{{browerCount.value}}</div>
        <div v-if="shareCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">分享：{{shareCount.value}}</div>
        <div v-if="commentCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">评论：{{commentCount.value}}</div>
        <div v-if="dataTime.show"
             class="n_details_time"
             :style="'font-size:12px;'">{{dataTime.value}}</div>
        <div style="clear: both;"></div>
      </div>
    </div>
    <div class="n_details_content"
         @click="setImgBigger"
         v-html="content"></div>
    <!-- 图文直播 -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      v-if="reportList&&reportList.length!==0">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="80"
                @load="onLoad"
                :immediate-check="false">
        <van-divider :style="{ color: appTheme, borderColor: appTheme, padding: '0 0.16rem' }">图文直播</van-divider>
        <ul class="list-box">
          <li v-for="(item,index) in reportList"
              :key="index"
              class="item-box">
            <div class="item-rot-box">
              <div class="item-rot"
                   :class="index==0?'one':''"></div>
            </div>
            <div class="time"
                 :style="general.loadConfiguration(-4)"
                 v-html="item.time"></div>
            <div class="n_details_content"
                 :style="general.loadConfiguration()"
                 v-html="item.content"></div>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
  <!--展示附件-->
  <template v-if="attachInfo.data.length != 0">
    <div class="general_attach"
         style="background-color: #fff;">
      <div v-for="(item,index) in attachInfo.data"
           :key="index"
           class="general_attach_item flex_box flex_align_center click"
           @click="annexClick(item,false)">
        <img class="general_attach_icon"
             :style="general.loadConfigurationSize([5,7])"
             :src="require('../../assets/fileicon/'+item.iconInfo.name)" />
        <div class="flex_placeholder flex_box flex_align_center">
          <div class="general_attach_name text_one2"
               style="font-size: 14px;display: -webkit-box;">{{item.name}}</div>
          <div class="general_attach_size"
               style="font-size: 12px;">{{general.getFileSize(item.size)}}</div>
        </div>
        <div v-if="item.state != 2"
             class="general_attach_state flex_box flex_align_center flex_justify_content"
             :style="general.loadConfigurationSize([7,7])">
          <van-icon v-if="item.state == 0"
                    class-prefix="iconfont"
                    color="#ccc"
                    :size="((appFontSize+3)*0.01)+'rem'"
                    name="xiazai"></van-icon>
          <van-circle v-else-if="item.state == 1"
                      :size="((appFontSize+3)*0.01)+'rem'"
                      v-model="item.schedule"
                      :rate="item.schedule"
                      stroke-width="150"></van-circle>
          <van-icon @click.stop="Toast('缓存异常，请点击标题重试');"
                    v-else-if="item.state == 3"
                    color="#ccc"
                    :size="((appFontSize+3)*0.01)+'rem'"
                    name="warning-o"></van-icon>
        </div>
      </div>
    </div>
  </template>
  <commentList ref="commentList"
               v-if="reportList.length===0"
               @openInputBoxEvent="openInputBoxEvent"
               @freshState="freshState"
               :commentData="commentData"
               :type="type"
               :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox">
    <inputBox ref="inputBox"
              :inputData="inputData"
              @addCommentEvent="addCommentEvent"
              :type="type"
              :id="id" />
  </footer>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, onBeforeUnmount } from 'vue'
import { NavBar, Sticky, ImagePreview, Divider } from 'vant'
import { useStore } from 'vuex'
import inputBox from '../../components/inputBox/inputBox.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'newsDetails',
  components: {
    inputBox,
    commentList,
    [Divider.name]: Divider,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      id: route.query.id,
      details: {},
      type: route.query.relateType,
      pageNo: 1,
      pageSize: 10,
      loading: false,
      finished: false,
      refreshing: false,
      show: false,
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      commentCount: { show: false, value: '0' }, // 评论数
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentList: null,
      inputBox: null,
      reportList: [], // 滚动报道
      task: null, // 计时任务
      time: 0,
      timer: 0
    })
    onMounted(() => {
      redPointSign()
      browseSave()
      newsInfo()
      getReportData()
      startTask()
    })
    onBeforeUnmount(() => {
      clearInterval(data.task)
    })
    const onRefresh = () => {
      newsInfo()
      data.pageNo = 1
      data.reportList = []
      data.loading = true
      data.finished = false
      getReportData()
    }
    const browseSave = async () => {
      const res = await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type,
        areaId: sessionStorage.getItem('areaId')
      })
      console.log('增加浏览记录', res)
    }
    // 去除红点
    const redPointSign = async () => {
      const res = await $api.Networkpolitics.redPointSign({
        id: data.id,
        type: 'memberead'
      })
      console.log('去除红点', res)
    }
    // 开始计时
    const startTask = () => {
      if (data.task) {
        stopTask()
      }
      data.task = setInterval(() => {
        data.time++
        console.log('计时' + data.time)
        if (data.time >= 60) {
          data.timer = 1
        }
        if (data.time >= 120) {
          data.timer = 2
        }
        if (data.time >= 180) {
          data.timer = 3
        }
        if (data.time >= 180) {
          data.timer = 3
        }
        if (data.time >= 240) {
          data.timer = 4
        }
        if (data.time >= 300) {
          data.timer = 5
        }
        if (data.time >= 360) {
          data.timer = 6
        }
        if (data.time < 60) {
          data.timer = 0
        }
        if (data.timer === 1) {
          getStartData(0, data.timer, null)
        }
        if (data.timer === 2) {
          getStartData(0, data.timer, null)
        }
        if (data.timer === 3) {
          getStartData(0, data.timer, null)
        }
        if (data.timer === 4) {
          getStartData(0, data.timer, null)
        }
        if (data.timer === 5) {
          getStartData(0, data.timer, null)
        }
        if (data.timer === 6) {
          getStartData(0, data.timer, null)
        }
      }, 1000)
    }
    // 暂停计时
    const stopTask = () => {
      clearInterval(data.task)
    }
    // 给接口传阅读时间
    const getStartData = async (_type, num) => {
      const res = await $api.general.wisdombeanAdd({
        dataId: data.id,
        score: num
      })
      console.log('给接口传阅读时间========>>>>', res)
    }
    const newsInfo = async () => {
      var res = {}
      // eslint-disable-next-line eqeqeq
      if (Number(data.relateType) == 53) {
        res = await $api.news.getSpecialsubjectnewsInfo({
          id: data.id
        })
      } else {
        res = await $api.news.getNewsDetail({
          id: data.id
        })
      }
      var info = res.data
      data.IBSName.show = true
      data.browerCount.show = true
      // data.shareCount.show = true
      // data.commentCount.show = true
      data.dataTime.show = true
      data.title = info.title || ''
      data.dataTime.value = info.publishDate || ''
      data.IBSName.value = info.source || info.structureName || ''
      data.browerCount.value = info.source
      data.browerCount.hint = info.source ? '来源：' : ''
      var content = (info.content || '').replace(/[\r\n]/g, '')
      // console.log('content==>', content)
      // const newHtml = content.replace(/<video([^>]+)>/g, function (match, p1) {
      //   const style = 'style="max-width: 100%;width:100%; height:100%;"'
      //   if (/style="/.test(p1)) {
      //     // 如果img标签中已经包含style属性，则替换为新的style
      //     return match.replace(/style="[^"]*"/, style)
      //   } else {
      //     // 如果img标签中不包含style属性，则添加新的style
      //     return match.replace(/<img/, '<img ' + style)
      //   }
      // })
      // console.log('newHtml===>', newHtml)
      data.content = general.dealWithCon(content || '') // 内容
      store.commit('setSpeechContent', data.content)
      data.picInfo.data = []
      data.attachInfo.data = []
      var attachmentList = info.attachmentList || []
      // eslint-disable-next-line eqeqeq
      if (attachmentList.length != 0) {
        for (var k = 0; k < attachmentList.length; k++) {
          var nItemPath = attachmentList[k].filePath
          var fileName = attachmentList[k].fileName
          var iconInfo = general.getFileTypeAttr(attachmentList[k].filePath.split('.')[attachmentList[k].filePath.split('.').length - 1])
          data.attachInfo.data.push({
            url: nItemPath,
            state: 0,
            schedule: -1,
            name: fileName,
            iconInfo: iconInfo
          })
        }
      }
      // eslint-disable-next-line eqeqeq
      if (data.module == 4 || data.module == 5) {
        data.shareCount.show = false
      }
      var externalLinks = info.externalLinks || ''// 发现有外部链接
      // eslint-disable-next-line eqeqeq
      if (externalLinks.indexOf('http') != 0) {
        externalLinks = ''
      }
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.n_details_content img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview({ images: img, startPosition: nowIndex, closeable: true })
      }
    }
    const addCommentEvent = (value) => {
      console.log(value)
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      console.log(value)
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      console.log(value)
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      // if (item.iconInfo.type) {
      //   if (window.location.origin === 'http://59.224.134.152:81') {
      //     window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + item.url)
      //   } else {
      //     window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.url)
      //   }
      // } else {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
      // }
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getReportData()
    }
    // 获取滚动报道
    const getReportData = async () => {
      const ret = await $api.news.getScrollingReportList({
        detailId: data.id,
        isCheck: 1,
        pageNo: data.pageNo,
        pageSize: data.pageSize
      })
      var { data: list, total } = ret
      if (list) {
        list.forEach(item => {
          item.time = item.publishDate || ''
          var oldT = new Date(item.time.replace(/-/g, '/'))
          var newT = new Date()
          var time = (newT.getTime() - oldT.getTime()) * 0.001 / 60
          time = parseInt(time)
          if (time > 1440 || time < -1440) {
            var timeL = item.time.length
            time = item.time.substring(5, timeL)
          } else {
            if (time < 0) {
              if (parseInt(time / 60) * (-1) !== 0) {
                time = (parseInt(time / 60) * (-1)) + '小时' + ((time % 60) * (-1)) + '分钟后'
              } else {
                time = ((time % 60) * (-1)) + '分钟后'
              }
            } else {
              if (parseInt(time / 60) * (-1) !== 0) {
                time = Number(parseInt(time / 60)) + '小时' + Number(time % 60) + '分钟前'
              } else {
                time = Number(time % 60) + '分钟前'
              }
            }
          }
          item.time = time
          item.content = general.dealWithCon(item.content || '')
        })
      }
      data.reportList = data.reportList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.reportList.length >= total) {
        data.finished = true
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onLoad, onClickLeft, setImgBigger, addCommentEvent, openInputBoxEvent, freshState, annexClick, general }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }

  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
  }
  .n_details_name {
    color: #666;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }

  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
    line-height: 30px;
    img {
      // margin: 15px 0;
      width: 100% !important;
    }
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
  .list-box {
    margin: 10px;
    padding-left: 12px;
    border-left: 1px dashed #333;
  }
  .item-box {
    position: relative;
    padding-bottom: 16px;
  }
  .item-rot-box {
    position: absolute;
    top: 0rem;
    background: #fff;
    width: 10px;
    height: 16px;
    left: -16px;
  }
  .item-rot {
    margin-top: 3px;
    border-radius: 50%;
    background-color: #01479d;
    width: 10px;
    height: 10px;
  }
  .item-rot.one {
    background-color: rgb(224, 82, 0);
  }
  .item-box .time {
    padding-bottom: 7px;
    color: #555;
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>
