
import api from '@/api'
import { generatorDynamicRouter } from '../../router/generator-router'

export const tabbarStore = {
  state: () => ({
    list: []
  }),
  getters: {
    tabbarList: state => state.list
  },
  mutations: {
    setTabbarList (state, list, aaa) {
      state.list = list
    }
  },
  actions: {
    async getAppList ({ commit }) {
      const areaId = JSON.parse(sessionStorage.getItem('areaId')) || ''
      const res = await api.general.appList({ position: '1', areaId: areaId })
      var { data: list } = res
      sessionStorage.setItem('menus', JSON.stringify(list))
      const generatorResult = await generatorDynamicRouter(list, 2)
      commit('setTabbarList', generatorResult.tabs)
      return generatorResult.tabs
    }
  }
}
