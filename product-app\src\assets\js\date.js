/**
 * 返回qq一样的时间样式  返回格式  默认qq列表   1为详情
 * @param {Object} _begin 开始时间
 * @param {Object} _end 结束时间
 * @param {Object} _interface
 */
function getConversionTime (_begin, _end, _interface) {
  var returnTime, reDetailsTime
  // 设置结束时间为  0时0分0秒0毫秒 再对比
  _end.setHours(0)
  _end.setMinutes(0)
  _end.setSeconds(0)
  _end.setMilliseconds(0)
  var beginTime = Math.round(_begin.getTime() / 1000)
  var endTime = Math.round(_end.getTime() / 1000)
  // 从0点开始计算   对比相差时间
  if (endTime - beginTime <= 0) { // 因为今天置于0了 小于0  就是今天
    returnTime = '' + getConversionHour(_begin) + getConversionMinute(_begin)
    reDetailsTime = '' + getConversionHour(_begin) + getConversionMinute(_begin)
  } else if (endTime - beginTime <= 86400) { // 小于1天 就是昨天
    returnTime = '昨天' + getConversionHour(_begin) + getConversionMinute(_begin)
    reDetailsTime = '昨天' + getConversionHour(_begin) + getConversionMinute(_begin)
  } else if (endTime - beginTime < 86400 * 6) { // 小于7天 就是星期
    returnTime = getConversionDay(_begin)
    reDetailsTime = getConversionDay(_begin) + ' ' + getConversionHour(_begin) + getConversionMinute(_begin)
  } else { // 否则就是年月日  年份不同 才加上年
    // eslint-disable-next-line eqeqeq
    returnTime = ((_begin.getFullYear() != _end.getFullYear()) ? getConversionYear(_begin) : '') + getConversionMonth(_begin) + getConversionDate(_begin)
    // eslint-disable-next-line eqeqeq
    reDetailsTime = ((_begin.getFullYear() != _end.getFullYear()) ? getConversionYear(_begin) : '') + getConversionMonth(_begin) + getConversionDate(_begin) + ' ' + getConversionHour(_begin) + getConversionMinute(_begin)
  }
  // eslint-disable-next-line eqeqeq
  if (_interface == 1) {
    return reDetailsTime
  }
  return returnTime
}
/**
 * 返回当前年+-
 * @param {Object} _data
 */
function getConversionYear (_data) {
  return _data.getFullYear() + '-'
}
/**
 * 返回当前月+- 少于10月时会加上0
 * @param {Object} _data
 */
function getConversionMonth (_data) {
  var str = _data.getMonth() + 1 // 当前月 从0开始
  if (str < 10) {
    str = '0' + str
  }
  return str + '-'
}
/**
 * 返回当前日+
 * @param {Object} _data
 */
function getConversionDate (_data) {
  var str = _data.getDate()
  if (str < 10) {
    str = '0' + str
  }
  return str + ''
}
/**
 * 返回当前小时+:
 * @param {Object} _data
 */
function getConversionHour (_data) {
  var str = _data.getHours()
  if (str < 10) {
    str = '0' + str
  }
  return str + ':'
}
/**
 * 返回当前分钟
 * @param {Object} _data
 */
function getConversionMinute (_data) {
  var str = _data.getMinutes()
  if (str < 10) {
    str = '0' + str
  }
  return str
}
/**
 * 返回当前星期几
 * @param {Object} _data
 */
function getConversionDay (_data) {
  return ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][_data.getDay()]
}
export {
  getConversionTime
}
