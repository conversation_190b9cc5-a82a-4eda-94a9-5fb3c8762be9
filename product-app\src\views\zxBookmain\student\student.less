.student {
    width: 100%;
    background: #FFF;

    .yx {
        background: #007AFF;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        position: fixed;
        margin: 0 auto;
        bottom: 40px;
        left: 0;
        right: 0;
        z-index: 991;
        box-shadow: 0 0 5px #007AFF;
        text-align: center;
    }

    .search_warp {
        width: 100%;
        background: #F9F9F9;
        min-height: 35px;
        border-radius: 35px;
        padding: 0 10px;
    }
    .van-pull-refresh {
      z-index: 99;
    }

    .box {
        padding: 15px;
        border-bottom: 2px #F4F4F4 solid;
    }
    .item_top {
        display: flex;
        width: 100%;
        height: 45px;
        .crus {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            margin-right: 10px;
            > img {
                width: 100%;
                height: 100%;
            }
        }
        .font {
            :first-child {
               color: #000000;
               font-weight: bold;
               font-size: 14px;
            }
            :last-child {
                color: #92A0AC;
                font-size: 12px;
                margin-top: 5px;
             }
        }
    }

    .text {
        margin-top: 10px;
        letter-spacing:1px;
    }
    .item_btn_box {
      width: 252px;
      margin: 20px 0 10px 0;
      font-size: 15px;
    }
  
    .itemSex_box {
      margin: 0 -6px;
    }
  
    .itemSex_item {
      width: 33.33%;
      padding: 0 6px 5px 6px;
    }
  
    .itemSex_name {
      color: #222;
      font-weight: 500;
      margin-top: 0;
      padding-left: 5px;
      font-size: 15px;
    }
  
    .itemThree_item {
      padding: 6px 0;
    }
  
    .itemThree_name {
      color: #222;
      font-weight: 400;
      margin-top: 0;
      margin-bottom: 3px;
      font-size: 15px;
    }
  
    .itemThree_summary {
      font-weight: 400;
      margin-top: 8px;
      color: #8b8a8a;
      font-size: 12px;
    }
  
    .itemNotes_item {
      padding-top: 12px;
    }
  
    .itemNotes_item_box {
      padding: 0 0 15px 10px;
    }
  
    .itemNotes_name {
      font-weight: 500;
      color: #222222;
      font-size: 12px;
    }
  
    .itemNotes_content {
      color: #222222;
      padding-bottom: 6px;
      font-size: 13px;
    }
  
    .itemNotes_from {
      padding: 5px;
      border-radius: 6px;
    }
  
    .itemNotes_author {
      margin-top: 3px;
    }
  
    .itemWords_content_box {
      margin: 10px 0;
      padding: 16px 16px;
    }
  
    .itemWords_content {
      position: relative;
    }
  
    .itemWords_content_hint {
      position: absolute;
      background: #fff;
      font-size: 56px;
      width: 22px;
      height: 26px;
      font-family: Helvetica;
    }
  
    .itemWords_author {
      text-align: right;
      margin-top: 15px;
    }
  }