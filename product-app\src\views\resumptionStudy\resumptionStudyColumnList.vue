<template>
  <div class="newsList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
      <div>
        <van-search v-model="keyword" @search="search" @clear="search" placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>
    <!-- <van-tabs v-model:active="switchs.value"
              swipeable
              sticky
              :color="appTheme"
              :ellipsis="false"
              line-height='3'>
      <van-tab v-for="item in switchs.data"
               :title="item.label"
               :name="item.value"
               :key="item.value"> -->
    <!-- 二级栏目 -->
    <!-- <div v-if="item.children.length > 1"
             class="van-hairline--bottom">
          <van-tabs v-model="childrenItemValue"
                    @click="tabClick2"
                    :ellipsis="false"
                    line-width="0"
                    :lazy-render="false">
            <van-tab v-for="(nItem, nIndex) in item.children"
                     :key="nIndex"
                     :name="nItem.id">
              <template v-slot:title>
                <div class="childrenItem_item"
                     :style="$general.loadConfiguration(-2) + 'color:' + (childrenItemValue == nItem.id ? '#FFF' : appTheme) + ';background:' + (childrenItemValue == nItem.id ? appTheme : '#F4F4F4')">
                  {{ nItem.name }}
                </div>
              </template>
</van-tab>
</van-tabs>
</div> -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad"
        :immediate-check="false">
        <!--一、轮播图-->
        <div v-if="carouselList.length != 0" class="carouselMap">
          <van-swipe :autoplay="5000" :show-indicators="false" @change="carouselChange">
            <van-swipe-item v-for="(item, index) in carouselList" :key="index"
              @click="details(carouselList[carouselIndex]);">
              <div class="carousel_img" :style="'background-image:url(' + item.url + ')'"></div>
            </van-swipe-item>
          </van-swipe>
          <div class="carousel_elBox flex_box flex_align_center"
            :style="'height: ' + ((16 - 3) * 1.46 * 2 + 17) + 'px;margin-top:-' + (((16 - 3) * 1.46 * 2 + 18) * 0.5) + 'px;'"
            @click="details(carouselList[carouselIndex]);">
            <img v-if="true" class="carousel_tag" src="../../assets/img/img_hot.png" />
            <div class="carousel_title text_two flex_placeholder" :style="$general.loadConfiguration(-3)"
              v-html="carouselList[carouselIndex].title"></div>
          </div>
          <div class="carouselPoint flex_box flex_align_center flex_justify_content"
            :style="'padding-top:' + (((16 - 3) * 1.46 * 2 + 18) * 0.5 + 5) + 'px'">
            <div v-for="(item, index) in carouselList" :key="index" class="carouselPointItem"
              :class="index == carouselIndex ? 'carouselPointActive' : ''"
              :style="index == carouselIndex ? 'width:23px;background:' + appTheme : ''"></div>
          </div>
        </div>
        <!--数据列表-->
        <ul v-if="dataList.length" class="vue_newslist_box"
          :style="!carouselList.length ? 'border-top:10px solid #f8f8f8;' : ''">
          <template v-for="(item, index) in dataList" :key="index">
            <div clickable class="vue_newslist_item "
              :style="(item.relateType == 32 ? (index == 0 ? 'margin-bottom:10px;' : (index == dataList.length - 1 ? 'margin-top:10px;' : 'margin:10px 0;')) : '')"
              @click="details(item)">
              <!--<div v-if="" class="notRead"></div>-->
              <div v-if="item.type == '大张图' || item.relateType == 32">
                <div class="flex_box flex_align_center">
                  <span v-if="item.relateType == 32"
                    :style="'height:16px;width:5px;background:' + appTheme + ';margin-right:11px;'"></span>
                  <div class="flex_placeholder" :class="(item.imgs.length > 0 || item.url) ? 'text_one2' : ''"
                    :style="'font-size:15px;' + (item.isRead == '1' ? 'color: #666;' : 'color: #333;')"
                    v-html="item.title"></div>
                  <div v-if="item.relateType == 32" class="flex_box flex_align_center">
                    <div :style="'font-size:15px;' + 'color:#999;margin-right:6px;'">打开</div>
                    <van-icon :color="'#999'" :size="15" name="arrow"></van-icon>
                  </div>
                </div>
                <div v-if="item.imgs.length || item.url" class="vue_newslist_boxImg">
                  <van-swipe :indicator-color="appTheme">
                    <template v-if="item.imgs.length">
                      <van-swipe-item v-for="(item2, index2) in item.imgs" :key="index2">
                        <img class="vue_newslist_itemImg" :src="item2.url" />
                      </van-swipe-item>
                    </template>
                    <van-swipe-item v-else>
                      <img class="vue_newslist_itemImg" :src="item.url" />
                    </van-swipe-item>
                  </van-swipe>
                </div>
                <div v-else class="vue_newslist_summary text_one2" :style="'font-size:14px;'" v-html="item.content">
                </div>
                <div class="flex_box flex_align_center">
                  <!--<div class="vue_newslist_label" :style="$general.loadConfiguration(-4)+'color:'+appTheme+';border: 1px solid '+appTheme+';margin-left: 0;'">{{item.ibsName}}</div>-->
                  <div class="vue_newslist_time" :style="'font-size:14px;'">{{ item.time.split(' ')[0] }}</div>
                  <div class="vue_newslist_source flex_placeholder text_one2" :style="'font-size:14px;'">{{ item.source
                    ||
                    item.createBy }}</div>
                  <!--<div class="vue_newslist_browerCount" :style="$general.loadConfiguration(-6)">阅读：{{item.browerCount}}</div>-->

                </div>
              </div>
              <div v-else class="flex_box">
                <img class="vue_newslist_img" v-if="item.url" :src="item.url" />
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                    :style="'font-weight:normal;' + (item.isRead == '1' ? 'color: #666;' : 'color: #333;')"
                    v-html="item.title">
                  </div>
                  <div v-if="item.url" class="vue_newslist_time" :style="'font-size:14px;' + 'margin-bottom:5px;'">{{
                    item.time.split(' ')[0] }}</div>
                  <!-- <div v-else class="vue_newslist_summary text_one2" :style="'font-size:14px;'" v-html="item.content"></div> -->
                  <div v-if="!item.url && item.videoHrefs" class="videoBox" style="margin-bottom: 10px;" @click.stop="">
                    <video class="videoItem" width="100%" height="auto" :src="item.videoHrefs" controls
                      controlsList='nofullscreen nodownload noremote footbar'></video>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div v-if="!item.url" class="vue_newslist_time" :style="'font-size:14px;'">
                      {{ item.time.split(' ')[0] }}
                    </div>
                    <div class="vue_newslist_source flex_placeholder text_one2" :style="'font-size:14px;'">{{
                      item.source ||
                      item.createBy }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </ul>
      </van-list>
    </van-pull-refresh>
    <!-- </van-tab>
    </van-tabs> -->
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'newsList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 9,
      carouselIndex: 0,
      carouselClick: true,
      carouselList: [],
      dataList: [],
      switchs: {
        value: '',
        data: []
      },
      childrenItem: { value: '', data: [] },
      childrenItemValue: ''
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      if (route.query.module) {
        data.module = route.query.module
      }
      getTree()
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 栏目请求
    const getTree = async () => {
      var { data: tree } = await $api.news.getNewsTree({
        module: data.module,
        isAppShow: 1,
        isPushApp: 1
      })
      data.switchs.data = []
      tree.forEach((item, index) => {
        if (index === 0) {
          data.switchs.value = item.id
        }
        item.label = item.name
        item.value = item.id
        item.children = item.children || []
        if (item.children.length !== 0) {
          data.childrenItemValue = item.children[0].id
        }
      })
      data.switchs.data = data.switchs.data.concat(tree)
    }
    // tab2切换事件
    const tabClick2 = (_value, _name) => {
      data.childrenItemValue = _value
      onRefresh()
    }
    // 列表请求
    const getList = async () => {
      // var newsId = ''
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      if (data.module === '4') { getCarousel() }
      // if (data.switchs.data.length > 0) {
      //   const currentItem = data.switchs.data.find(item => item.value === data.switchs.value)
      //   if (currentItem.children.length === 0) {
      //     newsId = data.switchs.value
      //   } else {
      //     newsId = data.childrenItemValue
      //   }
      // }
      var res = await $api.news.zyinfodetaillist({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        structureId: route.query.id,
        module: data.module
        // auditingFlag: 1,
        // isAppShow: 1
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.relateType = item.type || 5
        item.type = item.infoType || ''
        item.class = item.infoClass || ''
        item.id = item.relateId || item.id || ''
        item.title = item.title || ''
        if ((data.keyword).replace(/(^\s*)|(\s*$)/g, '')) {
          item.title = item.title.replace(new RegExp(data.keyword, 'g'), '<span style="color:' + data.appTheme + ';" class="inherit">' + data.keyword + '</span>')
        }
        // 去除HTML中的注释、去除HTML标签、去除HTML标签中的属性、去除所有空白字符即回车换行
        item.content = item.content ? DeleteHtmlFromStartToEnd(item.content, '<!--', '-->').replace(/<.*?>/g, '').replace(/&nbsp;/ig, '') : ''
        var image = item.image || {}
        item.url = item.leftImage || image.fullUrl || ''// 图片
        item.videoHrefs = item.videoHrefs || ''// 视频链接
        item.source = item.source || ''
        item.createBy = item.createBy || ''
        item.isTop = item.isTop || '0'// 置顶 1是0否
        item.externalLinks = item.externalLinks || ''// 外部链接
        item.browerCount = item.browseCount || '0'
        item.isRead = item.isRead ? '1' : '0'// 是否已读
        item.time = item.publishDate || item.createDate || ''
        item.imgs = []
        var resultBatchAttach = item.groupPicList || []
        var resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
        for (var j = 0; j < resultBatchAttachLength; j++) {
          item.imgs.push({ url: resultBatchAttach[j].fullUrl })
        }
        item.module = data.module
        // eslint-disable-next-line eqeqeq
        if (item.type == '无图') {
          item.url = ''
          item.imgs = []
        }
      })
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 内容content处理
    const DeleteHtmlFromStartToEnd = (str, begin, end) => {
      str = str.replace(begin + end, '')
      if (str.indexOf(begin) === -1) {
        return str
      }
      var substr = str.substring(str.indexOf(begin) + begin.length, str.indexOf(end))
      str = str.replace(substr, '')
      return DeleteHtmlFromStartToEnd(str, begin, end)
    }
    // 资讯module为1 的轮播图
    const getCarousel = async () => {
      var ret = await $api.news.getTopList({
        pageNo: 1,
        pageSize: 100,
        module: data.module,
        areaId: data.user.areaId,
        structureId: route.query.id,
        auditingFlag: 1,
        isAppShow: 1
      })
      if (ret) {
        var topList = ret.data || []
        data.carouselList = []
        topList.forEach(element => {
          var image = element.image || {}
          element.url = element.leftImage || image.fullUrl || ''// 图片
          element.class = element.infoClass || ''
        })
        data.carouselList = data.carouselList.concat(topList)
      }
    }
    // 轮播图改变事件
    const carouselChange = (_index) => {
      data.carouselIndex = _index
    }
    const onClickLeft = () => history.back()
    // 进详情
    const details = (row) => {
      if (row.externalLinks) {
        window.location.href = row.externalLinks
        return
      }
      // eslint-disable-next-line eqeqeq
      if (row.relateType == '32') {
        router.push({ name: 'newsZTList', query: { id: row.id, title: row.title, relateType: row.relateType } })
      } else {
        router.push({ name: 'newsDetails', query: { id: row.id, title: row.title, relateType: row.relateType } })
      }
    }
    return { ...toRefs(data), search, onClickLeft, onRefresh, onLoad, details, $general, carouselChange, tabClick2 }
  }
}
</script>

<style lang="less" scoped>
.newsList {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .carouselMap {
    width: 100%;
    height: auto;
    position: relative;
  }

  .carouselMap {
    .carousel_img {
      position: relative;
      width: 100%;
      height: 220px;
      // background: url() no-repeat;
      background-size: cover;
      -webkit-background-size: cover;
      background-position: 50%;
    }
  }

  .carousel_elBox {
    position: absolute;
    width: calc(100% - 30px);
    padding: 0 10px;
    margin: -28px 15px 0 15px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 2px 24px rgba(24, 64, 118, 0.1);
  }

  .carousel_tag {
    margin-right: 4px;
    height: 75%;
  }

  .carousel_title {
    font-weight: 600;
    color: #333;
    line-height: 1.46;
  }

  .carouselPoint {
    padding-top: 33px;
    width: 100%;
    height: 15px;
    background: #fff;
    box-sizing: content-box;
  }

  .carouselPointItem {
    width: 17px;
    height: 4px;
    background: #eeeeee;
    border-radius: 96px;
  }

  .carouselPointItem+.carouselPointItem {
    margin-left: 7px;
  }
}

.childrenItem_item {
  line-height: 1;
  padding: 6px 18px;
  border-radius: 12px;
}

#app .van-tabs__nav--complete {
  padding: 0 7px;
}

#app .van-tab {
  padding: 6px 7px;
}

#app .van-tabs__line {
  display: none;
}
</style>
