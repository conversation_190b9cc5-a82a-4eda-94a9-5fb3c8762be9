<template>
  <div class="girdModuleMoreList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <div class="grid_box">
      <div class="title_box">我的应用</div>
      <div style="color: #999999;font-size: 12px;">长按并拖动可调整应用位置与排序</div>
    </div>
    <van-grid v-if="refreshList" clickable :column-num="4">
      <!-- <draggable class="flex_placeholder flex_box T-flex-flow-row-wrap"
                 :delay="100"
                 group="memu"
                 v-model="modeList"> -->
      <van-grid-item v-for="(item, index) in modeListShow" :key="index" :badge="getModuleRedPointNum(item)"
        style="font-size: 14px;" @click.stop.prevent="itemClick(item)">
        <template v-slot:default>
          <div style="position: relative;">
            <img class="draggable_item" @click.stop.prevent="itemClick(item)" :src="item.url"
              style="object-fit: cover;width: 40px;height: 40px;-webkit-touch-callout: none;pointer-events:none" />
          </div>
          <div :style="'font-weight: 500;color: #333;font-size:12px;width: 55px;'" v-html="item.name"
            @click.stop="itemClick(item)"></div>
          <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
            :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
            :style="item.pointType == 'big' ? 'font-size:12px;width:20px;height:20px;' : 'width:10px;height:10px;'"
            v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></p>
        </template>
      </van-grid-item>
      <!-- </draggable> -->
    </van-grid>
    <div class="grid_box" style="margin-top: 10px;">
      <div class="title_box">其他应用</div>
    </div>
    <!--未选中的栏目-->
    <van-grid clickable :column-num="4">
      <!-- <draggable class="flex_placeholder flex_box T-flex-flow-row-wrap"
                 :delay="100"
                 group="memu"
                 v-model="menuList"> -->
      <van-grid-item v-for="(item, index) in menuListShow" :key="index" :badge="getModuleRedPointNum(item)"
        style="font-size: 14px;" @click.stop.prevent="itemClick(item)">
        <template v-slot:default>
          <div style="position: relative;">
            <img class="draggable_item" @click.stop.prevent="itemClick(item)" :src="item.url"
              style="object-fit: cover;width: 40px;height: 40px;-webkit-touch-callout :none;pointer-events:none" />
          </div>
          <div :style="'font-weight: 500;color: #333;font-size:12px;width: 55px;'" v-html="item.name"
            @click.stop.prevent="itemClick(item)"></div>
          <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
            :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
            :style="item.pointType == 'big' ? 'font-size:12px;width:20px;height:20px;' : 'width:10px;height:10px;'"
            v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></p>
        </template>
      </van-grid-item>
      <!-- </draggable> -->
    </van-grid>
  </div>
</template>
<script>
// import { VueDraggableNext } from 'vue-draggable-next'
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch, computed } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Toast } from 'vant'
import { useStore } from 'vuex'
export default {
  name: 'noticeList',
  components: {
    // draggable: VueDraggableNext,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: route.query.areaId,
      keyword: '',
      refreshList: true, // 刷新列表
      listData: [],
      modeList: [], // 已选中的模块
      modeListShow: [],
      menuList: [], // 未选中的模块
      menuListShow: [],
      ModuleRedDotsNumber1: computed(() => store.state.ModuleRedDotsNumber1),
      ModuleRedDotsNumber2: computed(() => store.state.ModuleRedDotsNumber2),
      ModuleRedDotsNumber3: computed(() => store.state.ModuleRedDotsNumber3),
      ModuleRedDotsNumber4: computed(() => store.state.ModuleRedDotsNumber4),
      ModuleRedDotsNumber5: computed(() => store.state.ModuleRedDotsNumber5),
      ModuleRedDotsNumber6: computed(() => store.state.ModuleRedDotsNumber6)
    })
    if (data.title) {
      document.title = data.title
    }
    watch(() => data.modeList, (newName, oldName) => {
      showModeList()
      setMainSelect()
    })
    onMounted(() => {
      var mainSelectData = localStorage.getItem(data.user.areaId + 'mainSelectData' + data.user.id) || ''
      if (mainSelectData) {
        data.modeList = mainSelectData.split('|,|')
      }
      getModuleList()
    })
    // 统计各模块需要红点的赋值
    const getModuleRedPointNum = (_item) => {
      if (_item.appUrl === 'conferenceActivities') { // 会议活动
        _item.pointNumber = data.ModuleRedDotsNumber1 + data.ModuleRedDotsNumber2 || 0
        console.log('data.pointNumber===>', _item.pointNumber)
      } else if (_item.appUrl === 'bookHome') { // 书香政协
        _item.pointNumber = data.ModuleRedDotsNumber3 || 0
      } else if (_item.appUrl === 'socials') { // 社情民意
        _item.pointNumber = data.ModuleRedDotsNumber4 || 0
      } else if (_item.appUrl === 'voteList') { // 投票
        _item.pointNumber = data.ModuleRedDotsNumber5 || 0
      } else if (_item.appUrl === 'questionnaireList') { // 调查问卷
        _item.pointNumber = data.ModuleRedDotsNumber6 || 0
      } else {
        _item.pointNumber = ''
      }
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getModuleList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        getModuleList()
      }, 520)
    }
    // 列表请求
    const getModuleList = async () => {
      var res = await $api.general.appList({ parentId: sessionStorage.getItem('historyIndex'), areaId: sessionStorage.getItem('areaId') })
      if (res) {
        var list = res.data || []
        var dataLength = list ? list.length : 0
        if (!list || dataLength === 0) {
          return
        }
        list = list.find(item => item.name === '首页').children || []
        dataLength = list ? list.length : 0
        data.listData = []
        data.menuList = []
        for (var i = 0; i < dataLength; i++) {
          var item = {}
          var itemData = list[i]
          item.id = itemData.id + ''
          item.name = itemData.name
          item.sort = itemData.sort
          item.url = itemData.iconUrl
          item.appUrl = itemData.infoUrl2
          item.type = itemData.type
          item.remarks = itemData.remarks
          item.pointType = 'big'
          item.pointNumber = 0
          item.needRecord = itemData.needRecord
          if (item.appUrl && item.appUrl !== '#') {
            data.listData.push(item)
          }
          data.listData = data.listData.filter(item => item.name !== '有事多商量')
          const sortedArray = data.listData.sort((a, b) => a.sort - b.sort)
          data.modeListShow = sortedArray.slice(0, 8)
          data.menuListShow = sortedArray.slice(8, 99)
        }
        if (!data.modeList) {
          for (var j = 0; j < dataLength; j++) {
            if (j < 7) {
              data.modeList = []
              data.modeList.push(data[j].id + '')
            }
          }
        }
        // showModeList()
        data.refreshList = false
        setTimeout(function () {
          data.refreshList = true
        }, 0)
      }
    }
    // 显示选择的数据
    const showModeList = () => {
      if (!data.listData.length) {
        return
      }
      data.modeListShow = []
      data.modeList.forEach(function (_eItem, _eIndex, _eArr) {
        var nItem = $general.getItemForKey(_eItem, data.listData, 'id')
        if (nItem) {
          data.modeListShow.push(nItem)
        }
      })
      data.menuList = []
      data.listData.forEach(function (_eItem, _eIndex, _eArr) {
        var result = !data.modeList.includes(_eItem.id)
        if (result) {
          data.menuList.push(_eItem.id)
        }
      })
      data.menuListShow = []
      data.menuList.forEach(function (_eItem, _eIndex, _eArr) {
        var nItem = $general.getItemForKey(_eItem, data.listData, 'id')
        if (nItem) {
          data.menuListShow.push(nItem)
        }
      })
      // that.$forceUpdate()
    }
    // 设置选择的模块
    const setMainSelect = async () => {
      localStorage.setItem(data.user.areaId + 'mainSelectData' + data.user.id, data.modeList.join('|,|'))
    }
    // 中间栏目点击
    const itemClick = (_item) => {
      console.log(JSON.stringify(_item))
      if (_item.appUrl) {
        var routerStrs2 = _item.appUrl + '?' + _item.remarks
        if (routerStrs2.indexOf('http://172.20.236.51:810') === 0 || routerStrs2.indexOf('http://111.231.25.206:8082') === 0) { // 济事商量
          const mobile = JSON.parse(sessionStorage.getItem('user')).mobile
          console.log('跳转协商平台手机号：', mobile)
          window.location.href = _item.appUrl + '?mobile=' + mobile
        } else if (routerStrs2.indexOf('http://172.20.57.9:99') === 0) { // OA
          const hrefTo = encodeURIComponent(JSON.parse(sessionStorage.getItem('desEncryptPhone')))
          const urlInfo = _item.appUrl.slice(0, 40)
          window.location.href = urlInfo + hrefTo
        } else if (routerStrs2.indexOf('http') === 0) {
          const token = JSON.parse(sessionStorage.getItem('token'))
          if (routerStrs2.indexOf('qdprmobile') !== -1 || routerStrs2.indexOf('zsta') !== -1) {
            window.location.href = _item.appUrl + '?token=' + token.replace(/bearer\s*/, '')
          } else {
            window.location.href = _item.appUrl
          }
        } else {
          var myParams2 = { title: _item.name }
          if (_item.remarks) {
            const a = _item.remarks.split('&')
            a.forEach(element => {
              myParams2[element.split('=')[0]] = element.split('=')[1]
            })
          }
          router.push({ path: routerStrs2, query: myParams2 })
        }
      } else {
        Toast('请配置好H5路由')
      }
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), itemClick, search, onClickLeft, onRefresh, getModuleRedPointNum }
  }
}
document.οncοntextmenu = function (e) {
  e.preventDefault()
}
</script>

<style lang="less">
body {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.girdModuleMoreList {
  width: 100%;
  min-height: 100%;
  background: #f8f8f8;

  .footer_item_hot {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #f92323;
    border-radius: 50%;
  }

  .footer_item_hot_big {
    position: absolute;
    top: 10px;
    right: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
  }

  .grid_box {
    display: flex;
    padding: 14px 20px;
    justify-content: space-between;
    background-color: #fff;

    .title_box {
      font-weight: bold;
      color: #333333;
      font-size: 16px;
    }
  }
}

.van-grid {
  background-color: #fff;
}

::v-deep .van-grid-item__content {
  padding: 0 !important;
}
</style>
