<template>
  <div class="bookDesk">
    <van-sticky>
      <div class="flex_placeholder flex_box flex_justify_content"
           style="background:#f8f8f8;">
        <ul class="header_box flex_box flex_align_center">
          <li v-for="(item,index) in switchs.data"
              :key="index"
              @click="switchHeader(index)"
              :style="switchs.value==index?'font-size:18px;':'font-size:14px;'"
              class="header_item"
              :class="switchs.value==index?'active':''">{{item.label}}</li>
        </ul>
      </div>
    </van-sticky>
    <van-tabs v-model:active="switchs.value"
              swipeable
              line-height='0'>
      <van-tab v-for="index in switchs.data.length"
               :key="index">
        <books v-if="switchs.value==0"></books>
        <notes v-if="switchs.value==1"></notes>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { onMounted, reactive, toRefs, watch } from 'vue'
import { Sticky } from 'vant'
import books from '../bookDesk/books'
import notes from '../bookDesk/notes.vue'
export default {
  components: {
    books,
    notes,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      appReadStatus: [],
      title: route.query.title,
      switchs: {
        value: 0,
        data: [
          { label: '图书', frameName: 'books_frame', url: './books_frame.html' },
          { label: '笔记', frameName: 'notes_frame', url: './notes_frame.html' }]
      }
    })
    watch(() => data.keyword, (newName, oldName) => {
    })
    onMounted(() => {
    })
    const switchHeader = (index) => {
      data.switchs.value = index
    }
    return { ...toRefs(data), switchHeader }
  }
}
</script>
<style lang="less" scoped>
.bookDesk {
  width: 100%;
  .header_box {
    padding: 0 5px;
  }
  .header_item {
    padding: 8px 10px;
    color: #a5a5a5;
  }
  .header_item.active {
    font-weight: bold;
    color: #101010;
  }
  .max_header {
    position: relative;
  }
  .btnLeft_box {
    position: absolute;
  }
}
</style>
