<template>
  <div class="myProposalList">
    <div class="myProposalListHead">
      <van-search v-model="keyword"
                  show-action
                  placeholder="请输入搜索关键词"
                  @clear="onRefresh"
                  @search="onRefresh">
        <template #action>
          <div class="screeningButton"
               @click="show = !show">
            <van-icon name="filter-o" />筛选
          </div>
        </template>
      </van-search>
    </div>
    <van-tabs v-model:active="active"
              @change="onRefresh"
              offset-top="44"
              color="#0D75FF"
              swipeable
              sticky>
      <van-tab v-for="item in activeData"
               :key="item.id"
               :name="item.id"
               :title="item.value">
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <div class="myProposalListBox">
              <dataList v-for="item in dataList"
                        :key="item.id"
                        :title="item.title"
                        @click="details(item)">
                <div class="state">{{ item.processStateView }}</div>
                <div>{{ item.submitDate.slice(0, 10) }}</div>
              </dataList>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <van-popup v-model:show="show"
               position="top">
      <screening more
                 cancel
                 title="年份"
                 v-model="year"
                 :data="yearData"
                 @more-click="moreClick"></screening>
      <screening title="届"
                 v-model="circles"
                 :data="circlesData"
                 :props="{ label: 'value', id: 'value' }">届</screening>
      <screening title="次"
                 v-model="bout"
                 :data="boutData"
                 :props="{ label: 'value', id: 'value' }"></screening>
      <screening cancel
                 title="提案类型"
                 v-model="submitType"
                 :data="submitTypeData"></screening>
      <screening cancel
                 title="会议类型"
                 v-model="meetType"
                 :data="meetTypeData"></screening>
      <div class="buttonBox">
        <van-button type="primary"
                    @click="reset"
                    size="mini">重置</van-button>
        <van-button type="primary"
                    @click="onRefresh"
                    size="mini">确定</van-button>
      </div>
    </van-popup>
    <div class="newProposal"
         @click="newClick">撰写</div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import dataList from '../homePage/dataList'
import screening from './components/screening'
export default {
  name: 'myProposalList',
  components: {
    dataList,
    screening
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      active: '1',
      activeData: [
        { id: '1', value: '我领衔的' },
        { id: '2', value: '我联名的' },
        { id: '3', value: '草稿箱' }
      ],
      year: '',
      yearData: [],
      circles: '',
      bout: '',
      circlesData: [],
      boutData: [],
      submitType: '',
      submitTypeData: [
        { id: '1', label: '集体提案' },
        { id: '2', label: '个人提案' },
        { id: 'queryAgreeJoin', label: '联名提案' }
      ],
      meetType: '',
      meetTypeData: [
        { id: '1', label: '大会提案' },
        { id: '2', label: '平时提案' }
      ],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      total: 0,
      dataList: [],
      show: false
    })
    onMounted(() => {
      crrentcircles()
      pubkvs()
      getNf(6)
    })
    /**
     * 当前届次
     */
    const crrentcircles = async type => {
      const res = await $api.general.crrentcircles({ memberType: 1 })
      var { data: list } = res
      data.circles = list.circlesName.slice(0, list.circlesName.length - 1)
      data.bout = list.boutName.slice(0, list.boutName.length - 1)
      if (type) {
        onRefresh()
      }
    }
    const moreClick = () => {
      getNf(data.yearData.length + 3)
    }
    const getNf = number => {
      var yearsArr = []
      var years = new Date().getFullYear()
      for (var i = years; i >= years - number; i--) {
        yearsArr.push({ id: i + '', label: i + '' })
      }
      data.yearData = yearsArr
    }
    const pubkvs = async () => {
      const res = await $api.general.pubkvs({
        types: 'bout_type,circles_type'
      })
      var { data: list } = res
      data.circlesData = list.circles_type
      data.boutData = list.bout_type
    }
    // 提案列表
    const myProposalList = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: 10,
        // processState: data.active === '1' ? '' : data.active,
        keyword: data.keyword,
        year: data.year, // 提案所属年份
        circles: data.circles, // 届
        bout: data.bout, // 次
        submitType: data.submitType === 'queryAgreeJoin' ? '' : data.submitType,
        meetType: data.meetType // 1 大会提案 2 平时提案
      }
      if (data.submitType === 'queryAgreeJoin') {
        datas.queryAgreeJoin = '1'
      }
      var res = []
      if (data.active === '1') {
        res = await $api.proposal.myProposalList(datas)
      } else if (data.active === '2') {
        res = await $api.proposal.myJoinProposalList(datas)
      } else if (data.active === '3') {
        res = await $api.proposal.myDraftsProposalList(datas)
      }
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      myProposalList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      myProposalList()
    }
    const reset = () => {
      getNf(6)
      data.year = ''
      data.keyword = ''
      data.meetType = ''
      data.submitType = ''
      crrentcircles(1)
    }
    const details = row => {
      console.log(row)
      router.push({ name: 'proposalDetails', query: { id: row.id } })
    }
    const newClick = () => {
      router.push({ name: 'proposalNew' })
    }
    return {
      ...toRefs(data),
      moreClick,
      onRefresh,
      onLoad,
      reset,
      details,
      newClick
    }
  }
}
</script>
<style lang="less">
.myProposalList {
  width: 100%;
  padding-top: 44px;
  .myProposalListHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .van-search {
      width: 100%;
      padding: 5px 0 5px 12px;
      .van-cell {
        padding: 3px 0;
      }
      .van-icon {
        color: #888;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        .van-field__body {
          font-size: 14px;
          .van-field__control {
            color: #888;
            &::-webkit-input-placeholder {
              color: #888;
            }
          }
        }
      }
      .screeningButton {
        font-size: 14px;
      }
    }
  }
  .myProposalListBox {
    margin: auto;
    padding: 16px;
  }
  .van-popup {
    width: 100%;
    padding-top: 44px;
    background-color: #fff;
    .buttonBox {
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 22px 0;
      .van-button {
        width: 90px;
        height: 28px;
        .van-button__content {
          height: 26px;
          .van-button__text {
            font-size: 14px;
          }
        }
      }
      .van-button + .van-button {
        margin-left: 20px;
      }
    }
  }
  .newProposal {
    position: fixed;
    left: 50%;
    bottom: 10%;
    transform: translateX(-50%);
    width: 56px;
    height: 56px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    text-align: center;
    color: #ffffff;
  }
}
</style>
