{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755082716031}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "names": [], "mappings": ";AAiGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,EAAE,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC1B,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1C;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACV;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/ImportantWork/attractInvestment/attractInvestmentDetailsCopy.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" title=\"详情\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n      <div class=\"details-container\">\r\n        <!-- 标题 -->\r\n        <div class=\"title\">{{ details.title || '孟庆斌主席在成都市拜访清华四川能源互联网研究院' }}</div>\r\n\r\n        <!-- 日期和标签 -->\r\n        <div class=\"header-info\">\r\n          <div class=\"date\">{{ formatDate(details.publishDate) || '2025-04-02' }}</div>\r\n          <div class=\"tag\">{{ getTypeText(details.type) || '外出拜访' }}</div>\r\n        </div>\r\n\r\n        <!-- 详情信息 -->\r\n        <div class=\"info-section\">\r\n          <div class=\"info-item\" v-if=\"details.publishDept\">\r\n            <div class=\"label\">地点：</div>\r\n            <div class=\"value\">{{ details.publishDept }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectWay\">\r\n            <div class=\"label\">带队领导姓名及职务：</div>\r\n            <div class=\"value\">{{ details.projectWay }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.companyNumber\">\r\n            <div class=\"label\">企业序号：</div>\r\n            <div class=\"value\">{{ details.companyNumber }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAreaName\">\r\n            <div class=\"label\">拜访企业名称：</div>\r\n            <div class=\"value\">{{ details.projectAreaName }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectSuggestion\">\r\n            <div class=\"label\">洽谈项目名称：</div>\r\n            <div class=\"value\">{{ details.projectSuggestion }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAppeal\">\r\n            <div class=\"label\">洽谈项目价值：</div>\r\n            <div class=\"value\">{{ details.projectAppeal }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.businessName\">\r\n            <div class=\"label\">洽谈情况：</div>\r\n            <div class=\"value content-text\" v-html=\"details.businessName\"></div>\r\n          </div>\r\n\r\n          <!-- 如果没有数据，显示默认内容 -->\r\n          <template v-if=\"!details.publishDept && !details.projectWay && !details.projectAreaName\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">地点：</div>\r\n              <div class=\"value\">北京</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">带队领导姓名及职务：</div>\r\n              <div class=\"value\">其他，市政协副主席，市工商联主席</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">企业序号：</div>\r\n              <div class=\"value\">1</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">拜访企业名称：</div>\r\n              <div class=\"value\">清华四川能源互联网研究院</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目名称：</div>\r\n              <div class=\"value\">建设一体飞行平台及发展游戏项目</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目价值：</div>\r\n              <div class=\"value\">储备在谈</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈情况：</div>\r\n              <div class=\"value content-text\">\r\n                重点围绕低空经济生态打造，在青岛地区开展飞行平台及发展游戏项目等方面进行了深入交流。双方将持续飞行系统等部分产品，推动项目落地。最终，双方就青岛低空经济应用通道</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </van-pull-refresh>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { NavBar, Sticky, PullRefresh } from 'vant'\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [PullRefresh.name]: PullRefresh\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const $general = inject('$general')\r\n    const $api = inject('$api')\r\n    const $appTheme = inject('$appTheme')\r\n    const $isShowHead = inject('$isShowHead')\r\n\r\n    const data = reactive({\r\n      hasApi: false,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {},\r\n      refreshing: false\r\n    })\r\n\r\n    if (data.title) {\r\n      document.title = data.title\r\n    }\r\n\r\n    if (typeof (window.api) === 'undefined') {\r\n      data.hasApi = false\r\n    } else {\r\n      data.hasApi = true\r\n    }\r\n\r\n    onMounted(() => {\r\n      getInfo()\r\n    })\r\n\r\n    const onRefresh = () => {\r\n      setTimeout(() => {\r\n        getInfo()\r\n      }, 520)\r\n    }\r\n\r\n    // 详情请求\r\n    const getInfo = async () => {\r\n      try {\r\n        const res = await $api.ImportantWork.info(data.id)\r\n        var { data: details } = res\r\n        data.details = details\r\n        data.refreshing = false\r\n      } catch (error) {\r\n        console.error('获取详情失败:', error)\r\n        data.refreshing = false\r\n      }\r\n    }\r\n\r\n    // 格式化日期\r\n    const formatDate = (dateStr) => {\r\n      if (!dateStr) return ''\r\n      const date = new Date(dateStr)\r\n      return date.getFullYear() + '-' +\r\n        String(date.getMonth() + 1).padStart(2, '0') + '-' +\r\n        String(date.getDate()).padStart(2, '0')\r\n    }\r\n\r\n    // 获取类型文本\r\n    const getTypeText = (type) => {\r\n      const typeMap = {\r\n        1: '外出拜访',\r\n        2: '在青接待',\r\n        3: '自主举办'\r\n      }\r\n      return typeMap[type] || '外出拜访'\r\n    }\r\n\r\n    const onClickLeft = () => {\r\n      history.back()\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      $general,\r\n      onRefresh,\r\n      onClickLeft,\r\n      formatDate,\r\n      getTypeText\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n\r\n  .details-container {\r\n    padding: 20px;\r\n\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      margin-bottom: 16px;\r\n      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n    }\r\n\r\n    .header-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #F4F4F4;\r\n\r\n      .date {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n      }\r\n\r\n      .tag {\r\n        background: rgba(0, 122, 255, 0.12);\r\n        border: 1px solid #007AFF;\r\n        border-radius: 4px;\r\n        padding: 4px 8px;\r\n        font-size: 12px;\r\n        color: #007AFF;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .info-section {\r\n      .info-item {\r\n        margin-bottom: 20px;\r\n\r\n        .label {\r\n          font-size: 16px;\r\n          color: #999999;\r\n          font-weight: 600;\r\n          margin-bottom: 8px;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #333333;\r\n          font-weight: 600;\r\n          line-height: 1.5;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n\r\n          &.content-text {\r\n            line-height: 1.6;\r\n            font-weight: normal;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 导航栏样式\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  }\r\n}\r\n</style>\r\n"]}]}