<template>
  <div class="speechMaterialList">
    <van-tabs v-model:active="switchs.value"
              :color="appTheme"
              swipeable
              sticky
              :offset-top="isShowHead?'46px':'0'"
              :title-active-color="appTheme"
              :ellipsis="false">
      <van-tab v-for="(item,index) in switchs.data"
               :key="index"
               :title="item.label"
               :name="item.value">
        <!-- 搜索框 -->
        <div id="search"
             style="border-radius: 10px;"
             class="search_box"
             :style="$general.loadConfiguration() ">
          <div class="search_warp flex_box">
            <div @click="search();"
                 class="search_btn flex_box flex_align_center flex_justify_content">
            </div>
            <form class="flex_placeholder flex_box flex_align_center search_input"
                  action="javascript:return true;">
              <input id="searchInput"
                     class="flex_placeholder"
                     :style="$general.loadConfiguration(-1)"
                     placeholder="请输入搜索内容"
                     maxlength="100"
                     type="search"
                     ref="btnSearch"
                     @keyup.enter="search()"
                     v-model="seachText" />
              <div v-if="seachText"
                   @click="seachText='';search();"
                   class="search_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                          :color="'#ccc'"
                          :name="'clear'"></van-icon>
              </div>
            </form>
          </div>
        </div>
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <template v-if="item.value === 'NoticeList'">
              <!--数据列表-->
              <ul class="vue_newslist3_box">
                <div v-for="(item,index) in listData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <van-cell clickable
                            class="vue_newslist3_item "
                            @click="details(item,1)">
                    <div class="flex_box">
                      <div class="flex_placeholder vue_newslist_warp">
                        <div class="vue_newslist_title text_two"
                             :style="$general.loadConfiguration(1)">
                          <span v-if="item.isTop == '1'"
                                class="vue_newslist_top"
                                :style="$general.loadConfiguration(-4)">
                            <van-tag plain
                                     :color="appTheme">置顶</van-tag>
                          </span>
                          <span class="inherit"
                                v-html="item.name"></span>
                        </div>
                        <div class="flex_box flex_align_center">
                          <div class="vue_newslist_time"
                               :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.time}}</div>
                          <div class="flex_placeholder"></div>
                          <span v-if="item.status"
                                class="social_status"
                                :style="$general.loadConfiguration(-3)+'margin-left:24px;color:'+getStatusColor(item.status,1)">
                            <van-tag :color="getStatusColor(item.status,0)">{{item.status}}</van-tag>
                          </span>
                        </div>
                      </div>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </template>
            <template v-else>
              <!--数据列表-->
              <ul class="vue_newslist3_box">
                <div v-for="(item,index) in PublicListData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <van-cell clickable
                            class="vue_newslist3_item "
                            @click="details(item,2)">
                    <div class="flex_box">
                      <div class="flex_placeholder vue_newslist_warp">
                        <div class="vue_newslist_title text_two"
                             :style="$general.loadConfiguration(1)">
                          <span v-if="item.isTop == '1'"
                                class="vue_newslist_top"
                                :style="$general.loadConfiguration(-4)">
                            <van-tag plain
                                     :color="appTheme">置顶</van-tag>
                          </span>
                          <span class="inherit"
                                v-html="item.name"></span>
                        </div>
                        <div class="flex_box flex_align_center">
                          <div class="vue_newslist_time"
                               :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.time}}</div>
                          <div class="flex_placeholder"></div>
                          <span v-if="item.status"
                                class="social_status"
                                :style="$general.loadConfiguration(-3)+'margin-left:24px;color:'+getStatusColor(item.status,1)">
                            <van-tag :color="getStatusColor(item.status,0)">{{item.status}}</van-tag>
                          </span>
                        </div>
                      </div>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </template>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <div class="footer_box"
         v-if="footerBtns.length != 0">
      <template v-for="(aitem,aindex) in footerBtns"
                :key="aindex">
        <div class="footer_nBtn_box T-flexbox-vertical flex_align_center flex_justify_content"
             @click="footerBtnClick(aitem)">
          <van-icon v-if="aitem.icon"
                    :size="((appFontSize+5)*0.01)+'rem'"
                    :name="aitem.icon"></van-icon>{{aitem.name}}
        </div>
      </template>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
                   showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag } from 'vant'
import moment from 'moment'

export default {
  name: 'speechMaterialList',
  components: {
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      switchs: { value: 'all', data: [{ label: '征稿通知', value: 'NoticeList' }, { label: '公开发言', value: 'Public' }] },
      listData: [],
      PublicListData: [],
      footerBtns: [] // 底部按钮集合 top为返回顶部   btn为按钮
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      if (data.switchs.value === 'NoticeList') {
        var footer = [{ type: 'nBtn', name: '我的', click: 'my', icon: 'search' }]
        data.footerBtns = data.footerBtns.concat(footer)
      } else {
        data.footerBtns = []
      }
      data.pageNo = 1
      data.listData = []
      data.PublicListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getNoticeList()
      PublicListData()
    })
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.PublicListData = []
      data.loading = true
      data.finished = false
      getNoticeList()
      PublicListData()
    }
    onMounted(() => {
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.PublicListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getNoticeList()
      PublicListData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取征稿通知列表
    const getNoticeList = async () => {
      const res = await $api.speechMaterial.speechnoticeList({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.time = dayjs(item.startTime).format('YYYY-MM-DD') + '至' + dayjs(item.endTime).format('YYYY-MM-DD')
        item.status = dayjs().isBefore(dayjs(item.startTime)) ? '未开始' : dayjs().isAfter(dayjs(item.endTime)) ? '已结束' : '征稿中'
      })
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 获取公开发言类别
    const PublicListData = async () => {
      const res = await $api.speechMaterial.conferencespeechList({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText,
        isPublic: 1
      })
      var { data: list, total: total1 } = res
      list.forEach(item => {
        item.time = item.speechDate ? moment(`${item.speechDate}`).format('YYYY-MM-DD') : '' // 时间
        item.status = item.speechStateName || ''
      })
      data.PublicListData = data.PublicListData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.PublicListData.length >= total1) {
        data.finished = true
      }
    }

    const getColor = (type) => {
      var color = data.backgroundcolor[0]
      switch (type) {
        case '征稿中':
          color = data.backgroundcolor[0]
          break
        case '已采纳':
          color = data.backgroundcolor[1]
          break
        case '未开始':
          color = data.backgroundcolor[1]
          break
      }
      return color
    }
    const getTextColor = (type) => {
      var color = data.textcolor[0]
      switch (type) {
        case '征稿中':
          color = data.textcolor[0]
          break
        case '已采纳':
          color = data.textcolor[1]
          break
        case '未开始':
          color = data.textcolor[1]
          break
      }
      return color
    }
    // 获取各状态颜色   0背景 1颜色
    const getStatusColor = (_status, _type) => {
      var colors = {
        征稿中: ['#E8F7FF', '#3E9DFF'],
        已采纳: ['#E8F7FF', '#3E9DFF'],
        未开始: ['#FFF6D0', '#F6AE31']
      }
      try {
        return colors[_status][_type]
      } catch (e) {
        return _type === 0 ? '#eee' : '#999'
      }
    }
    // 详情
    const details = (row, type) => {
      router.push({ name: 'speechMaterialDetails', query: { id: row.id, type: type } })
    }
    // 我的按钮跳转
    const footerBtnClick = (row, type) => {
      router.push({ name: 'myList' })
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, details, search, getColor, getTextColor, getStatusColor, footerBtnClick }
  }
}
</script>
<style lang="less" scoped>
.speechMaterialList {
  background: #f8f8f8;
  .social_status .van-tag {
    border-radius: 3px;
    padding: 2px 8px;
    color: inherit;
  }
  .footer_box {
    position: fixed;
    right: 25px;
    bottom: 30px;
    border-radius: 50%;
    padding: 8px 12px;
    background-color: #3088fe;
    .footer_nBtn_box {
      color: #fff;
    }
  }
}
</style>
