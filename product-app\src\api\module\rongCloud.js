import {
  HTTP
} from '../http.js'
class rongCloud extends HTTP {
  // 获取通讯录分组成员列表
  getChaters (params) {
    return this.request({
      url: '/chatter/selfList',
      data: params
    })
  }

  // 获取通讯录分组列表
  getTxlist (params) {
    return this.request({
      url: '/chatter/group/appList?',
      data: params
    })
  }

  // 获取群组列表
  getGroupList (params) {
    return this.request({
      url: '/talkroup/list/byuser',
      data: params
    })
  }

  // 获取用户信息
  getUserInfo (params) {
    return this.request({
      url: '/wholeuser/info/' + params.id,
      // data: params,
      method: 'GET'
    })
  }

  // 获取群组信息
  getGroupInfo (params) {
    return this.request({
      url: '/talkroup/info/' + params.id,
      // data: params,
      method: 'GET'
    })
  }

  // 获取群组信息
  getGroupMember (params) {
    return this.request({
      url: '/talkroup/member?groupId=' + params.id,
      data: params
    })
  }

  // 获取群组信息
  editopensay (params) {
    return this.request({
      url: '/talkroup/editopensay',
      data: params
    })
  }

  // 退出群组
  outGroup (params) {
    return this.request({
      url: '/talkroup/out',
      data: params
    })
  }

  // 退出群组
  disbandGroup (params) {
    return this.request({
      url: '/talkroup/disband/' + params.groupId,
      data: params
    })
  }

  // 获取我的文件
  getFileinfoList (params) {
    return this.request({
      url: '/fileinfo/list',
      data: params
    })
  }

  // 获取群文件
  getTeamGroupFaildsList (params) {
    return this.request({
      url: '/talkroup/findTeamGroupFailds',
      data: params
    })
  }

  // 删除群文件
  delFileinfo (params) {
    return this.request({
      url: '/fileinfo/del/' + params.id,
      data: params
    })
  }

  // 增加群文件
  addFileinfo (params) {
    return this.request({
      url: '/fileinfo/add',
      data: params
    })
  }

  // 编辑群文件
  editFileinfo (params) {
    return this.request({
      url: '/fileinfo/edit',
      data: params
    })
  }

  // 群文件详情
  getFileinfo (params) {
    return this.request({
      url: '/fileinfo/info/' + params.id,
      data: params
    })
  }

  // 新增群组
  addGroup (params) {
    return this.request({
      url: '/talkroup/add',
      data: params
    })
  }

  // 新增群组
  addGroupUser (params) {
    return this.request({
      url: '/talkroup/join',
      data: params
    })
  }

  updateWhether (params) {
    return this.request({
      url: '/talkroup/UpdateWhether',
      data: params
    })
  }

  // 增加消息统计
  addGroupmessage (params) {
    return this.request({
      url: '/groupmessage/add',
      data: params
    })
  }

  // 是否可以发送消息
  whetherToDiscuss (params) {
    return this.request({
      url: '/talkroup/whetherToDiscuss',
      data: params
    })
  }

  // 获取融云token
  getToken (params) {
    return this.request({
      url: params,
      data: params,
      method: 'GET'
    })
  }
}
export {
  rongCloud
}
