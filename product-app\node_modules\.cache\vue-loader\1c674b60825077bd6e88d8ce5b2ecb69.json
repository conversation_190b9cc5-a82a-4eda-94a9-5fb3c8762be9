{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue?vue&type=template&id=4b900e74", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue", "mtime": 1755082716031}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\attractInvestment\\attractInvestmentDetailsCopy.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/ImportantWork/attractInvestment/attractInvestmentDetailsCopy.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"attractInvestmentDetailsCopy\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" title=\"详情\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n      <div class=\"details-container\">\r\n        <!-- 标题 -->\r\n        <div class=\"title\">{{ details.title || '孟庆斌主席在成都市拜访清华四川能源互联网研究院' }}</div>\r\n\r\n        <!-- 日期和标签 -->\r\n        <div class=\"header-info\">\r\n          <div class=\"date\">{{ formatDate(details.publishDate) || '2025-04-02' }}</div>\r\n          <div class=\"tag\">{{ getTypeText(details.type) || '外出拜访' }}</div>\r\n        </div>\r\n\r\n        <!-- 详情信息 -->\r\n        <div class=\"info-section\">\r\n          <div class=\"info-item\" v-if=\"details.publishDept\">\r\n            <div class=\"label\">地点：</div>\r\n            <div class=\"value\">{{ details.publishDept }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectWay\">\r\n            <div class=\"label\">带队领导姓名及职务：</div>\r\n            <div class=\"value\">{{ details.projectWay }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.companyNumber\">\r\n            <div class=\"label\">企业序号：</div>\r\n            <div class=\"value\">{{ details.companyNumber }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAreaName\">\r\n            <div class=\"label\">拜访企业名称：</div>\r\n            <div class=\"value\">{{ details.projectAreaName }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectSuggestion\">\r\n            <div class=\"label\">洽谈项目名称：</div>\r\n            <div class=\"value\">{{ details.projectSuggestion }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.projectAppeal\">\r\n            <div class=\"label\">洽谈项目价值：</div>\r\n            <div class=\"value\">{{ details.projectAppeal }}</div>\r\n          </div>\r\n\r\n          <div class=\"info-item\" v-if=\"details.businessName\">\r\n            <div class=\"label\">洽谈情况：</div>\r\n            <div class=\"value content-text\" v-html=\"details.businessName\"></div>\r\n          </div>\r\n\r\n          <!-- 如果没有数据，显示默认内容 -->\r\n          <template v-if=\"!details.publishDept && !details.projectWay && !details.projectAreaName\">\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">地点：</div>\r\n              <div class=\"value\">北京</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">带队领导姓名及职务：</div>\r\n              <div class=\"value\">其他，市政协副主席，市工商联主席</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">企业序号：</div>\r\n              <div class=\"value\">1</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">拜访企业名称：</div>\r\n              <div class=\"value\">清华四川能源互联网研究院</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目名称：</div>\r\n              <div class=\"value\">建设一体飞行平台及发展游戏项目</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈项目价值：</div>\r\n              <div class=\"value\">储备在谈</div>\r\n            </div>\r\n\r\n            <div class=\"info-item\">\r\n              <div class=\"label\">洽谈情况：</div>\r\n              <div class=\"value content-text\">\r\n                重点围绕低空经济生态打造，在青岛地区开展飞行平台及发展游戏项目等方面进行了深入交流。双方将持续飞行系统等部分产品，推动项目落地。最终，双方就青岛低空经济应用通道</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </van-pull-refresh>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { NavBar, Sticky, PullRefresh } from 'vant'\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'attractInvestmentDetailsCopy',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [PullRefresh.name]: PullRefresh\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const $general = inject('$general')\r\n    const $api = inject('$api')\r\n    const $appTheme = inject('$appTheme')\r\n    const $isShowHead = inject('$isShowHead')\r\n\r\n    const data = reactive({\r\n      hasApi: false,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {},\r\n      refreshing: false\r\n    })\r\n\r\n    if (data.title) {\r\n      document.title = data.title\r\n    }\r\n\r\n    if (typeof (window.api) === 'undefined') {\r\n      data.hasApi = false\r\n    } else {\r\n      data.hasApi = true\r\n    }\r\n\r\n    onMounted(() => {\r\n      getInfo()\r\n    })\r\n\r\n    const onRefresh = () => {\r\n      setTimeout(() => {\r\n        getInfo()\r\n      }, 520)\r\n    }\r\n\r\n    // 详情请求\r\n    const getInfo = async () => {\r\n      try {\r\n        const res = await $api.ImportantWork.info(data.id)\r\n        var { data: details } = res\r\n        data.details = details\r\n        data.refreshing = false\r\n      } catch (error) {\r\n        console.error('获取详情失败:', error)\r\n        data.refreshing = false\r\n      }\r\n    }\r\n\r\n    // 格式化日期\r\n    const formatDate = (dateStr) => {\r\n      if (!dateStr) return ''\r\n      const date = new Date(dateStr)\r\n      return date.getFullYear() + '-' +\r\n        String(date.getMonth() + 1).padStart(2, '0') + '-' +\r\n        String(date.getDate()).padStart(2, '0')\r\n    }\r\n\r\n    // 获取类型文本\r\n    const getTypeText = (type) => {\r\n      const typeMap = {\r\n        1: '外出拜访',\r\n        2: '在青接待',\r\n        3: '自主举办'\r\n      }\r\n      return typeMap[type] || '外出拜访'\r\n    }\r\n\r\n    const onClickLeft = () => {\r\n      history.back()\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      $general,\r\n      onRefresh,\r\n      onClickLeft,\r\n      formatDate,\r\n      getTypeText\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.attractInvestmentDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n\r\n  .details-container {\r\n    padding: 20px;\r\n\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      margin-bottom: 16px;\r\n      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n    }\r\n\r\n    .header-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 24px;\r\n      padding-bottom: 16px;\r\n      border-bottom: 1px solid #F4F4F4;\r\n\r\n      .date {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n      }\r\n\r\n      .tag {\r\n        background: rgba(0, 122, 255, 0.12);\r\n        border: 1px solid #007AFF;\r\n        border-radius: 4px;\r\n        padding: 4px 8px;\r\n        font-size: 12px;\r\n        color: #007AFF;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .info-section {\r\n      .info-item {\r\n        margin-bottom: 20px;\r\n\r\n        .label {\r\n          font-size: 16px;\r\n          color: #999999;\r\n          font-weight: 600;\r\n          margin-bottom: 8px;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n        }\r\n\r\n        .value {\r\n          font-size: 16px;\r\n          color: #333333;\r\n          font-weight: 600;\r\n          line-height: 1.5;\r\n          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n\r\n          &.content-text {\r\n            line-height: 1.6;\r\n            font-weight: normal;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 导航栏样式\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  }\r\n}\r\n</style>\r\n"]}]}