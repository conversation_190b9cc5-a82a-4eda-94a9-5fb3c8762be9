<template>
  <div class="readUserList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <van-search v-model="keyword"
                  @search="search"
                  @clear="search"
                  placeholder="请输入搜索关键词" />
    </van-sticky>
    <!-- <div class="readUserListHead">
      <van-search v-model="keyword"
                  placeholder="请输入搜索关键词" />
    </div> -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="readUserBox">
        <div class="readUserItem"
             v-for="item in readUser.filter(data => !keyword || data.userName.toLowerCase().includes(keyword.toLowerCase()))"
             :key="item.userId"
             @click="openUser(item)">
          <div class="readUserImg">
            <img :src="item.headImg"
                 alt="">
          </div>
          <div class="readUserName">{{item.userName}}</div>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'readUserList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '人员详情',
      id: route.query.id,
      keyword: '',
      refreshing: false,
      readUser: []
    })
    onMounted(() => {
      readingDetail()
    })
    const onRefresh = () => {
      setTimeout(() => {
        readingDetail()
      }, 520)
    }
    const readingDetail = async () => {
      const res = await $api.notice.readingDetail({
        id: data.id
      })
      var { data: { readUser } } = res
      data.readUser = readUser
      data.refreshing = false
    }
    const openUser = (_item) => {
      router.push({ name: 'personData', query: { id: _item.userId } })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, openUser }
  }
}
</script>
<style lang="less">
.readUserList {
  width: 100%;
  min-height: 100%;
  // padding-top: 44px;
  background-color: #fff;
  .readUserListHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .van-search {
      width: 100%;
      .van-cell {
        padding: 3px 0;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        .van-field__body {
          font-size: 14px;
        }
      }
    }
  }
  .readUserBox {
    padding: 0 16px;
    .readUserItem {
      width: 100%;
      display: flex;
      align-items: center;
      position: relative;
      padding: 10px 0;
      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        opacity: 0.2;
        background-color: #999999;
      }
      .readUserImg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
