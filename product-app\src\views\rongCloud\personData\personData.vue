<template>
  <div class="personData">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">
      </van-nav-bar>
    </van-sticky>
    <div :style="'height: 180px;background-image:url('+userInfo.url+');filter: blur(2px);background-repeat: no-repeat;background-size: cover;background-position:center'"></div>
    <div class="msg_body flex_box">
      <div class="msg_body_bg">
        <van-image @click="previewImg(userInfo)"
                   :style="'width:70px;height:70px;'"
                   class="user_img"
                   round
                   fit="contain"
                   :src="userInfo.url"></van-image>
        <div class="user_name">{{userInfo.name}}</div>
      </div>
      <div class="flex_placeholder"></div>
      <div v-if="user.id != userInfo.id && !appShelf"
           :style="'font-size:13px;'">
        <van-button icon="chat-o"
                    size="mini"
                    :color="appTheme"
                    @click="openMsg">{{'发送消息'}}</van-button>
      </div>
    </div>
    <div style="margin-top: 10px;">
      <div :style="'font-size:15px;'">
        <van-cell v-for="(item,index) in items"
                  :key="index"
                  :title="item.label"
                  :value="item.value"></van-cell>
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { NavBar, Sticky, CellGroup, Button, Field, Popup, Image as VanImage, Divider, ActionSheet, Grid, GridItem, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
export default {
  name: 'personData',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Popup.name]: Popup,
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Divider.name]: Divider,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Field.name]: Field
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const $general = inject('$general')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      // eslint-disable-next-line eqeqeq
      title: route.query.title || '用户详情',
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      conversationType: Number(route.query.conversationType),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      userInfo: { url: '../../../images/icon_default_user.png', name: '', id: 0 },

      items: [
        { label: '职务', key: 'position', value: '' },
        { label: '联系方式', key: 'mobile', value: '' },
        { label: '邮箱', key: 'email', value: '' }
      ],

      imgBox: { name: '委员说', data: [] },
      isAttention: false,
      hasShow: false, // 是否显示委员说关注
      appShelf: false

    })
    onMounted(() => {
      getMemberList()
    })

    const getMemberList = async () => {
      var { data: info } = await $api.rongCloud.getUserInfo({
        id: data.id
      })
      data.userInfo.id = info.id
      data.userInfo.name = info.userName || ''
      data.title = info.userName || ''
      data.userInfo.url = info.fullImgUrl || ''
      for (var i = 0; i < data.items.length; i++) {
        data.items[i].value = info[data.items[i].key] || ''
      }
    }
    // 是否有搜索对象
    const hasSeach = () => {
      var hasSeach = false
      data.members.data.forEach(function (_eItem, _eIndex, _eArr) {
        // eslint-disable-next-line eqeqeq
        if (_eItem.name.indexOf(data.seachText) != -1) {
          hasSeach = true
        }
      })
      return hasSeach
    }
    const previewImg = (info) => {
      ImagePreview({
        images: [info.url],
        closeable: true
      })
    }
    const openMsg = () => {
      router.push({ name: 'chatRoom', query: { id: sessionStorage.getItem('rongCloudIdPrefix') + data.id, conversationType: 1, name: data.userInfo.name } })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, hasSeach, openMsg, previewImg }
  }
}
</script>
<style lang="less" scoped>
.personData {
  width: 100%;
  .msg_body {
    background: #fff;
    min-height: 90px;
    padding: 17px 14px;
    position: relative;
  }
  .msg_body_bg {
    position: absolute;
    top: -26px;
    left: 15px;
  }
  .user_img {
    background: #fff;
  }
  .user_name {
    margin-top: 5px;
    text-align: center;
    color: #333;
    font-weight: bold;
  }
  #app .msg_body .van-button {
    padding: 5px 15px;
    height: inherit;
    border-radius: 13px;
  }
  #app .van-cell {
    padding: 17px 14px;
    color: #333;
    font-weight: 600;
  }
  #app .van-cell__value {
    color: #666;
    font-weight: 600;
  }
}
</style>
