import { HTTP } from '../http.js'
class social extends HTTP {
  treelist (params) { // 查询树列表
    return this.request({
      url: '/tree/list',
      data: params
    })
  }

  // 获取社情民意列表
  getSocialList (params) {
    return this.request({
      url: '/socialinfo/list',
      data: params
    })
  }

  // 提案首页权限
  authority (params) {
    return this.request({
      url: '/proposalAuthority/getPublicOpinionControl',
      data: params
    })
  }

  authoritys (params) {
    return this.request({
      url: '/socialOpinions/authOpinions',
      data: params
    })
  }

  // 获取社情民意详情
  getSocialDetails (params) {
    return this.request({
      url: '/socialinfo/look/' + params
    })
  }

  // 新增社情民意详情
  addSocial (params) {
    return this.request({
      url: '/socialinfo/add',
      data: params
    })
  }

  // 编辑社情民意详情
  editSocial (params) {
    return this.request({
      url: '/socialinfo/edit',
      data: params
    })
  }

  // 删除社情民意详情
  delsSocial (params) {
    return this.request({
      url: '/socialinfo/dels',
      data: params
    })
  }

  // 获取年度来搞统计
  getStatistics (params) {
    return this.request({
      url: '/socialinfo/count',
      data: params
    })
  }

  // 征稿通知详情
  socialinfoInfo (params) {
    return this.request({ url: `/socialinfo/info/${params}` })
  }

  // 社情民意处理信息
  editqdstatus (params) {
    return this.request({
      url: '/socialinfo/editqdstatus',
      data: params
    })
  }

  // 社情民意处理信息详情
  statusinfo (params) {
    return this.request({ url: `/socialinfo/statusinfo/${params}` })
  }
}
export {
  social
}
