.library {
  width: 100%;
  background: #FFF;

  .top {
    width: 100%;
    height: 50px;
    background: #0271E3;
    color: #FFF;
    font-size: 20px;
    line-height: 50px;
  }
  .bookList{
    margin: 10px !important;
    background: #FFF !important;
    border-radius: 35px;
    padding: 10px;
  }
  .search_warp {
    width: 100%;
    background: #F9F9F9;
    min-height: 35px;
    border-radius: 35px;
    padding: 0 10px;
  }

  .carouselMap {
    width: 100%;
    height: auto;
    border-radius: 10px;
    overflow: hidden;
  }

  .carouselMap .carousel_img {
    position: relative;
    width: 100%;
    height: 154px;
    object-fit: cover;
  }

  .carouselMap .carousel_elBox {
    width: 100%;
    height: 28px;
    position: absolute;
    bottom: 0;
    background: rgba(0, 0, 0, 0.15);
    color: #FFF;
  }

  .carouselMap .carousel_title {
    font-weight: 400;
    color: #FFF;
    line-height: 1.3;
    padding: 0 5px;
  }

  .carouselMap .van-swipe__indicators {
    bottom: 3px;
  }

  .carouselMap .van-swipe__indicator {
    background-color: #9C9C9C;
    opacity: 1;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .van-swipe__indicator:not(:last-child) {
    margin-right: 0.1px;
  }

  .classification_item {
    padding: 15px 0;
    width: 25%;
    text-align: center;
    font-weight: bold;
  }

  .card_item {
    box-shadow: 0px 0px 26px -11px rgba(0, 0, 0, 0.4);
    background: #FFF;
    margin-top: 10px;
    padding: 5px;
    border-radius: 16px;
  }

  .itemSex_box {
    margin: 0 -6px;
  }

  .itemSex_item {
    width: 25%;
    padding: 0 6px 5px 6px;
  }

  .itemSex_name {
    color: #5E646D;
    font-weight: 500;
    margin-top: 0.0px;
    text-align: center;
  }

  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }
}