<template>
  <div class="student">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   right-text=""
                   left-arrow
                   @click-right="onClickRight"
                   @click-left="onClickLeft" />
    </van-sticky>
    <div style="padding: 11px 16px;">
      <div class="search_warp flex_box flex_align_center">
        <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
          <van-icon :size="18"
                    :color="'#A5A5A5'"
                    name="search"></van-icon>
        </div>
        <form @click="openSearch()"
              class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;"><input disabled=""
                 id="searchInput"
                 :style="'font-size:15px;'"
                 :placeholder="seachSuggest"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="btnSearch()"
                 v-model="seachText" /></form>
      </div>
    </div>
    <div style="height:10px;width:100%;background:#F4F4F4"></div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="listBox">
          <div v-for="item in dataList"
               :key="item.id">
            <li class="box"
                @click="openBookDetails(item)">
              <div class="item_top flex_box flex_align_center">
                <div class="crus">
                  <img :src="item.url"
                       alt="">
                </div>
                <div class="font">
                  <div>{{item.name}}</div>
                  <div>{{item.name}}</div>
                </div>
              </div>
              <div class="text">
                读书读书读书读书
              </div>
              <div class="item_top flex_box flex_align_center"
                   style="margin-top:10px;background:#F4F4F4;border-radius: 7px;padding:10px;height: 90px">
                <div class="crus">
                  <img :src="item.url"
                       alt="">
                </div>
                <div class="font">
                  <div>{{item.name}}</div>
                  <div>{{item.name}}</div>
                </div>
              </div>
            </li>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <div class="yx"
         @click="addStudent">
      <van-icon name="plus"
                color="#FFFFFF"
                size="40px"
                style="font-weight: bold;" />
    </div>
    <!--添加分类-->
    <van-overlay :show="addCategory"
                 z-index="100"
                 :lock-scroll="false">
      <div class="T-flexbox-vertical flex_align_center flex_justify_content">
        <div class="category_box T-flexbox-vertical"
             style="max-height: 70%;">
          <div class="flex_placeholder"
               style="height:1px;overflow-y: auto;-webkit-overflow-scrolling: touch;">
            <van-empty v-if="switchs.data.length <= 1"
                       :style="'font-size:14px;'"
                       :image="icon_no_data"
                       :description="'暂无分类'"></van-empty>
            <div v-else
                 v-for="(nItem,nIndex) in switchs.data"
                 :key='nItem.id'>
              <div v-if="nIndex != 0"
                   class="category_item flex_box flex_align_center">
                <van-icon @click="editCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'edit'"></van-icon>
                <div @click="clickCategory(nItem,nIndex)"
                     class="flex_placeholder"
                     :style="'color:'+appTheme+';text-align: center;'">{{nItem.label}}</div>
                <van-icon @click="deleteCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'close'"></van-icon>
              </div>
            </div>
          </div>
          <div @click.stop="createCategory()"
               class="category_item flex_box flex_align_center flex_justify_content">
            <van-icon :size="21"
                      :color="appTheme"
                      :name="'plus'"></van-icon>
            <div :style="'color:'+appTheme+';text-align: center;margin:0 0.4rem 0 0.1rem;'">新建分类</div>
          </div>
          <div class="flex_box flex_align_center flex_justify_content">
            <van-tag @click.stop="addCategory = false;"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;"
                     :color="appTheme"
                     plain
                     type="primary">{{'关闭'}}</van-tag>
            <!-- <van-tag @click="clickCategory({label:'',value:''})"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;margin-left: 0.1rem;"
                     :color="appTheme"
                     type="primary">{{'确认'}}</van-tag> -->
          </div>
        </div>
      </div>
    </van-overlay>
    <van-dialog v-model:show="addTypeNameShow"
                title="请输入分类名字"
                :confirmButtonColor="appTheme"
                @confirm="confirmAddTypeName"
                show-cancel-button>
      <van-field v-model="addTypeName"
                 label="分类名字"
                 placeholder="请输入分类名字" />
    </van-dialog>
  </div>
  <div style="height:50px;"></div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, NavBar, Sticky } from 'vant'
export default {
  name: 'student',
  components: {
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [Sticky.name]: Sticky,
    [NavBar.name]: NavBar
  },
  setup () {
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const router = useRouter()
    const data = reactive({
      value: '',
      title: '读书心得',
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      seachSuggest: '请输入关键字', // 建议搜索词
      seachText: '', // 搜索词
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      switchs: { value: '', data: [{ label: '所有书籍', value: '' }] },
      addCategory: false, // 添加分类是否
      optionItem: null,
      editIndex: 0,
      isEdit: false,
      addTypeNameShow: false,
      addTypeName: ''
    })
    onMounted(() => {
      getList(1)
    })
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    // 推荐列表
    const getList = async (type) => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      }
      var res = []
      res = await $api.bookAcademy.recommendlList(datas)
      var { data: list } = res
      var dataListNew = []
      list.forEach((element, index) => {
        var item = {}
        item.index = index + ((data.pageNo - 1) * data.pageSize)
        var itemType = element.type
        if (itemType === 'book') { // 书籍
          element = element.book
          item.count = element.count || ''
          item.type = element.count === 3 ? 'typeThree' : element.count === 6 ? 'typeSex' : 'typeOne'
          item.title = element.mainTitle || ''
          item.smallTitle = element.subhead || ''
          var books = element.books || []
          if (books.length < 1) { // 没关联书籍 但是发布了 不显示
            return
          }
          item.books = books
          if (item.type === 'typeOne') {
            item.nowSwitch = 0// 现在的位置角标
            item.maxSwitch = books.length// 共有几次可以切换 为1时不能切换
            showItemData(item)
            item.btn = { show: true, text: item.maxSwitch > 1 ? '换一本' : '加入书架', plain: item.maxSwitch === 1 }
            if (item.maxSwitch === 1) {
              // item.btn.text = ''
              existMyBook(item)// 如果只有一本 检查是否在书架中
              // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
            }
          } else if (item.type === 'typeThree' || item.type === 'typeSex') {
            item.nowSwitch = 0// 现在的位置角标  向上取整
            item.maxSwitch = Math.ceil(books.length / item.count)
            item.btn = { show: item.maxSwitch > 1, text: '换一批' }
            item.notText = '暂无数据'
            showItemData(item)
          }
        } else if (itemType === 'goldword') { // 金玉良言
          element = element.goldword
          item.type = 'typeWords'
          item.title = '金玉良言'
          item.color = '#DCB769'
          item.Tcolor = '#1987FF'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.content = element.content || ''
          item.id = element.bookId || ''
          item.author = element.authorName || ''
          item.name = element.bookName || ''
        } else if (itemType === 'note') { // 阅读笔记
          element = element.note
          item.type = 'typeNotes'
          item.title = '精选笔记'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.list = []
          var nItem = { islike: element.hasClick, likeNum: element.likenum }
          nItem.url = element.headImg || ''
          nItem.userName = element.createBy || ''
          nItem.userId = element.userId || ''
          nItem.content = element.noteContent || ''
          nItem.allContent = ''
          nItem.showAll = false
          nItem.noteId = element.id || ''
          nItem.id = element.bookId || ''
          nItem.Tcolor = '#1987FF'
          nItem.author = element.authorName || ''
          nItem.name = element.bookName || ''
          item.list.push(nItem)
        } else {

        }
        dataListNew.push(item)
      })
      data.dataList = data.dataList.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    const onClickLeft = () => history.back()
    // 添加心得
    const addStudent = () => {
      router.push({ name: 'addStudent', query: {} })
    }
    // 展示 一本 三本 六本 书
    const showItemData = (_item) => {
      var books = _item.books
      if (_item.type === 'typeOne') {
        _item.id = books[_item.nowSwitch].bookId || ''
        _item.url = books[_item.nowSwitch].coverImgUrl || ''
        _item.txt = { url: books[_item.nowSwitch].bookContentUrl || '', state: 0, schedule: -1, name: books[_item.nowSwitch].bookName || '', bookType: books[_item.nowSwitch].bookType || '3' }
        _item.name = books[_item.nowSwitch].bookName || ''
        _item.hint = books[_item.nowSwitch].recommendWord || ''
      } else if (_item.type === 'typeThree' || _item.type === 'typeSex') {
        var nowData = []
        if (_item.maxSwitch > 1) {
          books.forEach((_eItem, _eIndex, _eArr) => {
            var itemSwitch = Math.ceil((_eIndex + 1) / (_item.count))
            if (itemSwitch - 1 === _item.nowSwitch) {
              nowData.push(_eItem)
            }
          })
        }
        if (nowData.length < _item.count) { // 翻页少于当前数量 再加上去
          var nowLength = _item.count - nowData.length
          books.forEach((_eItem, _eIndex, _eArr) => {
            if (_eIndex < nowLength) {
              nowData.push(_eItem)
            }
          })
        }
        _item.list = []
        nowData.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
          var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
          item.id = _eItem.bookId || ''// 书本id
          item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
          _item.list.push(item)
        })
      }
    }

    // 点击 展示 笔记
    const showNoteDetails = async (_item) => {
      if (!_item.showAll && !_item.allContent) { // 要展示所有的时候 又没有内容的时候 就请求
        var datas = {
          id: _item.noteId
        }
        var { data: list } = await $api.bookAcademy.existBook(datas)
        if (list) {
          _item.allContent = list.noteContent
          _item.showAll = true
        } else {
          _item.showAll = false
        }
      } else {
        _item.showAll = !_item.showAll
      }
    }

    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }

    // 列表点击 换一本或加入书架
    const listBtnClick = (_item) => {
      if (_item.maxSwitch === 1) { // 为1时 加入书架
        if (_item.btn.existMyBook) {
          goBookReader(_item.txt, _item.id)
        } else {
          data.optionItem = _item
          data.addCategory = true
          getTypeList()// 获取所有分类
        }
      } else { // 换一本
        if (_item.nowSwitch + 1 >= _item.maxSwitch) {
          _item.nowSwitch = 0
        } else {
          _item.nowSwitch++
        }
        showItemData(_item)
      }
    }
    // 检查是否在书架
    const existMyBook = async (_item) => {
      var datas = {
        bookId: _item.id
      }
      var { data: list } = await $api.bookAcademy.existBook(datas)
      _item = data.dataList[_item.index]
      _item.btn.existMyBook = list || false
      _item.btn.text = _item.btn.existMyBook ? '继续阅读' : '加入书架'
      _item.btn.show = true
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList(1)
    }

    // 添加到分类里
    const clickCategory = async (_item) => {
      var clickName = data.optionItem.name
      var clickId = data.optionItem.id
      var datas = {
        bookIds: clickId, typeId: _item.value, isSingle: true
      }
      var res = []
      res = await $api.bookAcademy.addBookToType(datas)
      var { data: list } = res
      if (list) {
        data.addCategory = false
        data.optionItem.btn.existMyBook = true
        data.optionItem.btn.text = '开始阅读'
        data.optionItem = null
        Toast('书本【' + clickName + '】添加到' + (!_item.label ? '书架' : ('分类【' + _item.label + '】')) + (list ? '成功' : '失败'))
      }
    }
    const confirmAddTypeName = async () => {
      var typeName = data.addTypeName
      if (!(typeName).replace(/(^\s*)|(\s*$)/g, '')) {
        return Toast('请输入分类名字')
      }
      var datas = {}
      if (data.isEdit) {
        datas = {
          id: typeName, typeName: typeName
        }
        var { data: editData } = await $api.bookAcademy.editType(datas)
        if (editData) {
          Toast('修改分类' + (editData ? '成功' : '失败'))
          data.switchs.data[data.editIndex].label = typeName
          data.addTypeName = ''
        }
      } else {
        datas = {
          typeName: typeName
        }
        var { data: addData } = await $api.bookAcademy.addType(datas)
        if (addData) {
          Toast('新增分类' + (addData ? '成功' : '失败'))
          data.addTypeName = ''
          data.switchs.data.push({ label: typeName, value: addData })
        }
      }
    }
    // 创建新分类
    const createCategory = () => {
      data.addTypeNameShow = true
      data.isEdit = false
    }
    // 修改分类
    const editCategory = (_item, _index) => {
      data.addTypeNameShow = true
      data.addTypeName = _item.label
      data.editIndex = _index
      data.isEdit = true
    }
    // 删除分类
    const deleteCategory = async (_item, _index) => {
      var datas = {
        id: _item.value
      }
      var { data: list } = await $api.bookAcademy.delType(datas)
      if (list === 1) {
        Toast('删除分类' + (list === 1 ? '成功' : '失败'))
        $general.delItemForKey(_index, data.switchs.data)
        if (_item.value === data.switchs.value) { // 当前 正处于分类  回到所有书籍
          data.switchs.value = data.switchs.data[0].value
          onRefresh()
        }
      }
    }
    // 心得
    const onClickRight = async () => {
    }
    // 获取所有分类
    const getTypeList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getALlTypeList(datas)
      data.switchs.data = [{ label: '所有书籍', value: '' }]
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        data.switchs.data.push({ label: _eItem.typeName, value: _eItem.id })
      })
    }
    const onSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    const onClickButton = () => { router.push({ name: 'library', query: {} }) }
    const onChange = () => {

    }
    return { ...toRefs(data), addStudent, onClickRight, onClickLeft, openSearch, onChange, onSearch, onClickButton, getList, openBookDetails, onRefresh, onLoad, showItemData, showNoteDetails, listBtnClick, existMyBook, getTypeList, clickCategory, deleteCategory, createCategory, editCategory, confirmAddTypeName }
  }
}
</script>
<style lang="less" scoped>
@import "./student.less";
</style>
