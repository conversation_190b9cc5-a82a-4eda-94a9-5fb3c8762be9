<template>
  <div class="NetworkPolitics">
    <!-- 网络议政整体情况 -->
    <div class="discussions_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">网络议政整体情况</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">{{ circles }}</span>
        </div>
      </div>
      <div class="discussions_list">
        <div class="discussion_card" v-for="(item, idx) in discussionsList" :key="idx"
          :style="{ backgroundImage: `url(${item.bg})` }">
          <div class="discussion_card_number">{{ item.number }}<span class="discussion_card_unit">{{ item.unit }}</span>
          </div>
          <div class="discussion_card_label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <!-- 发布单位征集次数统计 -->
    <div class="unit_collection_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">发布单位征集次数统计</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_text">{{ circles }}</span>
        </div>
      </div>
      <div class="unit_collection_list">
        <horizontalBarEcharts id="unitcollection" :barList="barList" colorStart="#FFFFFF" colorEnd="#559FFF"
          style="height: 280px;" />
      </div>
    </div>
    <!-- 最热话题 -->
    <div class="hot_topics_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">最热话题</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_more">查看全部</span>
        </div>
      </div>
      <div class="hot_topics_list">
        <div class="hot_topic_item" v-for="(topic, idx) in hotTopics" :key="idx">
          <span class="hot_topic_index" :class="'hot_topic_index_' + (idx + 1)">{{ idx + 1 }}</span>
          <span class="hot_topic_text">{{ topic.title }}</span>
          <span class="hot_topic_tag" :class="'hot_topic_tag_' + (idx + 1)">热</span>
        </div>
      </div>
    </div>
    <!-- 热词分析 -->
    <div class="hot_word_analysis_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">热词分析</span>
        </div>
      </div>
      <div class="hot_word_analysis_list">
        <wordCloudEcharts id="wordcloud" :wordList="wordCloudData"
          :colorList="['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']"
          :sizeRange="[2, 10]" />
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
import horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'
import wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'
export default {
  name: 'NetworkPolitics',
  components: { horizontalBarEcharts, wordCloudEcharts },
  setup () {
    const data = reactive({
      circles: '',
      discussionsList: [
        {
          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),
          number: '103',
          unit: '个',
          label: '发布网络议题'
        },
        {
          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),
          number: '10023',
          unit: '次',
          label: '累计参与人次'
        },
        {
          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),
          number: '256',
          unit: '条',
          label: '累计征求意见'
        }
      ],
      barList: [
        { name: '厅领导', value: 43 },
        { name: '秘书处', value: 67 },
        { name: '研究一室', value: 84 },
        { name: '提案委', value: 52 },
        { name: '经济委', value: 37 },
        { name: '农业农村委', value: 76 },
        { name: '人口资源环境委', value: 26 },
        { name: '科教卫体委', value: 76 },
        { name: '社会和法制委', value: 47 }
      ],
      hotTopics: [
        { title: '推进黄河国家文化公园建设' },
        { title: '持续推进黄河流域生态保护修复，助力…' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' },
        { title: '全面加强新时代中小学劳动教育' }
      ],
      wordCloudData: [
        { name: '乡村振兴', value: 180 },
        { name: '就业优先', value: 165 },
        { name: '科技创新', value: 150 },
        { name: '改革开放', value: 135 },
        { name: '依法治国', value: 120 },
        { name: '教育人才', value: 105 },
        { name: '社会保障', value: 90 },
        { name: '热词', value: 75 },
        { name: '绿色发展', value: 60 },
        { name: '数字中国', value: 45 },
        { name: '共同富裕', value: 40 },
        { name: '文化自信', value: 35 },
        { name: '国家安全', value: 30 },
        { name: '人民至上', value: 25 },
        { name: '中国式现代化', value: 20 }
      ]
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less" scoped>
.NetworkPolitics {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .discussions_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .discussions_list {
      display: flex;
      gap: 10px;
      padding: 20px 15px;
      justify-content: flex-start;

      .discussion_card {
        flex: 1;
        width: 103px;
        height: 77px;
        background-size: 100% 100%;
        background-position: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;

        .discussion_card_number {
          font-size: 20px;
          margin-bottom: 2px;

          .discussion_card_unit {
            font-size: 12px;
            font-weight: normal;
            margin-left: 2px;
          }
        }

        .discussion_card_label {
          font-size: 14px;
          font-weight: 400;
          margin-bottom: 2px;
        }
      }
    }

  }

  .unit_collection_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .unit_collection_list {
      margin-top: 10px;
    }
  }

  .hot_topics_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .hot_topics_list {
      padding: 5px 15px 15px 15px;

      .hot_topic_item {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        padding: 15px 0;

        .hot_topic_index {
          font-size: 14px;
          // margin-right: 10px;
          color: #C7C7C7;
        }

        .hot_topic_index_1 {
          color: #FF4D4F;
        }

        .hot_topic_index_2 {
          color: #FF9900;
        }

        .hot_topic_index_3 {
          color: #FFD600;
        }

        .hot_topic_text {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          color: #666;
          margin-right: 10px;
          margin-left: 10px;
        }

        .hot_topic_tag {
          font-size: 14px;
          color: #fff;
          border-radius: 2px;
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .hot_topic_tag_1 {
          background: #FB3030;
        }

        .hot_topic_tag_2 {
          background: #FF833E;
        }

        .hot_topic_tag_3 {
          background: #FFD978;
        }
      }
    }
  }

  .hot_word_analysis_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .hot_word_analysis_list {
      padding: 5px 15px 15px 15px;
    }
  }
}
</style>
