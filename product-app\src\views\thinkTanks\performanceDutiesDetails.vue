<template>
  <div class="performanceDutiesDetails">
    <div class="info_title">{{ title }}</div>
    <div class="flex_box flex_justify_content">
      <div class="flex_placeholder"></div>
      <div class="info_time">{{ time }}</div>
    </div>
    <div v-html="content"
         class="info_content"></div>
  </div>
</template>
<script>
/* eslint-disable */
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'performanceDutiesDetails',
  components: {
  },
  setup () {
    const route = useRoute()
    // const router = useRouter()
    // const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      id: route.query.id,
      title: route.query.title || '详情',
      content: route.query.content,
      time: route.query.time
    })

    onMounted(() => {
    })
    return { ...toRefs(data), $general }
  }
}
</script>

<style lang="less" >
.performanceDutiesDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;
  .info_title {
    font-weight: bold;
    font-size: 18px;
    margin: 20px 15px;
  }
  .info_time {
    font-size: 14px;
    color: #8a8a8a;
  }
  .info_content {
    text-indent: 2em;
    margin: 10px 20px;
    line-height: 28px;
  }
}
</style>

