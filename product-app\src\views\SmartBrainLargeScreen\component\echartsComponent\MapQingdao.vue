<template>
  <div ref="mapRef" style="width:100%;height:350px;"></div>
</template>
<script>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import qingdaoGeoJson from '@/assets/json/qingdao.json'

export default {
  props: {
    mapData: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const mapRef = ref(null)
    let chart = null
    const renderMap = () => {
      if (!chart && mapRef.value) chart = echarts.init(mapRef.value)
      echarts.registerMap('qingdao', qingdaoGeoJson)
      chart.setOption({
        tooltip: { trigger: 'item' },
        visualMap: {
          min: 0,
          max: 100,
          left: 'left',
          top: 'bottom',
          inRange: { color: ['#b3dafe', '#0271e3'] },
          show: false
        },
        series: [
          {
            name: '区县',
            type: 'map',
            map: 'qingdao',
            zoom: 1.2,
            label: { show: true, color: '#fff', fontSize: 12 },
            data: props.mapData,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: { show: true, color: '#333' },
              itemStyle: { areaColor: '#ffeb3b' }
            }
          }
        ]
      })
    }
    onMounted(renderMap)
    watch(() => props.mapData, renderMap, { deep: true })
    return {
      mapRef
    }
  }
}
</script>
