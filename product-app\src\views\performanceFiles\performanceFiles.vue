<template>
  <div class="performanceFiles">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <van-pull-refresh v-model="refreshing"
                        @refresh="onRefresh">
        <van-list v-model:loading="loading"
                  :finished="finished"
                  finished-text="没有更多了"
                  offset="52"
                  @load="onLoad">
          <div class="top_bg"
               :style="'background: url('+(SYS_IF_ZX?require('../../assets/img/bg_duty_top_zx.png'):require('../../assets/img/bg_duty_top_rd.png'))+') no-repeat;background-position: top;background-size: 100%;'">
            <div style="background:#fff;position:absolute;top:10px;right:10px;color:#fff;border-radius:5px;">
              <van-dropdown-menu class="search-dropdown-menu flex_box flex_align_center"
                                 :active-color="appTheme"
                                 :style="$general.loadConfiguration(-3)+'max-width:100px;'">
                <van-dropdown-item @change="onRefresh();"
                                   v-model="years.value"
                                   get-container="#search"
                                   :options="years.dataFiter"></van-dropdown-item>
              </van-dropdown-menu>
            </div>
            <div class="top_name_box"
                 :style="$general.loadConfiguration(2)+'padding-top:'+(careMode?'25':'40')+'px'">
            </div>
            <div class="duty_des"
                 :style="$general.loadConfiguration(-6)+'margin-top:'+(careMode?'15':'35')+'px;'">说明：以下数据统计范围为<br v-if="careMode" />{{years.value}}.1.1至{{years.value==nowYear?dayjs().format('YYYY.MM.DD'):(years.value+'.12.31')}}
            </div>
            <div v-if="!userId || userId == user.id"
                 class="add_box"
                 @click="addMsg">
              <div class="plus_bg flex_box flex_align_center flex_justify_content"
                   :style="'background:'+appTheme">
                <van-icon name="plus"
                          :size="((appFontSize+1))+'px'"
                          style="font-weight:bold;"></van-icon>
              </div>
              <div :style="$general.loadConfiguration(-6)+';color:'+appTheme">履职补录</div>
            </div>
          </div>
          <!-- 参加会议 -->
          <div class="duty_box"
               :style="'margin-top:-'+(careMode?'37':'40')+'px;'"
               v-if="listdata.meet">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.meet.log" />{{listdata.meet.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.meet.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{ item.sort }}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.meet.type}}</div>
                <div class="duty_content_item_text1 flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 参加活动 -->
          <div class="duty_box"
               v-if="listdata.active">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.active.log" />{{listdata.active.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.active.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.active.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 会议发言 -->
          <div class="duty_box"
               v-if="listdata.conferenceSpeech">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.conferenceSpeech.log" />{{listdata.conferenceSpeech.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.conferenceSpeech.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.conferenceSpeech.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 提案 -->
          <div class="duty_box"
               v-if="listdata.suggest">
            <div class="you">
              <div style="height:20px;"></div>
              <van-circle v-model:current-rate="currentRate"
                          :rate="(Number(listdata.suggest.data[0].num)+Number(listdata.suggest.data[1].num))?((listdata.suggest.data[2].num*100/(Number(listdata.suggest.data[0].num)+Number(listdata.suggest.data[1].num))).toFixed(0)):0"
                          :stroke-width="160"
                          layer-color="#E3F4FF"
                          color="#4DACEB"
                          size="60px"
                          :text="currentRate+'%'"
                          :style="$general.loadConfiguration(-2)+'margin-top: 0px;'"></van-circle>
              <!--<div id="main1"></div>
        <div class="you_percent">{{(Number(listdata.suggest.data[0].num)+Number(listdata.suggest.data[1].num))?((listdata.suggest.data[2].num*100/(Number(listdata.suggest.data[0].num)+Number(listdata.suggest.data[1].num))).toFixed(0)):0}}%</div>-->
              <div :style="$general.loadConfiguration(-6)"
                   class="you_text">被接收占比</div>
            </div>
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.suggest.log" />{{listdata.suggest.title}}
            </div>
            <div class="duty_content_box">
              <div class="duty_content_item"
                   :style="$general.loadConfiguration(-2)"
                   v-for="(item,index) in listdata.suggest.data"
                   :key="index"
                   @click="meetClickInfo(item)">
                {{item.name}}<font class="inherit"
                      :style="'color:'+item.color">{{item.sort}}</font>{{listdata.suggest.type}}<font class="inherit"
                      :style="'color:'+item.color">{{item.num}}</font>分
              </div>
            </div>
          </div>
          <!-- 社情民意 -->
          <div class="duty_box"
               v-if="listdata.bill"
               style="margin-top:10px;">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.bill.log" />{{listdata.bill.title}}
            </div>
            <div class="duty_content_box">
              <div class="duty_content_item"
                   :style="$general.loadConfiguration(-2)"
                   v-for="(item,index) in listdata.bill.data"
                   :key="index"
                   @click="meetClickInfo(item)">
                {{item.name}}<font class="inherit"
                      :style="'color:'+item.color">{{item.num}}</font>{{listdata.bill.type}}
              </div>
            </div>
          </div>
          <!-- 撰写文章 -->
          <div class="duty_box"
               v-if="listdata.writingAnarticle">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.writingAnarticle.log" />{{listdata.writingAnarticle.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.writingAnarticle.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.writingAnarticle.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 履职报告 -->
          <div class="duty_box"
               v-if="listdata.PerformanceReport">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.PerformanceReport.log" />{{listdata.PerformanceReport.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.PerformanceReport.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.PerformanceReport.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{ item.num }}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 公益活动 -->
          <div class="duty_box"
               v-if="listdata.publicBenefit">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.publicBenefit.log" />{{listdata.publicBenefit.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.publicBenefit.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.publicBenefit.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 表彰荣誉  -->
          <div class="duty_box"
               v-if="listdata.recognitionOfHonors">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.recognitionOfHonors.log" />{{listdata.recognitionOfHonors.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.recognitionOfHonors.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.recognitionOfHonors.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 基层履职 -->
          <div class="duty_box"
               v-if="listdata.duties">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.duties.log" />{{listdata.duties.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.duties.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.duties.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 特邀监督员 -->
          <div class="duty_box"
               v-if="listdata.speciallySupervisor">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.speciallySupervisor.log" />{{listdata.speciallySupervisor.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.speciallySupervisor.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.speciallySupervisor.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 其他事项 -->
          <div class="duty_box"
               v-if="listdata.others">
            <div class="duty_box_title_box flex_box"
                 :style="$general.loadConfiguration(0)">
              <img :src="listdata.others.log" />{{listdata.others.title}}
            </div>
            <div class="duty_content_box border_left">
              <div v-for="(item,index) in listdata.others.data"
                   :key="index"
                   class="flex_box">
                <div class="duty_content_item_bg"
                     @click="meetClickInfo(item)">
                  <div class="duty_content_item meetbg flex_box"
                       :style="$general.loadConfiguration(-2)+'background:'+item.bgcolor+';width:6.5rem'">
                  </div>
                  <div class="duty_content_item_name"
                       :style="$general.loadConfiguration(-4)+ 'color:'+item.color">{{item.name}}</div>
                </div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.sort}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">{{listdata.speciallySupervisor.type}}</div>
                <div class="duty_content_item_text flex_box"
                     :style="$general.loadConfiguration(-4)+'color:'+item.color">{{item.num}}
                </div>
                <div class="duty_content_item_text_type"
                     :style="$general.loadConfiguration(-4)">分</div>
              </div>
            </div>
            <!--<div class="down">
          <div id="main3"></div>
        </div>-->
          </div>
          <!-- 履职足迹 -->
          <!-- <div class="duty_title"
               v-if="dutyListData && dutyListData.length != 0">履职足迹</div>
          <div class="line05"></div>
          <div class="">
            <div class="flex_box"
                 v-for="(item,key,index) in dutyListData"
                 :key="index">
              <div class="duty_time">
                <div class="flex_box">
                  <div class="m"
                       v-if="item[0].show">{{key.split("-")[1]}}月</div>
                  <div class="m_no"
                       v-else></div>
                  <div class="c"></div>
                  <div class="d">{{key.split("-")[2]}}日</div>
                </div>
                <div class="line_shu">1</div>
              </div>
              <div class="duty_item_content">
                <div v-for="(nItem,nIndex) in item"
                     :key="nIndex"
                     class="duty_item_content_item"
                     @click=openDetails(nItem)>
                  <div class="mark_right_span">
                    <span class="label"
                          :style="$general.loadConfiguration(-6)">{{nItem.typeNmae}}</span>
                    <span class="label"
                          v-if="nItem.type == '211'"
                          :style="$general.loadConfiguration(-6)+';padding: 1px 10px;background: #fff;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;color:'+appTheme+';border:1px solid '+appTheme">履职补录</span>
                  </div>
                  <div class="mark_right_title"
                       :style="$general.loadConfiguration(0)">{{nItem.title}}</div>
                </div>
              </div>
            </div>
          </div> -->
          <!-- <div v-if="!userId || userId == user.id"
               class="addmsg"
               :style="$general.loadConfiguration()+'background: '+appTheme"
               @click="addMsg">补录</div> -->
        </van-list>
      </van-pull-refresh>
    </div>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'
export default {
  name: 'performanceFiles',
  // beforeRouteLeave (to, from, next) {
  //   // 保存当前滚动位置
  //   const position = document.documentElement.scrollTop || document.body.scrollTop
  //   console.log(position, 'position')
  //   to.params.position = position
  //   next()
  // },
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Circle.name]: Circle,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      careMode: false, // 是否启用了关怀模式
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      userId: route.query.id || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      currentRate: 0,
      name: '',
      year: '',
      Time: '',
      newDate: '',
      listdata: {
        // 参加会议
        meet: {
          title: '参加会议',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '市政协全体会议', num: 2, sort: 0 },
            { name: '市政协常委会议', num: 1, sort: 0 },
            { name: '市政协重要协商会议', num: 1, sort: 0 },
            { name: '市政协各专委会全体成员会议', num: 1, sort: 0 },
            { name: '市政协各委室组织的会议', num: 0, sort: 0 },
            { name: '市政协各委员活动组组织的会议', num: 1, sort: 0 }
          ]
        },
        // 参加活动
        active: {
          title: '参加活动',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '市政协组织的履职培训', num: 2, sort: 0, maxNum: 2 },
            { name: '市政协组织的专题集中学习', num: 1, sort: 0, maxNum: 2 },
            { name: '市政协组织的视察', num: 1, sort: 0, maxNum: 2 },
            { name: '市政协组织的考察', num: 1, sort: 0, maxNum: 2 },
            { name: '市政协组织的调研', num: 0, sort: 0, maxNum: 2 },
            { name: '市政协组织的学习辅导报告会', num: 0, sort: 0, maxNum: 2 },
            { name: '市政协组织的理论研讨会', num: 0, sort: 0, maxNum: 2 },
            { name: '市政协组织的政协讲堂', num: 0, sort: 0, maxNum: 2 },
            { name: '市政协组织的读书会', num: 0, sort: 0, maxNum: 2 }
          ]
        },
        // 会议发言
        conferenceSpeech: {
          title: '会议发言',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '全会口头发言', num: 2, maxNum: 2 },
            { name: '全会书面发言', num: 1, maxNum: 2 },
            { name: '全会分组讨论发言被采用', num: 1, maxNum: 2 },
            { name: '常委会口头发言', num: 1, maxNum: 2 },
            { name: '常委会书面发言', num: 0, maxNum: 2 },
            { name: '其他会议作口头发言并有书面发言材料', num: 0, maxNum: 2 },
            { name: '其他会议提交书面发言材料', num: 0, maxNum: 2 }
            // { name: '其他会议联合提交发言(含书面)材料', num: 0, maxNum: 2 }
          ]
        },
        // 撰写文章
        writingAnarticle: {
          title: '撰写文章',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '撰写建议案文稿', num: 0, sort: 0, maxNum: 2 },
            // { name: '撰写建议案文稿被市领导批示', num: 1, sort: 0, maxNum: 2 },
            { name: '撰写调研报告文稿', num: 0, sort: 0, maxNum: 2 },
            // { name: '撰写调研报告文稿被市领导批示', num: 1, sort: 0, maxNum: 2 },
            { name: '撰写政协信息被采用', num: 0, sort: 0, maxNum: 2 },
            // { name: '撰写政协信息被省级及以上政协采用', num: 0, sort: 0, maxNum: 2 },
            { name: '撰写政协文史被采用', num: 0, sort: 0, maxNum: 2 },
            // { name: '撰写政协文史被省级及以上政协采用', num: 0, sort: 0, maxNum: 2 },
            { name: '撰写征文被采用', num: 0, sort: 0, maxNum: 2 },
            // { name: '撰写征文被省级及以上政协采用', num: 0, sort: 0, maxNum: 2 },
            { name: '读书活动提交音视频被采用', num: 0, sort: 0, maxNum: 2 },
            // { name: '读书活动提交音视频被省级及以上政协采用', num: 0, sort: 0, maxNum: 2 },
            { name: '参与市政协重大宣传活动', num: 0, sort: 0, maxNum: 2 },
            // { name: '参与市政协重大宣传活动所提供的文字或视频被采用', num: 2, sort: 0, maxNum: 2 },
            { name: '为理论研究活动提供稿件', num: 0, sort: 0, maxNum: 2 }
            // { name: '为理论研究活动提供稿件获省级奖励', num: 2, sort: 0, maxNum: 2 },
            // { name: '为理论研究活动提供稿件在市级理论研讨会上做发言', num: 2, sort: 0, maxNum: 2 },
            // { name: '为理论研究活动提供联合撰写的稿件', num: 2, sort: 0, maxNum: 2 }
          ]
        },
        // 履职报告
        PerformanceReport: {
          title: '履职报告',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '按要求提交年度履职报告', num: 0, sort: '', maxNum: 2 },
            { name: '不提交年度履职报告', num: 0, sort: '', maxNum: 2 }
          ]
        },
        // 公益活动
        publicBenefit: {
          title: '公益活动',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '参加公益事业', num: 2, sort: '', maxNum: 2 }
          ]
        },
        // 表彰荣誉
        recognitionOfHonors: {
          title: '表彰荣誉',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '获得县级表彰、奖励和荣誉称号', num: 2, sort: 0, maxNum: 2 },
            { name: '获得市级表彰、奖励和荣誉称号', num: 0, sort: 0, maxNum: 2 },
            { name: '省级表彰、奖励和荣誉称号', num: 1, sort: 0, maxNum: 2 },
            { name: '获得国家级表彰、奖励和荣誉称号', num: 2, sort: 0, maxNum: 2 },
            { name: '受到市政协通报表扬', num: 1, sort: 0, maxNum: 2 }
          ]
        },
        // 基层履职
        duties: {
          title: '基层履职',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '主要牵头建设界别同心汇', num: 2, sort: 0, maxNum: 2 },
            { name: '其他牵头建设界别同心汇', num: 0, sort: 0, maxNum: 2 },
            { name: '牵头建设委员书房、之家、工作室', num: 1, sort: 0, maxNum: 2 }
          ]
        },
        // 特邀监督员
        speciallySupervisor: {
          title: '特邀监督员',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '市政协组织推荐，以政协委员身份去担任特邀监督员', num: 0, sort: 0, maxNum: 2 },
            { name: '担任特邀监督员期间，参加其组织的检查、活动', num: 0, sort: 0, maxNum: 2 }
          ]
        },
        // 其他事项
        others: {
          title: '其他事项',
          log: require('../../assets/img/duty_1.png'),
          type: '次',
          data: [
            { name: '其他事项加分', num: 0, sort: 0, maxNum: 2 },
            { name: '其他事项减分', num: 0, sort: 0, maxNum: 2 }
          ]
        },
        // 提案
        suggest: {
          title: $ifzx ? '提交提案' : '代表建议',
          log: require('../../assets/img/duty_4.png'),
          type: '件',
          data: [
            { name: '提交提案被立案', num: '0', sort: 0, color: '#4DACEB' },
            { name: '被确定为年度重点提案', num: '0', sort: 0, color: '#4DACEB' },
            { name: '被市领导批示肯定', num: '0', sort: 0, color: '#4DACEB' },
            { name: '被列为市领导领办', num: '0', sort: 0, color: '#4DACEB' },
            { name: '通过界别、小组或联组名义提出集体提案', num: '0', sort: 0, color: '#4DACEB' }
          ]
        },
        // 社情民意
        bill: {
          title: $ifzx ? '社情民意' : '代表议案',
          log: require('../../assets/img/duty_3.png'),
          type: '件',
          data: [
            { name: '提交社情民意并被采用', num: '0', color: '#EB4D4D' },
            { name: '社情民意信息被市委、市政府主要领导批示', num: '0', color: '#EB4D4D' },
            { name: '被省政协采用', num: '0', color: '#EB4D4D' },
            { name: '被全国政协采用', num: '0', color: '#EB4D4D' }
          ]
        }
      },
      scrollTop: 0, // 页面划动距离
      dutyListData: {
        '2020-07-14': [{
          title: '暂无履职信息',
          typeNmae: '活动'
        }]
      },
      show: false,
      noPerformance: false,
      footerBtnsShow: true,
      footerBtns: [{ name: '返回顶部', type: 'top' }], // 底部按钮集合 top为返回顶部   btn为按钮
      option1: {
        color: ['#4DACEB', '#E3F4FF'],
        tooltip: {
          show: false
        },
        series: [{
          name: '销量',
          type: 'pie',
          radius: ['70%', '90%'],
          silent: true,
          label: {
            position: 'center',
            formatter: '',
            textStyle: {
              fontSize: 21,
              color: '#c71615'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }]
      },
      option3: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '4%',
          right: '30%',
          bottom: '30%',
          top: '-30%',
          containLabel: true
        },
        //  ------  X轴 ------
        xAxis: {
          // axisLabel: {
          //        fontFamily: "Arial",
          //        fontSize: 8,
          //        interval: 0,
          //        margin: 15,
          //        formatter: function (value) {
          //          return value.split('').join('\n')
          //        }
          // },
          axisTick: { // 坐标轴的刻度
            show: false // 是否显示
          },
          splitLine: { // gird 区域中的分割线
            show: false // 是否显示
          },
          axisLine: { // y轴
            show: false
          },
          axisLabel: { show: false },
          type: 'value', // 轴类型， 默认为 'category'
          data: []
        },
        //   ------   y轴  ----------
        yAxis: {
          type: 'category',
          data: [],
          //      minInterval: 10,
          //      min: 0,
          axisTick: { // 坐标轴的刻度
            show: false // 是否显示
          },
          splitLine: { // gird 区域中的分割线
            show: false // 是否显示
          },
          axisLine: { // y轴
            show: false
          },
          axisLabel: { show: false }
        },
        //  -------   内容数据 -------
        series: [{
          name: '', // 序列名称
          type: 'bar', // 类型
          avoidLabelOverlap: true,
          legendHoverLink: true, // 是否启用图列 hover 时的联动高亮
          //      label: {// 图形上的文本标签
          //        show: false,
          //        position: 'insideTop', // 相对位置
          //        color: '#eee'
          //      },
          itemStyle: { // 图形的形状
            normal: {
              //          label: {
              //            show: true, //开启显示
              //            position: 'top', //在上方显示
              //            textStyle: {//数值样式
              //              color: 'black',
              //              fontSize: 8
              //            }
              //          },
              color: new echarts.graphic.LinearGradient(0, 0, 0, 0.1, [{
                offset: 0.1,
                color: '#FFB6C1'
              }, {
                offset: 0.1,
                color: '#FFB6C1'
              }])
              //          barBorderRadius: 10 //设置圆角
            }
          },
          label: {
            show: true,
            position: 'right',
            textStyle: { // 数值样式
              color: '#EB4D4D',
              fontSize: 8
            },
            formatter: '{b}'
          },
          barGap: '50%', /* 多个并排柱子设置柱子之间的间距 */
          barCategoryGap: '50%', /* 多个并排柱子设置柱子之间的间距 */
          barWidth: 14, // 柱形的宽度
          data: []
        }]
      },
      option4: {
        //  ------  X轴 ------
        xAxis: {
          axisLabel: {
            fontSize: 8,
            interval: 0,
            margin: 10,
            formatter: function (value) {
              return value.split('').join('\n')
            }
          },
          axisTick: { // 坐标轴的刻度
            show: false // 是否显示
          },
          axisLine: { // y轴
            show: false
          },
          type: 'category', // 轴类型， 默认为 'category'
          data: []
        },
        //   ------   y轴  ----------
        yAxis: {
          type: 'value',
          minInterval: 10,
          min: 0,
          axisTick: { // 坐标轴的刻度
            show: false // 是否显示
          },
          splitLine: { // gird 区域中的分割线
            show: false // 是否显示
          },
          axisLine: { // y轴
            show: false
          }
        },
        //  -------   内容数据 -------
        series: [{
          name: '销量', // 序列名称
          type: 'bar', // 类型
          legendHoverLink: true, // 是否启用图列 hover 时的联动高亮
          avoidLabelOverlap: true,
          label: { // 图形上的文本标签
            show: false,
            position: 'insideTop', // 相对位置
            color: '#eee'
          },
          itemStyle: { // 图形的形状
            normal: {
              label: {
                show: true, // 开启显示
                position: 'top', // 在上方显示
                textStyle: { // 数值样式
                  color: 'black',
                  fontSize: 8
                }
              },
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#eca8ab'
              }, {
                offset: 1,
                color: '#bd2727'
              }]),
              barBorderRadius: 10 // 设置圆角
            }
          },
          barWidth: 10, // 柱形的宽度
          data: []
        }]
      },
      years: { value: '', data: [], dataFiter: [] },
      nowYear: ''
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      var nowYear = new Date().getFullYear()
      data.years.dataFiter = []
      for (var i = 2018; i <= nowYear; i++) {
        data.years.data.push(i)
        data.years.dataFiter.push({ text: i, value: i })
      }
      if (route.query.year) {
        data.years.value = Number(route.query.year)
      } else {
        data.years.value = nowYear
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })

    watch(() => data.dataList, (newName, oldName) => {

    })

    const getAppDutyNums = async () => {
      const param = {
        year: data.years.value,
        areaId: sessionStorage.getItem('areaId'),
        userIds: data.userId
      }
      const res = await $api.performanceFiles.getAppDutyNums(param)
      if (res.errcode === 200) {
        // console.log('res.data==>', res.data)
        // 参加会议
        data.listdata.meet.data[0].num = res.data.allMeetingScore
        data.listdata.meet.data[0].sort = res.data.allMeetingInfoVos ? res.data.allMeetingInfoVos.length : 0
        data.listdata.meet.data[1].num = res.data.committeeMeetingScore
        data.listdata.meet.data[1].sort = res.data.committeeMeetingInfoVos ? res.data.committeeMeetingInfoVos.length : 0
        data.listdata.meet.data[2].num = res.data.importantConsultationScore
        data.listdata.meet.data[2].sort = res.data.importantConsultationVos ? res.data.importantConsultationVos.length : 0
        data.listdata.meet.data[3].num = res.data.allSpecialCommitteesScore
        data.listdata.meet.data[3].sort = res.data.allSpecialCommitteesVos ? res.data.allSpecialCommitteesVos.length : 0
        data.listdata.meet.data[4].num = res.data.meetingsVariousScore
        data.listdata.meet.data[4].sort = res.data.meetingsVariousVos ? res.data.meetingsVariousVos.length : 0
        data.listdata.meet.data[5].num = res.data.meetingsMembersScore
        data.listdata.meet.data[5].sort = res.data.meetingsMembersVos ? res.data.meetingsMembersVos.length : 0
        // 参加活动
        data.listdata.active.data[0].num = res.data.performanceTrainingScore
        data.listdata.active.data[0].sort = res.data.performanceTrainingVos ? res.data.performanceTrainingVos.length : 0
        data.listdata.active.data[1].num = res.data.focusLearningScore
        data.listdata.active.data[1].sort = res.data.focusLearningVos ? res.data.focusLearningVos.length : 0
        data.listdata.active.data[2].num = res.data.visitScore
        data.listdata.active.data[2].sort = res.data.visitVos ? res.data.visitVos.length : 0
        data.listdata.active.data[3].num = res.data.inspectScore
        data.listdata.active.data[3].sort = res.data.inspectVos ? res.data.inspectVos.length : 0
        data.listdata.active.data[4].num = res.data.surveyScore
        data.listdata.active.data[4].sort = res.data.surveyVos ? res.data.surveyVos.length : 0
        data.listdata.active.data[5].num = res.data.learningGuidanceScore
        data.listdata.active.data[5].sort = res.data.learningGuidanceVos ? res.data.learningGuidanceVos.length : 0
        data.listdata.active.data[6].num = res.data.theoreticalSeminarScore
        data.listdata.active.data[6].sort = res.data.theoreticalSeminarVos ? res.data.theoreticalSeminarVos.length : 0
        data.listdata.active.data[7].num = res.data.lectureScore
        data.listdata.active.data[7].sort = res.data.lectureVos ? res.data.lectureVos.length : 0
        data.listdata.active.data[8].num = res.data.readingClubScore
        data.listdata.active.data[8].sort = res.data.readingClubVos ? res.data.readingClubVos.length : 0
        // 会议发言
        data.listdata.conferenceSpeech.data[0].num = res.data.plenaryMeetingOralScore
        data.listdata.conferenceSpeech.data[0].sort = res.data.plenaryMeetingOralVos ? res.data.plenaryMeetingOralVos.length : 0
        data.listdata.conferenceSpeech.data[1].num = res.data.plenaryMeetingWrittenScore
        data.listdata.conferenceSpeech.data[1].sort = res.data.plenaryMeetingWrittenVos ? res.data.plenaryMeetingWrittenVos.length : 0
        data.listdata.conferenceSpeech.data[2].num = res.data.plenaryMeetingGroupScore
        data.listdata.conferenceSpeech.data[2].sort = res.data.plenaryMeetingGroupVos ? res.data.plenaryMeetingGroupVos.length : 0
        data.listdata.conferenceSpeech.data[3].num = res.data.municipalStandingScore
        data.listdata.conferenceSpeech.data[3].sort = res.data.municipalStandingVos ? res.data.municipalStandingVos.length : 0
        data.listdata.conferenceSpeech.data[4].num = res.data.meetingWrittenSpeechScore
        data.listdata.conferenceSpeech.data[4].sort = res.data.meetingWrittenSpeechVos ? res.data.meetingWrittenSpeechVos.length : 0
        data.listdata.conferenceSpeech.data[5].num = res.data.otherStatementsScore
        data.listdata.conferenceSpeech.data[5].sort = res.data.otherStatementsVos ? res.data.otherStatementsVos.length : 0
        data.listdata.conferenceSpeech.data[6].num = res.data.otherOralScore
        data.listdata.conferenceSpeech.data[6].sort = res.data.otherOralVos ? res.data.otherOralVos.length : 0
        // data.listdata.conferenceSpeech.data[7].num = res.data.otherWrittenScore
        // data.listdata.conferenceSpeech.data[7].sort = res.data.otherWrittenVos ? res.data.otherWrittenVos.length : 0
        // 基层履职
        data.listdata.duties.data[0].num = res.data.keyConcentricScore
        data.listdata.duties.data[0].sort = res.data.keyConcentricVos ? res.data.keyConcentricVos.length : 0
        data.listdata.duties.data[1].num = res.data.otherConcentricScore
        data.listdata.duties.data[1].sort = res.data.otherConcentricVos ? res.data.otherConcentricVos.length : 0
        data.listdata.duties.data[2].num = res.data.memberHomeScore
        data.listdata.duties.data[2].sort = res.data.memberHomeVos ? res.data.memberHomeVos.length : 0
        // 履职报告
        data.listdata.PerformanceReport.data[0].num = res.data.performanceReportScore
        data.listdata.PerformanceReport.data[0].sort = res.data.performanceReportVos ? res.data.performanceReportVos.length : 0
        data.listdata.PerformanceReport.data[1].num = res.data.performanceReportDeductScore
        data.listdata.PerformanceReport.data[1].sort = res.data.performanceReportDeductVos ? res.data.performanceReportDeductVos.length : 0
        // 特邀监督员
        data.listdata.speciallySupervisor.data[0].num = res.data.speciallyInviteInfoScore
        data.listdata.speciallySupervisor.data[0].sort = res.data.speciallyInviteInfoVos ? res.data.speciallyInviteInfoVos.length : 0
        data.listdata.speciallySupervisor.data[1].num = res.data.speciallyInviteCityScore
        data.listdata.speciallySupervisor.data[1].sort = res.data.speciallyInviteCityVos ? res.data.speciallyInviteCityVos.length : 0
        // 其他事项
        data.listdata.others.data[0].num = res.data.otherBonusInfoScore
        data.listdata.others.data[0].sort = res.data.otherBonusInfoVos ? res.data.otherBonusInfoVos.length : 0
        data.listdata.others.data[1].num = res.data.otherMinusInfoScore
        data.listdata.others.data[1].sort = res.data.otherMinusInfoVos ? res.data.otherMinusInfoVos.length : 0
        // 公益活动
        data.listdata.publicBenefit.data[0].num = res.data.publicWelfareScore
        data.listdata.publicBenefit.data[0].sort = res.data.publicWelfareVos ? res.data.publicWelfareVos.length : 0
        // 表彰荣誉
        data.listdata.recognitionOfHonors.data[0].num = res.data.countyRecognitionScore
        data.listdata.recognitionOfHonors.data[0].sort = res.data.countyRecognitionVos ? res.data.countyRecognitionVos.length : 0
        data.listdata.recognitionOfHonors.data[1].num = res.data.cityRecognitionScore
        data.listdata.recognitionOfHonors.data[1].sort = res.data.cityRecognitionVos ? res.data.cityRecognitionVos.length : 0
        data.listdata.recognitionOfHonors.data[2].num = res.data.provinceRecognitionScore
        data.listdata.recognitionOfHonors.data[2].sort = res.data.provinceRecognitionVos ? res.data.provinceRecognitionVos.length : 0
        data.listdata.recognitionOfHonors.data[3].num = res.data.countryRecognitionScore
        data.listdata.recognitionOfHonors.data[3].sort = res.data.countryRecognitionVos ? res.data.countryRecognitionVos.length : 0
        data.listdata.recognitionOfHonors.data[4].num = res.data.praiseRecognitionScore
        data.listdata.recognitionOfHonors.data[4].sort = res.data.praiseRecognitionVos ? res.data.praiseRecognitionVos.length : 0
        // 提案
        data.listdata.suggest.data[0].num = res.data.acceptResultScore
        data.listdata.suggest.data[0].sort = res.data.acceptResultVos ? res.data.acceptResultVos.length : 0
        data.listdata.suggest.data[1].num = res.data.importantScore
        data.listdata.suggest.data[1].sort = res.data.importantVos ? res.data.importantVos.length : 0
        data.listdata.suggest.data[2].num = res.data.leaderApprovalScore
        data.listdata.suggest.data[2].sort = res.data.leaderApprovalVos ? res.data.leaderApprovalVos.length : 0
        data.listdata.suggest.data[3].num = 0
        data.listdata.suggest.data[3].sort = 0
        data.listdata.suggest.data[4].num = 0
        data.listdata.suggest.data[4].sort = 0
        // 撰写文章
        data.listdata.writingAnarticle.data[0].num = res.data.proposalDraftScore
        data.listdata.writingAnarticle.data[0].sort = res.data.proposalDraftVos ? res.data.proposalDraftVos.length : 0
        // data.listdata.writingAnarticle.data[1].num = res.data.manuscriptCityScore
        // data.listdata.writingAnarticle.data[1].sort = res.data.manuscriptCityVos ? res.data.manuscriptCityVos.length : 0
        data.listdata.writingAnarticle.data[1].num = res.data.manuscriptScore
        data.listdata.writingAnarticle.data[1].sort = res.data.manuscriptVos ? res.data.manuscriptVos.length : 0
        // data.listdata.writingAnarticle.data[3].num = res.data.leaderInstructionsScore
        // data.listdata.writingAnarticle.data[3].sort = res.data.leaderInstructionsVos ? res.data.leaderInstructionsVos.length : 0
        data.listdata.writingAnarticle.data[2].num = res.data.informationEmployScore
        data.listdata.writingAnarticle.data[2].sort = res.data.informationEmployVos ? res.data.informationEmployVos.length : 0
        // data.listdata.writingAnarticle.data[5].num = res.data.informationProvinceScore
        // data.listdata.writingAnarticle.data[5].sort = res.data.informationProvinceVos ? res.data.informationProvinceVos.length : 0
        data.listdata.writingAnarticle.data[3].num = res.data.literatureAndHistoryScore
        data.listdata.writingAnarticle.data[3].sort = res.data.literatureAndHistoryVos ? res.data.literatureAndHistoryVos.length : 0
        // data.listdata.writingAnarticle.data[7].num = res.data.literatureHistoryProvinceScore
        // data.listdata.writingAnarticle.data[7].sort = res.data.literatureHistoryProvinceVos ? res.data.literatureHistoryProvinceVos.length : 0
        data.listdata.writingAnarticle.data[4].num = res.data.manuscriptUtiliseScore
        data.listdata.writingAnarticle.data[4].sort = res.data.manuscriptUtiliseVos ? res.data.manuscriptUtiliseVos.length : 0
        // data.listdata.writingAnarticle.data[9].num = res.data.manuscriptProvinceScore
        // data.listdata.writingAnarticle.data[9].sort = res.data.manuscriptProvinceVos ? res.data.manuscriptProvinceVos.length : 0
        data.listdata.writingAnarticle.data[5].num = res.data.readingActivitiesAudioScore
        data.listdata.writingAnarticle.data[5].sort = res.data.readingActivitiesAudioVos ? res.data.readingActivitiesAudioVos.length : 0
        // data.listdata.writingAnarticle.data[11].num = res.data.readingActivitiesProvinceScore
        // data.listdata.writingAnarticle.data[11].sort = res.data.readingActivitiesProvinceVos ? res.data.readingActivitiesProvinceVos.length : 0
        data.listdata.writingAnarticle.data[6].num = res.data.municipalCPPCCScore
        data.listdata.writingAnarticle.data[6].sort = res.data.municipalCPPCCVos ? res.data.municipalCPPCCVos.length : 0
        // data.listdata.writingAnarticle.data[13].num = res.data.provinceTheoryScore
        // data.listdata.writingAnarticle.data[13].sort = res.data.provinceTheoryVos ? res.data.provinceTheoryVos.length : 0
        data.listdata.writingAnarticle.data[7].num = res.data.theoreticalManuscriptScore
        data.listdata.writingAnarticle.data[7].sort = res.data.theoreticalManuscriptVos ? res.data.theoreticalManuscriptVos.length : 0
        // data.listdata.writingAnarticle.data[15].num = res.data.theoreticalProvinceScore
        // data.listdata.writingAnarticle.data[15].sort = res.data.theoreticalProvinceVos ? res.data.theoreticalProvinceVos.length : 0
        // data.listdata.writingAnarticle.data[16].num = res.data.theoreticalSpeechScore
        // data.listdata.writingAnarticle.data[16].sort = res.data.theoreticalSpeechVos ? res.data.theoreticalSpeechVos.length : 0
        // data.listdata.writingAnarticle.data[17].num = res.data.theoryScore
        // data.listdata.writingAnarticle.data[17].sort = res.data.theoryVos ? res.data.theoryVos.length : 0
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        data.finished = true
      }
    }
    const meetClickInfo = async (item) => {
      console.log('item====>>>', item)
      // router.push({ name: 'performanceInfo', query: { id: data.userId, title: item.name, year: data.years.value } })
    }
    // const getList = async () => {
    //   if (data.pageNo === 1) {
    //     getAppDutyNums()
    //   }
    //   const param = {
    //     pageNo: data.pageNo,
    //     pageSize: data.pageSize,
    //     keyword: data.seachText,
    //     year: data.years.value,
    //     userId: data.userId,
    //     areaId: sessionStorage.getItem('areaId')
    //   }
    //   const res = await $api.performanceFiles.getAppDutyDetail(param)
    //   var { data: list, total } = res
    //   for (var i = 0; i <= list.length; i++) {
    //     var item = list[i]
    //     if (!item) {
    //       continue
    //     }
    //     var index = i
    //     var time1 = new Date().getTime()
    //     var time2 = new Date(item.date.replace(/-/g, '/')).getTime()
    //     if (time1 < time2) {
    //       continue
    //     }
    //     var date = item.date.split(' ')[0]
    //     var nItem = {}
    //     nItem.id = item.id
    //     nItem.title = item.title
    //     nItem.type = item.type
    //     switch (item.type) {
    //       case 'suggest':// 建议
    //         nItem.typeNmae = '建议'
    //         break
    //       case 'proposal':// 提案
    //         nItem.typeNmae = '提案'
    //         break
    //       case 'officeOnline':// 委员值班
    //         nItem.typeNmae = '委员值班'
    //         break
    //       case 'social':// 社情民意
    //         nItem.typeNmae = '社情民意'
    //         break
    //       case 'activity':// 活动
    //       case '49':// 活动
    //         nItem.typeNmae = '活动'
    //         break
    //       case 'survey':// 意见征集
    //         nItem.typeNmae = '意见征集'
    //         break
    //       case 'learning':// 考试
    //         nItem.typeNmae = '学习培训'
    //         break
    //       case 'meet':// 会议
    //         nItem.typeNmae = '会议'
    //         break
    //       case 'bill':// 议案
    //         nItem.typeNmae = '议案'
    //         break
    //       case '211':// 履职补录
    //         nItem.typeNmae = '活动'
    //         break
    //     }
    //     if (data.newDate !== date) {
    //       data.dutyListData[date] = []
    //       data.newDate = date
    //     }
    //     if (index !== 0) {
    //       if (date.split('-')[1] === list[index - 1].date.split(' ')[0].split('-')[1]) {
    //         nItem.show = false
    //       } else {
    //         nItem.show = true
    //       }
    //     } else {
    //       nItem.show = true
    //     }
    //     data.dutyListData[date].push(nItem)
    //   }
    //   data.showSkeleton = false
    //   data.loading = false
    //   data.refreshing = false
    //   // 数据全部加载完成
    //   if (data.dutyListData.length >= total) {
    //     data.finished = true
    //   }
    //   data.finished = true
    // }

    const onRefresh = () => {
      data.pageNo = 1
      data.dutyListData = {}
      data.newDate = ''
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getAppDutyNums()
      // getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      // getList()
    }
    const openDetails = (rows) => {
      console.log(rows)
      var keyList = { suggest: '7', social: '12', proposal: '11', officeOnline: '50', activity: '49', 49: '49', survey: '4', learning: '35', meet: '26', bill: '7.3' }
      rows.relateType = keyList[rows.type]
      if (rows.type === 211) { rows.relateType = '49.1' }
      if (rows.relateType === '12') {
        router.push({ path: 'socialDetails', query: { id: rows.id, title: rows.title } })
      } else if (rows.relateType === '11') {
        router.push({ name: 'proposalDetails', query: { id: rows.id } })
      } else if (rows.relateType === '49') {
        router.push({ path: 'activityDetails', query: { id: rows.id, title: rows.title } })
      } else if (rows.relateType === '50') {
        router.push({ path: 'committeeLivingRoomDetails', query: { id: rows.id, title: rows.title } })
      } else {
        console.log('暂时无法看详情')
        Dialog.alert({
          message: '暂时无法看详情'
        }).then(function () {
        })
      }
    }

    const addMsg = () => {
      var myParam = {}
      myParam.title = '履职补录'// 页面名字
      myParam.paramType = 'lzbl'
      router.push({ name: 'add', query: myParam })
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, confirm, dayjs, openDetails, addMsg, meetClickInfo }
  }
}
</script>
<style lang="less" scoped>
.performanceFiles {
  background: #fff;
  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }

  .clearfix {
    *zoom: 1;
  }

  .w {
    width: 93%;
    margin: auto;
  }

  i {
    font-style: normal;
  }

  .title_box {
    margin-top: 10px;
    padding-bottom: 15px;
    overflow: hidden;
    overflow: scroll;
  }

  .title_box ul {
    width: 2500px;
  }

  .title_box li {
    float: left;
    min-width: 130px;
    height: 100px;
    margin-right: 8px;
    padding: 12px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(249, 249, 249, 1);
    border-radius: 10px;
    box-sizing: border-box;
  }

  .title_box .title {
    height: 32px;
    line-height: 32px;
    margin-bottom: 10px;
  }

  .icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    margin-right: 8px;
    justify-content: center;
  }

  .title_box .title i {
    margin-right: 10px;
  }

  .title_box li:last-child {
    max-width: 187px;
  }

  .title_box li:last-child .title {
    margin-bottom: 0;
  }

  .title_box_img {
    width: 22px;
    height: 22px;
  }

  .title1 {
    padding: 10px 0;
    text-align: center;
  }

  .number {
    line-height: 24px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .liaison {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .num {
    min-width: 37px;
    text-align: center;
    padding: 2px 5px;
    margin-right: 10px;
    border-radius: 5px;
  }

  .box {
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 12px 0px rgba(169, 167, 167, 0.3);
    border-radius: 10px;
    overflow: hidden;
  }

  .common_box {
    padding: 20px;
    border-bottom: 1px solid #f0f2f5;
  }

  .zuo {
    float: left;
    width: 65%;
  }

  .zuo_1 {
    float: left;
  }

  .number1 div {
    margin: 10px 0 0 3px;
    color: #9b999b;
  }

  .you {
    float: right;
    width: 30%;
    position: relative;
    text-align: center;
  }

  .you img {
    width: 80%;
  }

  .you_1 {
    float: left;
    margin: 11px 0 0 7px;
    padding-top: 3px;
    box-sizing: border-box;
    color: #646464;
  }

  /*.you div {height: 100px;}*/
  .bill_you {
    padding-top: 50px;
  }

  .down #main3 {
    margin-top: 0;
    height: 300px;
  }

  .fl {
    float: left;
    margin-right: 5px;
  }

  .mark_box {
    border-top: 1px solid #eee;
    padding-top: 20px;
  }

  .mark_box li {
    margin: 15px 0;
  }

  .mark_left {
    float: left;
    width: 20%;
  }

  .mark_right {
    float: right;
    width: 80%;
    padding-left: 15px;
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
    margin-bottom: 5px;
  }

  /*.mark_right_title {overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;}*/
  .mark_right_span {
    margin-bottom: 5px;
  }

  .time_box {
    height: 25px;
    line-height: 25px;
    display: flex;
    overflow: hidden;
    width: 60px;
  }

  .time_box i {
  }

  .time_box .d {
    font-size: 14px;
    font-weight: 600;
    color: #222222;
  }

  .time_box .c {
    width: 6px;
    height: 6px;
    background: #eb4d4d;
    border: 1px solid #eb4d4d;
    box-shadow: 0px 0px 0 rgba(235, 77, 77, 0.5);
    border-radius: 50%;
    opacity: 1;
    margin: 10px 5px;
  }

  .time_box .m {
    width: 60px;
    text-align: center;
    color: #fff;
    background-color: #d55155;
    font-size: 12px;
    width: 38px;
    background: #eb4d4d;
    opacity: 1;
    border-radius: 9px;
  }

  .mark_title {
    padding: 10px 15px;
    font-weight: 700;
    color: #d55155;
  }

  .ct {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .label {
    padding: 2px 10px;
    margin-right: 10px;
    border-radius: 2px;
    background-color: #d55155;
    color: #fff;
  }

  #app .van-cell .van-cell__title {
    text-align: center;
  }

  .train_box {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: baseline;
    margin: 10px 0 0 3px;
    color: #9b999b;
  }

  .top_bg {
    height: 181px;
    width: 100%;
  }

  .top_name_box {
    font-weight: 600;
    color: #ffffff;
    opacity: 1;
    margin-left: 16px;
    padding-top: 40px;
  }

  .duty_des {
    font-weight: 500;
    color: #eeeeee;
    opacity: 1;
    margin-left: 16px;
  }

  .duty_box {
    width: 94%;
    left: 0;
    right: 0;
    margin: auto;
    height: auto;
    background: #ffffff;
    box-shadow: 0px 3px 25px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 10px 10px;
  }

  .duty_box_title_box {
    height: 45px;
    line-height: 40px;
    font-weight: 600;
    color: #222222;
    opacity: 1;
  }

  .duty_box_title_box img {
    height: 14px;
    margin: 12px 11px;
  }

  .duty_content_item {
    height: 20px;
    font-weight: 500;
    line-height: 20px;
    color: #222222;
    opacity: 1;
    margin-left: 11px;
    padding-bottom: 0.3px;
  }

  .duty_title {
    height: 25px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 25px;
    color: #222222;
    opacity: 1;
  }

  .line05 {
    width: 94%;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 10px;
  }

  .duty_time {
    margin: 10px 10px;
  }

  .line_shu {
    width: 1px;
    background: #eeeeee;
    height: 150%;
    left: 0;
    right: 0;
    margin: auto;
    overflow: hidden;
    margin-top: -10px;
  }

  .duty_time .m {
    white-space: nowrap;
    width: 38px;
    height: 16px;
    background: #eb4d4d;
    border-radius: 9px;
    font-size: 10px;
    font-weight: 600;
    line-height: 16px;
    text-align: center;
    color: #ffffff;
    opacity: 1;
  }

  .duty_time .m_no {
    width: 38px;
    height: 16px;
  }

  .duty_time .c {
    white-space: nowrap;
    width: 6px;
    height: 6px;
    background: #eb4d4d;
    border: 1px solid #eb4d4d;
    box-shadow: 0px 0px 6px rgba(235, 77, 77, 0.5);
    border-radius: 50%;
    opacity: 1;
    margin-top: 5px;
    margin: 5px 5px;
  }

  .duty_time .d {
    white-space: nowrap;
    width: 38px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #222222;
    opacity: 1;
  }

  .duty_item_content {
    margin-top: 35px;
    margin-left: -50px;
  }

  .duty_item_content_item {
    width: 287px;
    background: #ffffff;
    box-shadow: 0px 3px 12px rgba(34, 85, 172, 0.12);
    border-radius: 2px;
    padding: 10px;
    margin-bottom: 10px;
  }

  .you_text {
    text-align: center;
    font-size: 10px;
    font-weight: 500;
    color: #cccccc;
    opacity: 1;
    padding-top: 10px;
  }

  .you_percent {
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #4daceb;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    margin: auto;
    margin-top: 44px;
    text-align: center;
    opacity: 1;
  }

  .border_left {
    border-left: 1px solid #eeeeee;
  }

  .meetbg {
    width: 100%;
    height: 25px;
    background: #eb4d4d;
    opacity: 0.11;
    border-radius: 0 4px 4px 0;
    margin-bottom: 10px;
  }

  .duty_content_item_text {
    padding: 5px 0 0 15px;
    font-size: 12px;
    font-weight: 500;
    color: #eb4d4d;
    opacity: 1;
  }
  .duty_content_item_text1 {
    padding: 5px 0 0 8px;
    font-size: 12px;
    font-weight: 500;
    color: #eb4d4d;
    opacity: 1;
  }

  .duty_content_item_text_type {
    padding: 5px 0 0 5px;
    font-size: 12px;
    font-weight: 500;
  }

  .duty_content_item_bg {
    position: relative;
    width: 100%;
  }

  .duty_content_item_name {
    position: absolute;
    font-size: 12px;
    font-weight: 500;
    color: #eb4d4d;
    opacity: 1;
    top: 3px;
    left: 20px;
  }

  #app .van-circle__text {
    color: #4daceb;
    font-size: inherit;
    font-family: inherit;
  }

  .add_box {
    position: absolute;
    top: 121px;
    right: 30px;
    z-index: 99;
  }

  .plus_bg {
    color: #ffffff;
    text-align: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    left: 0;
    right: 0;
    margin: auto;
    border: 3px solid #ffffff;
  }

  .addmsg {
    width: 56px;
    height: 56px;
    text-align: center;
    background: #3088fe;
    box-shadow: 0 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    opacity: 1;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    color: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 50px;
    margin: auto;
  }
}
</style>
