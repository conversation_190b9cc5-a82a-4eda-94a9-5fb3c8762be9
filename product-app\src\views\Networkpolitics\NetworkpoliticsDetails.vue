<template>
  <div class="NetworkpoliticsDetails">
    <!-- 卡片栏目 -->
    <div class="card">
      <div class="dimback">
        <van-image fit="cover"
                   :src="bgUrl.url"
                   style="width: 100%;height: 100%;"></van-image>
      </div>
      <div class="card_box"
           :style="'top:'+(safeAreaTop+45)+'px'">
        <div v-if="title"
             class="card_title"><span>#</span> {{title}}
          <span>#</span>
        </div>
      </div>
    </div>
    <!-- 内容栏目 -->
    <div class="content">
      <div class="content_box">
        <div class="flex_box flex_align_center">
          <div class="n_details_survey_content">
            <div class="flex_box flex_align_center">
              <img style="margin-right: 0.03rem;border-radius: 50%;width: 50px;height: 55px;"
                   src="../../assets/img/icon_default_user1.png"
                   alt="">
              <div class="flex_placeholder user_name">{{organizeType}}</div>
            </div>
            <div class="n_details_survey_con"
                 v-html="content"></div>
            <template v-if="attachInfo.data&&attachInfo.data.length != 0">
              <div class="general_attach"
                   style="background-color: #fff;">
                <!-- @click="annexClick(item,false)" -->
                <div v-for="(item,index) in attachInfo.data"
                     :key="index"
                     @click="annexClick(item)"
                     class="general_attach_item flex_box flex_align_center click">
                  <img class="general_attach_icon"
                       :style="$general.loadConfigurationSize([5,7])"
                       :src="require(`../../assets/fileicon/${item.iconInfo.name}`)" />
                  <div class="flex_placeholder flex_box flex_align_center">
                    <div class="general_attach_name text_one2"
                         style="font-size: 14px;display: -webkit-box;">{{item.name}}</div>
                    <div class="general_attach_size"
                         style="font-size: 12px;">{{$general.getFileSize(item.size)}}</div>
                  </div>
                </div>
              </div>
            </template>
            <div style="background:#fff;padding: 10px 0px;">
              <div :style="$general.loadConfiguration(-1)">起止时间：</div>
              <div :style="$general.loadConfiguration(-2) + 'margin-top:5px'">{{starTime}}至{{endTime}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 请您建言 -->
    <div class="T-flexbox-vertical">
      <div style="height: 10px;"
           :style="{paddingTop:(safeAreaTop)+'px'}"></div>
      <div class="header_box flex_box flex_align_center van-hairline--bottom">
        <div :style="$general.loadConfigurationSize(0,'h')+'width:5px;background:'+appTheme+';margin-left:16px;'"></div>
        <div class="comment_title"
             :style="$general.loadConfiguration(1)"
             v-html="commentInfo.hint"></div>
        <div class="flex_placeholder"></div>
        <div class="header_btn flex_box flex_align_center flex_justify_content"
             @click="onRefresh"><img :style="$general.loadConfigurationSize(4,'w')"
               src="../../assets/img/icon_refresh.png" /></div>
      </div>
      <!--筛选样式-->
      <div class="deSe_box flex_box"
           :style="$general.loadConfiguration(-3)">
        <div v-for="(item,index) in hotList.data"
             :key="index"
             @click="hotList.value = item.value"
             class="deSe_item inherit"
             :style="'color:'+(item.value==hotList.value?appTheme:'#666')">{{item.name}}</div>
        <div id="dePopover"
             class="deSe_item inherit">
          <van-popover get-container="#dePopover"
                       v-model:show="hotSwitchs.showPopover"
                       trigger="click"
                       placement="bottom-end"
                       :actions="hotSwitchs.data"
                       @select="onSelect">
            <template #reference>
              <div class="flex_box flex_align_center inherit">
                <span class="inherit">{{hotSwitchs.value.text}}</span>
                <van-icon style="margin-left:0.02rem;"
                          :size="((appFontSize)*0.01)+'rem'"
                          color="#666"
                          name="arrow-down"></van-icon>
              </div>
            </template>
          </van-popover>
        </div>
      </div>
      <!--数据列表-->
      <van-pull-refresh v-model="refreshing"
                        @refresh="onRefresh"
                        ref="commentList">
        <van-list v-model:loading="loading"
                  :finished="finished"
                  finished-text="没有更多了"
                  offset="52"
                  @load="onLoad">
          <div v-if="listData.length != 0"
               style="padding: 0 20px;background: #FFF;">
            <template v-for="(item,index) in listData"
                      :key="index">
              <div class="line05"></div>
              <div class="comment_item">
                <div class="flex_box flex_align_center"
                     @click="openCommentDetails(item)">
                  <img :src="item.url"
                       class="comment_img" />
                  <div class="comment_name">{{item.name}}</div>
                  <div v-if="item.tag"
                       :style="$general.loadConfiguration(-6)+'color:'+appTheme"
                       class="comment_tag">{{item.tag}}</div>
                  <div class="flex_placeholder"></div>
                  <div v-if="item.time"
                       class="comment_time">
                    {{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}</div>
                </div>
                <!-- <div v-if="item.title"
                     :style="$general.loadConfiguration()"
                     :class="item.isRead?'comment_read':''"
                     class="comment_titles text_two"
                     v-html="item.title"></div> -->
                <div v-if="item.detail"
                     class="comment_summary">
                  {{item.detail}}</div>
                <div v-if="item.comment_imgs.length != 0"
                     class="comment_imgs_box flex_box T-flex-flow-row-wrap"
                     @click="openCommentDetails(item)">
                  <img v-for="(nItem,nIndex) in item.comment_imgs"
                       :key="nIndex"
                       @click.stop="previewImg(item.comment_imgs,nIndex)"
                       :src="nItem.url"
                       class="comment_imgs_item" />
                </div>
                <div class="flex_box flex_align_center">
                  <div v-if="item.auditSituation == '1'"
                       :style="$general.loadConfiguration(-6)+'color:#F00'"
                       class="comment_tag">待审核</div>
                  <!-- v-if="item.hasDelete" -->
                  <div class="flex_placeholder"></div>
                  <div @click.stop="fabulousInfo(item)"
                       class="flex_box flex_align_center">
                    <van-icon :color="item.isFabulous?appTheme:'#ABABAB'"
                              :size="((appFontSize+2)*0.01)+'rem'"
                              :name="item.isFabulous?'good-job':'good-job-o'"></van-icon>
                    <div :style="$general.loadConfiguration(-2)"
                         class="comment_like_txt">{{item.fabulousNumber}}
                    </div>
                  </div>
                  <div @click.stop="clickPL(item,false,1)"
                       class="flex_box flex_align_center"
                       style="position: relative;margin-left: 22px;">
                    <div v-if="false"
                         class="comment_comment_hot"></div>
                    <van-icon :color="'#ABABAB'"
                              :size="((appFontSize+1)*0.01)+'rem'"
                              name="comment-o">
                    </van-icon>
                    <div :style="$general.loadConfiguration(-2)"
                         class="comment_comment_txt">{{item.commentNumber}}
                    </div>
                  </div>
                  <van-icon :color="'#333'"
                            class="delete_btn"
                            v-if="item.showDelete"
                            :size="(($general.appFontSize+1)*0.01)+'px'"
                            name="delete"
                            @click="deleteItem(item,index,dataList)"></van-icon>
                </div>
              </div>
            </template>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <!-- <commentList ref="commentList"
                 @openInputBoxEvent="openInputBoxEvent"
                 @freshState="freshState"
                 :commentData="commentData"
                 :type="type"
                 :id="id" /> -->
    <div style="height:60px;"></div>
    <footer class="footerBox">
      <inputBoxs ref="inputBox"
                 :inputData="inputData"
                 :surveyDetailsTitle="title"
                 :title="title"
                 @addCommentEvent="addCommentEvent"
                 :type="type"
                 :id="id" />
    </footer>
  </div>
</template>
<script>
/* eslint-disable */
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { ImagePreview, Dialog, Toast, Popover, Image as VanImage } from 'vant'
import inputBoxs from '../../components/inputBoxs/inputBoxs.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'NetworkpoliticsDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component,
    [Popover.name]: Popover,
    [VanImage.name]: VanImage,
    inputBoxs,
    commentList
  },
  setup (context) {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      active: '0',
      appTheme: sessionStorage.getItem('appTheme'),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      id: route.query.id,
      type: 5,
      bgUrl: { url: '' },
      title: '',
      starTime: '',
      endTime: '',
      content: '',
      organizeType: '', // 部门
      attachInfo: { name: "附件", data: [] }, //附件对象
      inputData: {
        showTitleInput: 1,
        input_placeholder: '请您建言……', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentInfo: { name: '', hint: '请您建言' },
      commentData: {},
      listData: [], // 评论列表
      commentList: null,
      inputBox: null,
      showDelete: false,
      hotList: { value: '1', data: [{ name: "时间排序", value: "1" }, { name: "热度排序", value: "2" }] },
      hotSwitchs: { showPopover: false, value: { text: '党派筛选', value: "" }, data: [{ text: '党派筛选', value: "" }, { text: '市政协委员', value: "1" }, { text: '民主党派', value: "2" }, { text: '政协机关', value: "3" }, { text: '区市政协', value: "4" }, { text: '群众', value: "5" }] },
    })

    onMounted(() => {
      addBrowse()
      redPointSign()
      getDetails()
      adviceList()
    })
    watch(() => data.hotList.value, (newName, oldName) => {
      onRefresh()
    })
    watch(() => data.hotSwitchs.value, (newName, oldName) => {
      onRefresh()
    })
    // 增加浏览记录
    const addBrowse = async () => {
      const res = await $api.Networkpolitics.browseSave({
        keyId: data.id,
        type: 4,
        areaId: data.user.areaId
      })
      data.browse = res.data
    }
    // 去除红点
    const redPointSign = async () => {
      const res = await $api.Networkpolitics.redPointSign({
        id: data.id,
        type: 'survey'
      })
      data.browse = res.data
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      adviceList()
    }
    const onLoad = () => {
      data.loading = true
    }
    //筛选确定
    const onSelect = (_action) => {
      data.hotSwitchs.value = _action
    }
    // 获取详情
    const getDetails = async () => {
      const res = await $api.Networkpolitics.info(data.id)
      var { data: info } = res
      data.title = info.title || ""
      data.content = info.content || ""
      data.starTime = info.starTime || "";
      data.endTime = info.endTime || "";
      data.id = info.id || ""
      data.organizeType = info.organizeType || ""
      var attachmentList = info.attachmentList || []
      data.attachInfo.data = []
      if (attachmentList.length != 0) {
        data.bgUrl.url = $general.getItemForKey('splashImg', attachmentList, 'moduleType').filePath
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) {//item index 原数组对象
          if (_eItem.moduleType == "splashBackGround" || _eItem.moduleType == "splashImg") {
            return
          }
          var item = {}
          item.url = _eItem.filePath || ""
          item.name = _eItem.fileName || ""
          item.size = _eItem.fileSize || ""
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          // that.annexCheck(item);//附件检测 拿到附件缓存 信息
          data.attachInfo.data.push(item)
          console.log('')
        })
      }
    }
    // 获取评论列表
    const adviceList = async () => {
      var postParam = {
        pageNo: data.pageNo,
        pageSize: 200,
        auditSituation: 2,
        surveyId: data.id,
        sort: data.hotList.value,
        townhallIdentity: data.hotSwitchs.value.value
      }
      const res = await $api.Networkpolitics.Advicelist(postParam)
      var { data: list, total } = res
      var newData = []
      list.forEach(_eItem => {
        var item = {}
        item.id = _eItem.id || "";
        item.url = _eItem.headImg || "";
        item.name = _eItem.name || "";
        item.tag = _eItem.townhallIdentity || "";
        item.time = _eItem.createDate || "";
        item.title = _eItem.title || "";
        item.detail = _eItem.detail || "";
        item.auditSituation = _eItem.auditSituation || "";
        item.isFabulous = _eItem.isFabulous == 1;
        item.isRead = _eItem.isRead == 1;
        item.fabulousNumber = Number(_eItem.fabulousNumber || "0");
        if (dayjs().isBefore(dayjs('2022-02-22'))) {//在下周二之前 加上20
          item.fabulousNumber = item.fabulousNumber + 20;
        }
        item.commentNumber = _eItem.commentNumber || "0";
        item.comment_imgs = [];
        var attachmentList = _eItem.attachmentList || [];
        attachmentList.forEach(function (_aItem, _aIndex, _aArr) {
          item.comment_imgs.push({ url: _aItem.filePath });
        });
        if (data.user.id !== _eItem.createBy) {
          item.showDelete = false
        } else {
          item.showDelete = true
        }
        newData.push(item)
      })
      data.listData = data.listData.concat(newData)
      console.log('data.listData==>', data.listData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 打开附件
    const annexClick = (_item) => {
      console.log('_item===', _item)
      // if (_item.iconInfo.type === 'pdf') {
      //   if (window.location.origin === 'http://59.224.134.152:81') {
      //     window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + _item.url)
      //   } else {
      //     window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + _item.url)
      //   }
      // } else {
      var param = {
        id: _item.id,
        url: _item.url,
        name: _item.name
      }
      router.push({ name: 'superFile', query: param })
      // }
    }
    const addCommentEvent = (value) => {
      onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      console.log(value)
      data.inputBox.getStats()
    }
    //点赞或取消点赞
    const fabulousInfo = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousNumber++
      } else {
        _item.fabulousNumber--
      }
      var url = 'fabulous/del'
      if (_item.isFabulous) {
        url = 'fabulous/save'
      }
      $api.Networkpolitics.fabulous(url, {
        keyId: _item.id,
        type: '60',
        areaId: data.user.areaId
      }).then(res => {
        console.log('点赞或取消点赞===>>', res)
      })
    }
    const clickPL = (item) => {
      // 打开评论框
      data.inputBox.changeType(2, item, false)
    }
    const previewImg = (imgs, _index) => {
      var img = []
      imgs.forEach(element => {
        img.push(element.url)
      })
      ImagePreview(img, _index)
    }
    const openCommentDetails = (_item) => {
      router.push({ name: 'NetworkCommentDetails', query: { id: _item.id, url: _item.url, name: _item.name, time: _item.time, content: _item.detail } })
    }
    const deleteItem = (item) => {
      Dialog.confirm({
        title: '',
        message: '确定要删除这条评论吗?'
      }).then(async () => {
        const res = await $api.Networkpolitics.delsComment({
          ids: item.id
        })
        console.log('res.errcod', res.errcode)
        if (res.errcode === 200) {
          Toast('删除成功')
          onRefresh()
          // context.emit('freshState', true)
        }
      }).catch(() => {
        // on cancel
      })
    }
    return { ...toRefs(data), onRefresh, dayjs, onLoad, $general, freshState, addCommentEvent, openInputBoxEvent, previewImg, fabulousInfo, clickPL, openCommentDetails, deleteItem, onSelect, annexClick }
  }
}
</script>

<style lang="less" >
.NetworkpoliticsDetails {
  width: 100%;
  .card {
    position: absolute;
    width: 100%;
    height: 5rem;
    box-sizing: border-box;
    .dimback .van-image {
      opacity: 0.6;
      filter: alpha(opacity=60);
      filter: blur(1px);
      -webkit-filter: blur(1px);
    }

    .dimback {
      background: #000;
      width: 100%;
      height: 100%;
    }
    .card_box {
      position: absolute;
      left: 0.16rem;
      top: 0.74rem;
      padding-right: 0.16rem;
      z-index: 999;
      // display: flex;
      // justify-content: center;
      // align-items: center;
      // position: relative;
      // top: -4.2rem;
      span {
        color: #fff44b;
      }
      .card_title {
        margin-bottom: 0.12rem;
        font-weight: bold;
        color: #fff;
        line-height: 1.5;
        font-size: 18px;
      }
    }
  }
  .content {
    position: relative;
    padding-top: 4.3rem;
    margin-bottom: 0.1rem;
    .content_box {
      width: 100%;
      background-color: #fff;
      border-radius: 14px 14px 0px 0px;
      z-index: 888;
      padding: 15px 14px;
      .n_details_survey_content {
        width: 100%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        box-sizing: border-box;
        .user_name {
          font-weight: bold;
          color: #333;
          margin-left: 6px;
          padding: 15px 14px;
        }
        .n_details_survey_con {
          margin-top: 15px;
          line-height: 30px;
        }
        // .n_details_survey_top {
        //   width: 100%;
        //   height: 30px;
        //   color: #a8a8a8;
        //   display: flex;
        //   align-items: center;
        //   justify-content: space-between;
        //   .n_details_survey_top_time {
        //     font-size: 14px;
        //   }
        //   .n_details_survey_top_text {
        //     font-size: 14px;
        //   }
        // }
      }
    }
    .details_content {
      font-size: 17px;
      text-indent: 2em;
      line-height: 1.5;
      padding: 0 10px 15px 10px;
    }
  }

  #app,
  .T-flexbox-vertical {
    height: 100%;
  }
  .header_box {
    background: #fff;
    width: 100%;
    .header_btn {
      color: #555;
      padding: 9px 10px;
    }
  }

  .comment_item {
    padding: 10px 0;
    .comment_img {
      width: 35px;
      height: 42px !important;
      margin-right: 7px;
    }
    .comment_name {
      font-size: 14px;
    }
    .comment_time {
      font-size: 12px;
      color: #c6c6c6;
    }
    .comment_titles {
      font-size: 16px;
      font-weight: bold;
      line-height: 1.4;
      margin: 10px 0;
      color: #333333;
    }
    .comment_summary {
      // line-height: 1.5;
      color: #666;
      margin-bottom: 10px;
      margin-top: 15px;
      font-size: 14px;
    }
    .comment_imgs_item {
      width: 100px;
      height: 100px;
      object-fit: cover;
      margin-bottom: 10px;
      margin-right: 10px;
      margin-top: 10px;
    }
    .comment_like_txt,
    .comment_comment_txt {
      margin-left: 5px;
      color: #999999;
    }
  }
  .comment_title {
    padding: 10px;
    color: #000;
  }
  .delete_btn {
    padding: 2px 5px;
    margin-left: 15px;
  }
  .deSe_box {
    background: #fff;
    padding-bottom: 10px;
  }

  .deSe_item {
    width: 100%;
    text-align: center;
    padding: 7px 0;
  }
}
</style>

