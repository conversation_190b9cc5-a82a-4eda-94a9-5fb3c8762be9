
const performanceFilesList = () => import('@/views/performanceFiles/performanceFilesList')
const performanceFiles = () => import('@/views/performanceFiles/performanceFiles')
const performanceInfo = () => import('@/views/performanceFiles/performanceInfo')
const performanceFilesTypeList = () => import('@/views/performanceFiles/performanceFilesTypeList')

const performanceFilesRoter = [{
  path: '/performanceFilesList',
  name: 'performanceFilesList',
  component: performanceFilesList,
  meta: {
    title: '履职列表',
    keepAlive: true
  }
},
{
  path: '/performanceFiles',
  name: 'performanceFiles',
  component: performanceFiles,
  meta: {
    title: '履职信息',
    keepAlive: true
  }
},
{
  path: '/performanceInfo',
  name: 'performanceInfo',
  component: performanceInfo,
  meta: {
    title: '履职信息详情',
    keepAlive: true
  }
},
{
  path: '/performanceFilesTypeList',
  name: 'performanceFilesTypeList',
  component: performanceFilesTypeList,
  meta: {
    title: '履职信息详情',
    keepAlive: true
  }
}]
export default performanceFilesRoter
