{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\router\\module\\ImportantWork.js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\router\\module\\ImportantWork.js", "mtime": 1755080752679}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ImportantWork", "WorkNoticeList", "WorkNoticeDetails", "WorkTeamList", "WorkTeamColumnList", "WorkTeamDetails", "MajorProjectsList", "MajorProjectsColumnList", "MajorProjectsDetails", "contactCompanyList", "contactCompanyColumnList", "contactCompanyDetails", "villageContactPointList", "villageContactPointDetails", "contactServiceExpertsList", "contactServiceExpertsDetails", "attractInvestmentList", "attractInvestmentColumnList", "attractInvestmentDetails", "pdfFilePreview", "attractInvestmentDetailsCopy", "MajorProjectsDetailsCopy", "Networkpolitics", "path", "name", "component", "meta", "title", "keepAlive"], "sources": ["D:/zy/xm/h5/qdzx_h5/product-app/src/router/module/ImportantWork.js"], "sourcesContent": ["const ImportantWork = () => import('@/views/ImportantWork/ImportantWork')\r\nconst WorkNoticeList = () => import('@/views/ImportantWork/WorkNotice/WorkNoticeList')\r\nconst WorkNoticeDetails = () => import('@/views/ImportantWork/WorkNotice/WorkNoticeDetails')\r\nconst WorkTeamList = () => import('@/views/ImportantWork/WorkTeam/WorkTeamList')\r\nconst WorkTeamColumnList = () => import('@/views/ImportantWork/WorkTeam/WorkTeamColumnList')\r\nconst WorkTeamDetails = () => import('@/views/ImportantWork/WorkTeam/WorkTeamDetails')\r\nconst MajorProjectsList = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsList')\r\nconst MajorProjectsColumnList = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsColumnList')\r\nconst MajorProjectsDetails = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsDetails')\r\nconst contactCompanyList = () => import('@/views/ImportantWork/contactCompany/contactCompanyList')\r\nconst contactCompanyColumnList = () => import('@/views/ImportantWork/contactCompany/contactCompanyColumnList')\r\nconst contactCompanyDetails = () => import('@/views/ImportantWork/contactCompany/contactCompanyDetails')\r\nconst villageContactPointList = () => import('@/views/ImportantWork/villageContactPoint/villageContactPointList')\r\nconst villageContactPointDetails = () => import('@/views/ImportantWork/villageContactPoint/villageContactPointDetails')\r\nconst contactServiceExpertsList = () => import('@/views/ImportantWork/contactServiceExperts/contactServiceExpertsList')\r\nconst contactServiceExpertsDetails = () => import('@/views/ImportantWork/contactServiceExperts/contactServiceExpertsDetails')\r\nconst attractInvestmentList = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentList')\r\nconst attractInvestmentColumnList = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentColumnList')\r\nconst attractInvestmentDetails = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentDetails')\r\nconst pdfFilePreview = () => import('@/views/pdfFilePreview/pdfFilePreview')\r\nconst attractInvestmentDetailsCopy = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentDetailsCopy')\r\nconst MajorProjectsDetailsCopy = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsDetailsCopy')\r\nconst Networkpolitics = [\r\n  {\r\n    path: '/ImportantWork',\r\n    name: 'ImportantWork',\r\n    component: ImportantWork,\r\n    meta: {\r\n      title: '重点工作首页',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/WorkNoticeList',\r\n    name: 'WorkNoticeList',\r\n    component: WorkNoticeList,\r\n    meta: {\r\n      title: '重点工作的工作通知列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/WorkNoticeDetails',\r\n    name: 'WorkNoticeDetails',\r\n    component: WorkNoticeDetails,\r\n    meta: {\r\n      title: '重点工作的工作通知详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/WorkTeamList',\r\n    name: 'WorkTeamList',\r\n    component: WorkTeamList,\r\n    meta: {\r\n      title: '重点工作的工作专班列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/WorkTeamColumnList',\r\n    name: 'WorkTeamColumnList',\r\n    component: WorkTeamColumnList,\r\n    meta: {\r\n      title: '重点工作的工作专班栏目列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/WorkTeamDetails',\r\n    name: 'WorkTeamDetails',\r\n    component: WorkTeamDetails,\r\n    meta: {\r\n      title: '重点工作的工作专班详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/MajorProjectsList',\r\n    name: 'MajorProjectsList',\r\n    component: MajorProjectsList,\r\n    meta: {\r\n      title: '重点工作的顶格推进重大项目列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/MajorProjectsColumnList',\r\n    name: 'MajorProjectsColumnList',\r\n    component: MajorProjectsColumnList,\r\n    meta: {\r\n      title: '重点工作的顶格推进重大项目栏目列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/MajorProjectsDetails',\r\n    name: 'MajorProjectsDetails',\r\n    component: MajorProjectsDetails,\r\n    meta: {\r\n      title: '重点工作的顶格推进重大项目详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/contactCompanyList',\r\n    name: 'contactCompanyList',\r\n    component: contactCompanyList,\r\n    meta: {\r\n      title: '重点工作的联系企业列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/contactCompanyColumnList',\r\n    name: 'contactCompanyColumnList',\r\n    component: contactCompanyColumnList,\r\n    meta: {\r\n      title: '重点工作的联系企业栏目列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/contactCompanyDetails',\r\n    name: 'contactCompanyDetails',\r\n    component: contactCompanyDetails,\r\n    meta: {\r\n      title: '重点工作的联系企业详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/villageContactPointList',\r\n    name: 'villageContactPointList',\r\n    component: villageContactPointList,\r\n    meta: {\r\n      title: '重点工作的乡村振兴联系点列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/villageContactPointDetails',\r\n    name: 'villageContactPointDetails',\r\n    component: villageContactPointDetails,\r\n    meta: {\r\n      title: '重点工作的乡村振兴联系点详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/contactServiceExpertsList',\r\n    name: 'contactServiceExpertsList',\r\n    component: contactServiceExpertsList,\r\n    meta: {\r\n      title: '重点工作的联系服务专家列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/contactServiceExpertsDetails',\r\n    name: 'contactServiceExpertsDetails',\r\n    component: contactServiceExpertsDetails,\r\n    meta: {\r\n      title: '重点工作的联系服务专家详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/attractInvestmentList',\r\n    name: 'attractInvestmentList',\r\n    component: attractInvestmentList,\r\n    meta: {\r\n      title: '重点工作的招商引资情况统计列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/attractInvestmentColumnList',\r\n    name: 'attractInvestmentColumnList',\r\n    component: attractInvestmentColumnList,\r\n    meta: {\r\n      title: '重点工作的招商引资情况统计栏目列表',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/attractInvestmentDetails',\r\n    name: 'attractInvestmentDetails',\r\n    component: attractInvestmentDetails,\r\n    meta: {\r\n      title: '重点工作的招商引资情况统计详情',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/pdfFilePreview',\r\n    name: 'pdfFilePreview',\r\n    component: pdfFilePreview,\r\n    meta: {\r\n      title: '附件预览',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/attractInvestmentDetailsCopy',\r\n    name: 'attractInvestmentDetailsCopy',\r\n    component: attractInvestmentDetailsCopy,\r\n    meta: {\r\n      title: '重点工作的招商引资情况统计详情copy',\r\n      keepAlive: true\r\n    }\r\n  },\r\n  {\r\n    path: '/MajorProjectsDetailsCopy',\r\n    name: 'MajorProjectsDetailsCopy',\r\n    component: MajorProjectsDetailsCopy,\r\n    meta: {\r\n      title: '重点工作的招商引资情况统计详情copy',\r\n      keepAlive: true\r\n    }\r\n  }\r\n]\r\nexport default Networkpolitics\r\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;AACzE,MAAMC,cAAc,GAAGA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC;AACtF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC;AAC5F,MAAMC,YAAY,GAAGA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC;AAChF,MAAMC,kBAAkB,GAAGA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC;AAC5F,MAAMC,eAAe,GAAGA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;AACtF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM,MAAM,CAAC,uDAAuD,CAAC;AAC/F,MAAMC,uBAAuB,GAAGA,CAAA,KAAM,MAAM,CAAC,6DAA6D,CAAC;AAC3G,MAAMC,oBAAoB,GAAGA,CAAA,KAAM,MAAM,CAAC,0DAA0D,CAAC;AACrG,MAAMC,kBAAkB,GAAGA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC;AAClG,MAAMC,wBAAwB,GAAGA,CAAA,KAAM,MAAM,CAAC,+DAA+D,CAAC;AAC9G,MAAMC,qBAAqB,GAAGA,CAAA,KAAM,MAAM,CAAC,4DAA4D,CAAC;AACxG,MAAMC,uBAAuB,GAAGA,CAAA,KAAM,MAAM,CAAC,mEAAmE,CAAC;AACjH,MAAMC,0BAA0B,GAAGA,CAAA,KAAM,MAAM,CAAC,sEAAsE,CAAC;AACvH,MAAMC,yBAAyB,GAAGA,CAAA,KAAM,MAAM,CAAC,uEAAuE,CAAC;AACvH,MAAMC,4BAA4B,GAAGA,CAAA,KAAM,MAAM,CAAC,0EAA0E,CAAC;AAC7H,MAAMC,qBAAqB,GAAGA,CAAA,KAAM,MAAM,CAAC,+DAA+D,CAAC;AAC3G,MAAMC,2BAA2B,GAAGA,CAAA,KAAM,MAAM,CAAC,qEAAqE,CAAC;AACvH,MAAMC,wBAAwB,GAAGA,CAAA,KAAM,MAAM,CAAC,kEAAkE,CAAC;AACjH,MAAMC,cAAc,GAAGA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC;AAC5E,MAAMC,4BAA4B,GAAGA,CAAA,KAAM,MAAM,CAAC,sEAAsE,CAAC;AACzH,MAAMC,wBAAwB,GAAGA,CAAA,KAAM,MAAM,CAAC,8DAA8D,CAAC;AAC7G,MAAMC,eAAe,GAAG,CACtB;EACEC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEzB,aAAa;EACxB0B,IAAI,EAAE;IACJC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAExB,cAAc;EACzByB,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEvB,iBAAiB;EAC5BwB,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEtB,YAAY;EACvBuB,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAErB,kBAAkB;EAC7BsB,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEpB,eAAe;EAC1BqB,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEnB,iBAAiB;EAC5BoB,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAElB,uBAAuB;EAClCmB,IAAI,EAAE;IACJC,KAAK,EAAE,mBAAmB;IAC1BC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEjB,oBAAoB;EAC/BkB,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEhB,kBAAkB;EAC7BiB,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEf,wBAAwB;EACnCgB,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEd,qBAAqB;EAChCe,IAAI,EAAE;IACJC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEb,uBAAuB;EAClCc,IAAI,EAAE;IACJC,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,6BAA6B;EACnCC,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEZ,0BAA0B;EACrCa,IAAI,EAAE;IACJC,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,4BAA4B;EAClCC,IAAI,EAAE,2BAA2B;EACjCC,SAAS,EAAEX,yBAAyB;EACpCY,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,+BAA+B;EACrCC,IAAI,EAAE,8BAA8B;EACpCC,SAAS,EAAEV,4BAA4B;EACvCW,IAAI,EAAE;IACJC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAET,qBAAqB;EAChCU,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,8BAA8B;EACpCC,IAAI,EAAE,6BAA6B;EACnCC,SAAS,EAAER,2BAA2B;EACtCS,IAAI,EAAE;IACJC,KAAK,EAAE,mBAAmB;IAC1BC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEP,wBAAwB;EACnCQ,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEN,cAAc;EACzBO,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,+BAA+B;EACrCC,IAAI,EAAE,8BAA8B;EACpCC,SAAS,EAAEL,4BAA4B;EACvCM,IAAI,EAAE;IACJC,KAAK,EAAE,qBAAqB;IAC5BC,SAAS,EAAE;EACb;AACF,CAAC,EACD;EACEL,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,0BAA0B;EAChCC,SAAS,EAAEJ,wBAAwB;EACnCK,IAAI,EAAE;IACJC,KAAK,EAAE,qBAAqB;IAC5BC,SAAS,EAAE;EACb;AACF,CAAC,CACF;AACD,eAAeN,eAAe", "ignoreList": []}]}