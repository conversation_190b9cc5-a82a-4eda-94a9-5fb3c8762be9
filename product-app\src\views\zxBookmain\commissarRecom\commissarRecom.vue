<template>
  <div class="commissarRecom">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <div class="commissarRecom_type">
      <span id="typeone"
            @click="getListType(0)">最新</span> | <span id="typetwo"
            @click="getListType(1)">最热</span>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="listBox">
          <div v-for="item in dataList"
               :key="item.id">
            <li class="item_body_warp">
              <div class="item_body typeOne T-flexbox-vertical flex_align_center">
                <div class="item_img">
                  <img v-if="item.coverImg"
                       @click="openBookDetails(item)"
                       :src="item.coverImg"
                       style="object-fit: cover;border-radius: 2px;" />
                  <div v-if="item.entity"
                       class="item_entity"
                       v-html="'实体书'"></div>
                </div>
                <div v-if="item.bookName"
                     @click="openBookDetails(item)"
                     class="item_name"
                     v-html="item.bookName"></div>
                <div v-if="item.hint"
                     class="item_hint"
                     v-html="item.hint"></div>
                <div class="item_people">
                  <img v-for="items in item.userList"
                       :key="items.id"
                       :src="items.headImg"
                       alt="">
                  <span v-if="item.userList">等{{item.userList.length}}人推荐过</span>
                </div>
              </div>
            </li>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, NavBar, Sticky } from 'vant'
export default {
  name: 'commissarRecom',
  components: {
    [Toast.name]: Toast,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [Sticky.name]: Sticky,
    [NavBar.name]: NavBar
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const router = useRouter()
    const data = reactive({
      title: '委员荐书',
      value: '',
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      switchs: { value: '', data: [{ label: '所有书籍', value: '' }] },
      addCategory: false, // 添加分类是否
      optionItem: null,
      editIndex: 0,
      isEdit: false,
      addTypeNameShow: false,
      addTypeName: '',
      type: 0
    })
    onMounted(() => {
      getListType(0)
    })
    const getListType = (_type) => {
      data.type = _type
      var type1 = document.getElementById('typeone')
      var type2 = document.getElementById('typetwo')
      if (_type === 0) {
        type1.style.color = '#007AFF'
        type2.style.color = '#BABEBF'
      } else {
        type1.style.color = '#BABEBF'
        type2.style.color = '#007AFF'
      }
      onRefresh()
    }
    // 推荐列表
    const getList = async () => {
      var datas = {
        isRecommend: 1, // 是否委员荐书。0：否；1：是
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        isPrivacy: 0,
        orderByType: data.type// 排序类型。0：最新；1：最热
      }
      var res = await $api.bookAcademy.getCommissarRecommendlList(datas)
      var { data: list } = res
      data.dataList = data.dataList.concat(list)
      console.log(data.dataList)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.bookId } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, getList, getListType, openBookDetails, onRefresh, onLoad }
  }
}
</script>
<style lang="less" scoped>
@import "./commissarRecom.less";
</style>
