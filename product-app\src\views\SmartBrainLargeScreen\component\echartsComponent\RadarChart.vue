<template>
  <div :id="chartId" class="chart"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'RadarChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    id: {
      type: String,
      default: () => ''
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    // 初始化图表
    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        // 获取最大值用于归一化
        const maxValue = Math.max(...props.chartData.map(item => item.value))
        const option = {
          radar: {
            triggerEvent: false,
            indicator: props.chartData.map(item => ({
              name: `{name|${item.name}}\n{value|${item.value}人}`,
              max: maxValue
            })),
            radius: '75%',
            center: ['50%', '58%'],
            splitNumber: 4,
            axisName: {
              color: '#666',
              fontSize: 12,
              padding: [5, 0],
              lineHeight: 16,
              rich: {
                name: {
                  fontSize: 12,
                  color: '#999'
                },
                value: {
                  fontSize: 15,
                  color: '#666'
                }
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(215, 224, 231, 1)'
              }
            },
            splitArea: {
              show: true
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(215, 224, 231, 1)'
              }
            }
          },
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: props.chartData.map(item => item.value),
                  name: '学历分布',
                  itemStyle: {
                    color: '#4A90E2'
                  },
                  areaStyle: {
                    color: 'rgba(82,148,227,0.4)'
                  },
                  lineStyle: {
                    color: '#4A90E2',
                    width: 2
                  },
                  symbol: 'circle',
                  symbolSize: 7
                }
              ]
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 监听数据变化
    watch(() => props.chartData, () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
