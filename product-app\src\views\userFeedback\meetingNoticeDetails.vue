<template>
  <div class="meetingNoticeDetails">
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item, index) in 1"
                    :key="index"
                    title
                    :row="10"></van-skeleton>
    </div>
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item, index) in 1"
                    :key="index"
                    title
                    :row="4"></van-skeleton>
    </div>
    <van-pull-refresh v-else
                      v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text=""
                offset="52"
                @load="onLoad">
        <div class="message_Details">
          <div class="message_Details_title">{{ details.noticeTitle }}</div>
          <div class="message_Details_time"><span>{{ details.publishDate }}</span> <span>{{details.org}}</span></div>
          <div class="message_Details_con"
               v-html="details.content"></div>
        </div>
        <div class="attachment_box"
             v-if="attachmentList.length != 0">
          <div class="attachment_title">附件</div>
          <div class="attachment_item"
               v-for="item in attachmentList"
               :key="item.id"
               @click="download(item)">
            {{ item.fileName }}
          </div>
        </div>
        <!-- <iframe src="" frameborder="0"></iframe> -->

      </van-list>
    </van-pull-refresh>
    <iframe style="width:100%;height:100vh;"
            name="preview"
            v-if="viewUrl"
            :url="viewUrl"
            useWKWebView
            allowEdit
            scaleEnabled></iframe>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'meetingNoticeDetails',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      id: route.query.id || '',
      user: JSON.parse(sessionStorage.getItem('user')) || JSON.parse(localStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      viewUrl: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      details: {},
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      attachmentList: []
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {
    })

    const onRefresh = () => {
      data.details = {}
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getDetails()
    }
    const onLoad = () => {
      // data.pageNo = data.pageNo + 1
      // getNoticeList()
    }
    const getDates = (date) => {
      // 获取当前时间
      var currentDate = new Date()
      // 计算3天前的时间
      var threeDaysAgo = new Date()
      threeDaysAgo.setDate(currentDate.getDate() - 3)
      // 将传入的日期字符串转换为日期对象
      var inputDate = new Date(date)
      // 判断传入的日期是否在3天前和当前时间之间
      if (inputDate >= threeDaysAgo && inputDate <= currentDate) {
        return true
      } else {
        return false
      }
    }
    const getDetails = async (page = 3) => {
      const res = await $api.notice.noticeInfo(data.id)
      data.details = res.data
      data.details.publishDate = dayjs(data.details.publishDate).format('YYYY-MM-DD HH:mm ')
      data.attachmentList = res.data.attachmentList.length ? res.data.attachmentList : []
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      data.finished = true
    }
    const onClickLeft = () => history.back()
    const download = async (item) => {
      var param = {
        url: item.filePath,
        name: item.fileName
      }
      router.push({ name: 'superFile', query: param })
    }
    return { ...toRefs(data), download, onClickLeft, getDates, onRefresh, onLoad, $general, confirm }
  }
}
</script>
<style lang="less" scoped>
.meetingNoticeDetails {
  min-height: 900px;
  background: url("../../../assets/img/jining/ztbg.png");
  background-size: 100%;
  min-height: 100vh;

  .message_Details {
    width: 96%;
    margin: 10px auto;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px;

    .message_Details_title {
      width: 100%;
      font-size: 18px;
      font-weight: 700;
      line-height: 22px;
    }

    .message_Details_time {
      font-size: 14px;
      color: #929498;
      margin: 10px 0;
      display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .message_Details_con {
      margin: 10px 0;

      >p {
        font-size: 16px;
      }
    }
  }

  .attachment_box {
    width: 96%;
    margin: 10px auto;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px;

    .attachment_title {
      margin: 0px 0 10px 0;
      font-size: 16px;
    }

    .attachment_item {
      width: 100%;
      background: #f8f8f8;
      padding: 10px;
      font-size: 14px;
      margin: 10px 0;
      border-radius: 5px;
    }
  }

  .tagbs {
    font-size: 12px;
    color: #fff;
    background-color: rgba(255, 58, 58, 0.939);
    padding: 2px 8px;
    border-radius: 10px;
    margin-right: 5px;
    transform: scale(0.8);
  }

  .tagb {
    font-size: 12px;
    color: #fff;
    border-radius: 10px;
    background-color: #588bf9;
    padding: 2px 8px;
    margin-right: 5px;
    transform: scale(0.8);
  }

  .notText {
    padding: 0;

    .van-skeleton {
      overflow: hidden;
      background: #fff;
      margin: 0 10px 20px;
      padding: 10px;
      border-radius: 10px;
    }
  }

  .van-pull-refresh {
    // background: #fff;
    min-height: 900px;
  }
}
</style>
