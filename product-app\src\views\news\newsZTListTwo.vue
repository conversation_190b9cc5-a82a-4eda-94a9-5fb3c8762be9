<template>
  <div class="newsZTList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <!-- <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" /> -->
      </div>
    </van-sticky>
    <div v-if="themeImg.url">
      <img style="width:100%;"
           fit="cover"
           :src="themeImg.url" />
    </div>
    <!--图标栏目-->
    <div v-if="menuList.length > 0"
         class="menu">
      <div class="flex_box T-flex-flow-row-wrap">
        <div v-for="(item,index) in menuList"
             :key="index"
             class="menu_item T-flexbox-vertical flex_align_center flex_justify_content"
             @click="itemClick(item)">
          <img :src="item.url"
               class="iconImg" />
          <p v-html="item.label"
             class="p1"></p>
          <p v-if="item.pointNumber > 0"
             class="flex_box flex_align_center flex_justify_content text_one"
             :class="item.pointType == 'big'?'footer_item_hot_big':'footer_item_hot'"
             :style="item.pointType == 'big'?'font-size:12px;'+'width:20px;height:20px;':'width:12px;height:12px;'"
             v-html="item.pointType == 'big'?(item.pointNumber>99?'99+':item.pointNumber):''"></p>
        </div>
      </div>
    </div>
    <!--正式列表-->
    <ul class="vue_newslist_box">
      <!--是一级专题页面-->
      <template v-if="!isList">
        <template>
          <div v-for="(item,index) in listData"
               :key="index">
            <div class="specialsubject_hint flex_box flex_align_center">
              <img class="specialsubject_hint_img"
                   src="../../assets/img/icon_specialsubject_hint.png" />
              <div class="specialsubject_hint_name flex_placeholder text_one2">
                {{item.name}}</div>
              <div v-if="item.hasMore"
                   @click="itemClick(item)"
                   class="flex_box flex_align_center">
                <div class="viewMore">查看全部</div>
                <van-icon :size="((appFontSize-5)*0.01)+'rem'"
                          color="#B6B6B6"
                          name="arrow"></van-icon>
              </div>
            </div>
            <!--列表中没数据-->
            <template v-if="item.data == 0">
              <div>暂无数据</div>
            </template>
          </div>
        </template>
      </template>
      <template v-else-if="columnType == '置顶图'||columnType == '左侧图'||columnType == '大张图'||columnType == '无图'">
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
        <van-pull-refresh v-model="refreshing"
                          style="min-height: 80vh;"
                          success-text="刷新成功"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad"
                    :immediate-check="false">
            <div v-if="columnType == '置顶图'"
                 class="flex_box T-flex-flow-row-wrap ToppingDiagram">
              <div v-for="(nItem,nIndex) in listData"
                   :key="nIndex"
                   class="specialsubject_wenshi_video"
                   @click="openDetails(nItem)">
                <div class="specialsubject_wenshi_video_img">
                  <div class="specialsubject_wenshi_video_play flex_box flex_align_center flex_justify_content">
                    <van-icon :size="((appFontSize+20)*0.01)+'rem'"
                              color="#FFF"
                              name="http://qdzhzx.qingdao.gov.cn/lzt/flowAttachment/tempRepository/2023/09/13/645123811506651136.png"></van-icon>
                  </div>
                  <img style="width: 100%;height: 100%;object-fit: cover;"
                       :src="nItem.url" />
                </div>
                <div class="text_two specialsubject_wenshi_video_title_box">
                  {{nItem.title}}</div>
                <div class="flex_box flex_align_center specialsubject_wenshi_video_timebox">
                  <div class="flex_placeholder"></div>
                  <div class="specialsubject_wenshi_video_title">
                    {{dayjs(nItem.time).format('YYYY-MM-DD')}}</div>
                </div>
              </div>
            </div>
            <div v-else-if="columnType == '左侧图'"
                 v-for="(nItem,nIndex) in listData"
                 :key="nIndex"
                 class="click leftHandView"
                 @click="openDetails(nItem)">
              <div class="flex_box van-hairline--bottom"
                   style="padding: 12px 0;">
                <img v-if="nItem.url"
                     class="leftHandView_img"
                     :src="nItem.url" />
                <div class="flex_placeholder">
                  <div class="text_two leftHandView_title">
                    {{nItem.title}}</div>
                  <div class="flex_box flex_align_center leftHandView_time_box">
                    <div class="flex_placeholder"></div>
                    <div class="leftHandView_time">
                      {{dayjs(nItem.time).format('YYYY-MM-DD')}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="columnType == '无图'"
                 class="flex_box T-flex-flow-row-wrap noImage">
              <div v-for="(nItem,nIndex) in listData"
                   :key="nIndex"
                   class="noImage_item_box"
                   @click="openDetails(nItem)">
                <div class="noImage_item_img_box">
                  <img class="noImage_item_img"
                       :src="nItem.url" />
                </div>
                <div class="text_two noImage_item_title">
                  {{nItem.title}}</div>
              </div>
            </div>
            <div v-else-if="columnType == '大张图'"
                 class="LargeImage">
              <van-swipe class="LargeImage_warp"
                         :autoplay="6000"
                         :show-indicators="true">
                <van-swipe-item v-for="(nItem,nIndex) in listData"
                                :key="nIndex"
                                @click="openDetails(nItem);">
                  <img class="LargeImage_img"
                       :src="nItem.url" />
                </van-swipe-item>
              </van-swipe>
            </div>
          </van-list>
        </van-pull-refresh>
      </template>
      <template v-else>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
        <van-pull-refresh v-model="refreshing"
                          style="min-height: 80vh;"
                          success-text="刷新成功"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad"
                    :immediate-check="false">
            <div v-if="columnType == 1"
                 class="flex_box T-flex-flow-row-wrap">
              <div v-for="(nItem,nIndex) in listData"
                   :key="nIndex"
                   class="specialsubject_item_video"
                   @click="openDetails(nItem)">
                <div class="specialsubject_item_video_img">
                  <div class="specialsubject_item_video_play flex_box flex_align_center flex_justify_content">
                    <van-icon :size="((appFontSize+20)*0.03)+'rem'"
                              color="#FFF"
                              name="http://qdzhzx.qingdao.gov.cn/lzt/flowAttachment/tempRepository/2023/09/13/645123811506651136.png">
                    </van-icon>
                  </div>
                  <img style="width: 100%;height: 100%;object-fit: cover;"
                       :src="nItem.url" />
                </div>
                <div class="text_two title_box"
                     v-html="nItem.title"></div>
                <div class="flex_box flex_align_center flex_time_box">
                  <div class="flex_placeholder"></div>
                  <div class="time_text">
                    {{dayjs(nItem.time).format('YYYY-MM-DD')}}</div>
                </div>
              </div>
            </div>
            <div v-else-if="columnType == 2"
                 class="flex_box T-flex-flow-row-wrap specialsubject_item_LongChart">
              <div v-for="(nItem,nIndex) in listData"
                   :key="nIndex"
                   class="specialsubject_item_long"
                   @click="openDetails(nItem)">
                <img style="width: 100%;height: 100%;object-fit: cover;"
                     :src="nItem.url" />
              </div>
            </div>
            <div v-else-if="columnType == 3"
                 v-for="(nItem,nIndex) in listData"
                 :key="nIndex"
                 class="specialsubject_item_tabulation"
                 @click="openDetails(nItem)">
              <div class="flex_box van-hairline--bottom specialsubject_item_padding">
                <img v-if="nItem.url"
                     :src="nItem.url"
                     class="specialsubject_item_image" />
                <div class="flex_placeholder">
                  <div class="text_two specialsubject_item_title"
                       v-html="nItem.title"></div>
                  <div class="flex_box flex_align_center specialsubject_item_time">
                    <div class="flex_placeholder"></div>
                    <div class="specialsubject_item_timeText">
                      {{dayjs(nItem.time).format('YYYY-MM-DD')}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="columnType == 4"
                 class="specialTopic_bigImg_box">
              <van-swipe class="specialTopic_bigImg_warp"
                         :autoplay="6000"
                         :show-indicators="true">
                <van-swipe-item v-for="(nItem,nIndex) in listData"
                                :key="nIndex"
                                @click="openDetails(nItem);">
                  <img class="specialTopic_bigImg_img"
                       :src="nItem.url" />
                </van-swipe-item>
              </van-swipe>
            </div>
            <div v-else-if="columnType == 5"
                 class="flex_box T-flex-flow-row-wrap">
              <div v-for="(nItem,nIndex) in listData"
                   :key="nIndex"
                   class="specialsubject_item_small"
                   @click="openDetails(nItem)">
                <div class="specialsubject_item_small_img">
                  <img style="width: 100%;height: 100%;object-fit: cover;"
                       :src="nItem.url" />
                </div>
                <div class="text_two specialsubject_item_small_title"
                     v-html="nItem.title"></div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </template>
    </ul>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'newsZTList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: '#9e7b4b',
      isShowHead: $isShowHead,
      title: route.query.title || '', // 标题
      id: route.query.id, // 数据id
      columnId: route.query.columnId || '', // 栏目id
      columnType: route.query.columnType, // 栏目类型
      isClickMore: route.query.isClickMore, // 是否点击更多进到此页面的
      isHistory: route.query.isHistory, // 是否是文史馆模块
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      listData: [],
      isList: false,
      appFontSize: $general.data.appFontSize,
      themeImg: { url: '' },
      menuList: [],
      HistoryList: [] // 数字文史列表
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      if (route.query.columnId) {
        data.isList = true
      } else {
        data.isList = false
      }
      browseSave()
      if (!data.isList) {
        getSpecialsubjectinfo()
      }
      onRefresh()
    })
    // 浏览
    const browseSave = async () => {
      const res = await $api.notice.browseSave({
        keyId: data.id,
        type: 5
      })
      console.log(res)
    }
    // 获取详情
    const getSpecialsubjectinfo = async () => {
      const { data: info } = await $api.news.getSpecialsubjectinfo({
        id: data.id
      })
      data.themeImg.url = (info.themeImg || info.coverImg || {}).fullUrl || ''
    }
    // 搜索
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
    }
    // 刷新
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      data.refreshing = false
      getList()
    }
    // 下拉加载
    const onLoad = () => {
      data.pageNo = data.pageNo += 1
      getList()
    }
    // 列表请求
    const getList = async () => {
      var res = await $api.news.getSpecialsubjectRelateinfoAndColumnListVos({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.id,
        title: data.keyword,
        isPublish: 1
      })
      if (data.isList) {
        res = await $api.news.getSpecialsubjectRelateinfoList({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          title: data.keyword,
          subjectId: data.id,
          columnId: data.columnId,
          // isAppShow: 1,
          // auditingFlag: 1,
          isPublish: 1
        })
      }
      if (data.isClickMore) {
        res = await $api.news.getlhinfostructuretList({
          module: 1,
          pageNo: 1,
          pageSize: 999,
          columnId: data.columnId,
          structureId: data.columnId,
          isPushApp: 1
        })
      }
      var { data: list, total } = res
      console.log('list==>', list)
      if (list && list.length !== 0) {
        if (!data.isList) {
          if (list.length === 1) {
            data.columnId = list[0].columnId
            data.columnType = list[0].type
            data.isList = true
            getList()
            return
          }
          list.forEach(_eItem => {
            var item = {}
            item.columnId = _eItem.columnId || '' // id
            item.name = _eItem.columnName || '' // 标题
            item.type = _eItem.type || '' // 类型  1视频  2大图 3列表
            item.url = _eItem.icon || '' // 是否有图标
            var iconopen = _eItem.iconopen || '' // 1图标展示
            if (iconopen === '1') {
              item.pointType = 'big' // 红点类型
              item.pointNumber = 0 // 数量
              data.menuList.push(item)
            } else {
              item.data = []
              var specialsubjectRelateinfoListVos = _eItem.specialsubjectRelateinfoListVos || []
              item.hasNum = item.type === 1 ? 4 : item.type === 3 ? 3 : item.type === 4 ? 0 : 6
              item.hasMore = item.hasNum !== 0 && specialsubjectRelateinfoListVos.length > item.hasNum
              showData(item.data, specialsubjectRelateinfoListVos, item.hasNum)
              data.listData.push(item)
            }
          })
        } else {
          if (data.isClickMore) {
            data.listData = []
            list.forEach(function (item) {
              item.time = item.createDate || '' // 时间
              item.url = item.leftImage // 图片
            })
            data.listData = data.listData.concat(list)
            data.showSkeleton = false
            data.loading = false
            data.refreshing = false
            data.finished = true
            return
          }
          showData(data.listData, list, 0, total)
        }
      } else {
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        if (data.listData.length >= total) {
          data.finished = true
        }
      }
    }
    // 列表拿数据
    const showData = (_list, _data, _num, _total) => {
      _data.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        if (_num !== 0 && _eIndex >= _num) {
          return
        }
        var item = {}
        item.id = _eItem.relateRecordId || ''
        item.title = (_eItem.title || '').replace(new RegExp(data.keyword, 'g'), data.keyword ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.keyword + '</span>' : '')
        item.url = _eItem.image || '' // 图片
        item.time = _eItem.createDate || '' // 时间
        item.relateType = _eItem.relateType || '' // 类型
        _list.push(item)
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        if (_list.length >= _total) {
          data.finished = true
        }
      })
    }
    // 返回
    const onClickLeft = () => history.back()
    //  点击图标
    const itemClick = (row) => {
      console.log(row)
      router.push({ name: 'newsZTListTwo', query: { id: data.id, columnId: row.columnId, title: row.name, type: row.type } })
    }
    // 进详情
    const openDetails = (row) => {
      console.log(row)
      // eslint-disable-next-line eqeqeq
      if (row.relateType == '5' || row.relateType == '53') {
        if (!row.externalLinks) {
          router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title } })
        } else {
          window.open(row.externalLinks)
        }
        // eslint-disable-next-line eqeqeq
      } else if (row.relateType == '27') {
        router.push({ name: 'noticeDetails', query: { id: row.id, title: row.title } })
      } else {
        row.relateType = '53'
        if (!row.externalLinks) {
          router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title } })
        } else {
          window.open(row.externalLinks)
        }
      }
    }
    return { ...toRefs(data), search, $general, onClickLeft, onRefresh, onLoad, openDetails, itemClick, dayjs }
  }
}
</script>

<style lang="less" scoped>
.newsZTList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
    .menu_item {
      position: relative;
      width: 25%;
      padding: 10px 0;
      .iconImg {
        width: 39px;
        height: 39px;
      }
      .p1 {
        font-size: 13px;
        margin-top: 9px;
        color: #3e3e3e;
        margin-top: 3px;
        text-align: center;
      }
      .footer_item_hot {
        position: absolute;
        top: 4px;
        right: 25%;
        width: 10px;
        height: 10px;
        background: #f92323;
        border-radius: 50%;
      }
      .footer_item_hot_big {
        position: absolute;
        top: 1px;
        right: 20%;
        width: 20px;
        height: 20px;
        background: #f92323;
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
      }
    }
  }
  .specialsubject_hint {
    padding: 20px 14px 20px 14px;
    position: relative;
    display: flex;
    justify-content: space-between;
    .specialsubject_hint_img {
      height: 19px;
      position: absolute;
      left: 14px;
      z-index: 0;
    }
    .specialsubject_hint_name {
      font-size: 17px;
      padding-left: 16px;
      position: relative;
    }
    .viewMore {
      color: #b6b6b6;
      margin-right: 4px;
      font-size: 14px;
    }
  }
  .specialsubject_item_video {
    border-radius: 10px;
    background: #fff;
    overflow: hidden;
    margin: 12px 0 0 12px;
    width: calc(50% - 17px);
    box-shadow: 0px 0px 10px -5px rgba(0, 0, 0, 0.4);
    .specialsubject_item_video_img {
      position: relative;
      width: 100%;
      height: 100px;
      .specialsubject_item_video_play {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
      }
    }
    .title_box {
      color: #333;
      margin: 7px;
      line-height: 1.42;
      min-height: 28px;
      font-size: 14px;
    }
    .flex_time_box {
      padding: 0 7px 7px 7px;
      .time_text {
        color: #999;
        font-family: Source Han Serif SC;
        font-size: 12px;
      }
    }
  }

  .specialsubject_item_LongChart {
    padding-left: 6px;

    .specialsubject_item_long {
      height: 160px;
      margin: 20px 0 0 18px;
      width: calc(33.33% - 28px);
    }
  }

  .specialsubject_item_tabulation {
    padding: 0 12px;
    .specialsubject_item_padding {
      padding: 12px 0;
      .specialsubject_item_image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        margin-right: 10px;
      }
      .specialsubject_item_title {
        color: #333;
        line-height: 1.42;
        min-height: 30px;
        font-size: 15px;
      }
      .specialsubject_item_time {
        margin-top: 12px;
        .specialsubject_item_timeText {
          color: #999;
          font-family: Source Han Serif SC;
          font-size: 12px;
        }
      }
    }
  }

  .specialTopic_bigImg_box {
    padding: 8px 12px;
    .specialTopic_bigImg_warp {
      width: 100%;
      height: auto;
      border-radius: 8px;
      overflow: hidden;
      .specialTopic_bigImg_img {
        width: 100%;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
      }
    }
  }
  .specialsubject_item_small {
    margin: 18px 0 0 10px;
    width: calc(33.33% - 14px);
    .specialsubject_item_small_img {
      height: 136px;
      width: 100%;
    }
    .specialsubject_item_small_title {
      color: #333;
      line-height: 1.42;
      text-align: center;
      margin-top: 10px;
      font-size: 12px;
    }
  }
  // 置顶图
  .ToppingDiagram {
    margin-top: -12px;
    .specialsubject_wenshi_video {
      border-radius: 8px;
      background: #fff;
      overflow: hidden;
      margin: 12px 0 0 12px;
      width: calc(50% - 18px);
      box-shadow: 0px 0px 8px -5px rgba(0, 0, 0, 0.4);
      .specialsubject_wenshi_video_img {
        position: relative;
        width: 100%;
        height: 100px;
        .specialsubject_wenshi_video_play {
          position: absolute;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.2);
        }
      }
    }
    .specialsubject_wenshi_video_title {
      color: #333;
      margin: 8px;
      line-height: 1.42;
      min-height: 28px;
      font-size: 14px;
    }
    .specialsubject_wenshi_video_title_box {
      padding: 0 8px 8px 8px;
      .specialsubject_wenshi_video_title {
        color: #999;
        font-family: Source Han Serif SC;
        font-size: 12px;
      }
    }
  }
  // 左侧图
  .leftHandView {
    padding: 0 12px;
    .leftHandView_img {
      width: 90px;
      height: 60px;
      object-fit: cover;
      margin-right: 10px;
    }
    .leftHandView_title {
      color: #333;
      line-height: 1.42;
      min-height: 30px;
      font-size: 15px;
    }
    .leftHandView_time_box {
      margin-top: 14px;
      .leftHandView_time {
        color: #999;
        font-family: Source Han Serif SC;
        font-size: 12px;
      }
    }
  }
  // 无图
  .noImage {
    margin-top: -16px;
    .noImage_item_box {
      .noImage_item_img_box {
        height: 130px;
        width: 100%;
        .noImage_item_img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .noImage_item_title {
        color: #333;
        line-height: 1.42;
        text-align: center;
        margin-top: 10px;
        font-size: 12px;
      }
    }
  }
  // 大张图
  .LargeImage {
    margin-top: -16px;
    padding: 8px 12px;
    .LargeImage_warp {
      width: 100%;
      height: auto;
      border-radius: 10px;
      overflow: hidden;
      .LargeImage_img {
        width: 100%;
        height: 96px;
        object-fit: cover;
        border-radius: 10px;
      }
    }
  }
}
</style>
