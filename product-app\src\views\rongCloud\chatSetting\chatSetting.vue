<template>
  <div class="chatSetting">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">

      </van-nav-bar>
    </van-sticky>
    <div style="height: 1px;"></div>
    <!--顶上的头像-->
    <div class="chat_item_box chat_info_box flex_box flex_align_center">
      <div class="chat_img">
        <img @click="previewImg(headerImg)"
             :src="headerImg"
             :style="'weight:43px;height:43px;'"
             alt=""
             srcset="">
      </div>
      <div class="flex_placeholder">
        <div class="chat_setting_name"
             :style="'font-size:15px;'"
             v-html="userName"></div>
        <div class="chat_setting_position text_two"
             :style="'font-size:13px;'"
             v-html="position"></div>
      </div>
    </div>

    <van-cell v-if="groupBook.has && isGroupAdmin"
              @click="openSay()"
              class="chat_item_box"
              style="padding: 15px 16px;">
      <template #right-icon>
        <div class="flex_box flex_align_center flex_placeholder">
          <div class="text_one"
               :style="'font-size:16px;'+'margin-right:10px;'"
               v-html="'群公告'"></div>
          <div class="flex_placeholder flex_box flex_align_center">
            <div class="flex_placeholder text_one2"
                 :style="'font-size:16px;'+'text-align: right;'">{{groupSay}}</div>
            <van-icon :size="16"
                      :color="'#A5A5A5'"
                      name="arrow"></van-icon>
          </div>
        </div>
      </template>
    </van-cell>
    <!-- <van-cell @click="openSearch()"
              class="chat_item_box"
              style="padding: 15px 16px;">
      <div class="text_one"
           v-html="'查找聊天记录'"></div>
      <template #right-icon>
        <van-icon :size="16"
                  :color="'#A5A5A5'"
                  name="arrow"></van-icon>
      </template>
    </van-cell> -->
    <!--群成员列表-->
    <div v-if="conversationType == 3"
         class="chat_item_box group_list_box">
      <!-- <div :style="'font-size:15px;'"
           class="group_search_box flex_box van-hairline--bottom">
        <van-search class="flex_placeholder"
                    v-model="members.search"
                    placeholder="搜索"></van-search>
      </div> -->
      <van-grid clickable
                :column-num="5">
        <!--有搜索时 查询所有 长度小于预定值时查询所有-->
        <template v-for="(item,index) in members.data"
                  :key="index">
          <van-grid-item v-if="index < members.moreNum"
                         @click="openUser(item)">
            <div>

              <img :style="'weight:42px;height:42px;'"
                   :src="item.url"
                   alt=""
                   srcset="">
              <div :style="'font-size:13px;'+'color:#666'"
                   class="text_one">{{item.name}}</div>
              <div v-if="item.isGroupOwner"
                   :style="'font-size:12px;'+'color:'+appTheme"
                   class="group_admin">群主</div>
            </div>
          </van-grid-item>
        </template>
        <van-grid-item @click="addGroupMember()">
          <template v-slot:icon>
            <div :style="'width:42px;height:42px;'"
                 class="add_box flex_box flex_align_center flex_justify_content">
              <van-icon :size="16"
                        color="#E1E1E1"
                        name="plus"></van-icon>
            </div>
            <div :style="'font-size:13px;'+'color:#666'"
                 class="text_one2">&nbsp;</div>
          </template>
        </van-grid-item>
      </van-grid>
      <!--展开收起大小大于预设 -->
      <!-- <div v-if="members.data.length > members.moreNum" -->
      <div @click="openGroupMem()"
           :style="'font-size:13px;'+'color:#999;padding:8px 0 14px 0;text-align: center;'">查看全部群成员({{members.data.length}})</div>
    </div>
    <!--群书籍列表-->
    <div v-if="groupBook.has"
         class="nItem_box">
      <div class="nItem_title_box flex_box flex_align_center">
        <div :style="'font-size:14px;'+'color:#101010;'"
             class="flex_placeholder text_one2">{{groupBook.name}}</div>
        <div v-if="groupBook.allData.length != 0"
             @click="addGroupBook(1)"
             class="flex_box flex_align_center">
          <div :style="'font-size:12px;color:#A5A5A5;margin-right:5px;'"
               class="flex_placeholder text_one2">查看所有{{groupBook.allData.length}}本书籍</div>
          <van-icon :color="'#A5A5A5'"
                    :size="13"
                    name="arrow"></van-icon>
        </div>
      </div>
      <div class="flex_box nItem_title_box">
        <div v-if="isGroupAdmin"
             class="itemSex_item click"
             @click="addGroupBook(2)">
          <div :style="'width:60px;height:80px;margin:auto;border-radius: 2px;'"
               class="add_box flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      color="#E1E1E1"
                      name="plus"></van-icon>
          </div>
          <div class="itemSex_name text_one"
               :style="'font-size:12px;'"
               v-html="'添加书籍'"></div>
        </div>
        <div v-for="(nItem,nIndex) in groupBook.data"
             :key="nIndex"
             class="itemSex_item click"
             @click="openBookDetails(nItem)">
          <img :style="'width:60px;height:80px;'+'object-fit:contain;margin:auto;'"
               :src="nItem.url" />
          <div class="itemSex_name text_one"
               :style="'font-size:12px;'"
               v-html="nItem.name"></div>
        </div>
      </div>
    </div>

    <!--加入黑名单即屏蔽消息单聊中才有 -->
    <!-- <van-cell v-if="conversationType == 1"
              class="chat_item_box">
      <div class="text_one"
           v-html="'加入黑名单'"></div>
      <template #right-icon>
        <van-switch :active-color="appTheme"
                    @change="setToBlack"
                    v-model="toBlack"
                    :size="24"></van-switch>
      </template>
    </van-cell> -->
    <!--消息免打扰 -->
    <!-- <van-cell class=""
              :class="conversationType != 1?'chat_item_box':''">
      <div class="text_one"
           v-html="'消息免打扰'"></div>
      <template #right-icon>
        <van-switch :active-color="appTheme"
                    @change="setNotificationStatus"
                    v-model="notificationStatus"
                    :size="24"></van-switch>
      </template>
    </van-cell> -->
    <!--添加新成员 -->
    <!-- <van-cell @click="addGroupMember()"
              v-if="conversationType == 3"
              class="chat_item_box">
      <div class="flex_box flex_align_center">
        <div class="flex_placeholder"
             v-html="'添加新成员'"></div>
        <template right-icon>
          <van-icon :color="'#aaa'"
                    :size="18"
                    :name="'add-o'"></van-icon>
        </template>
      </div>
    </van-cell> -->
    <!--清空聊天记录-->
    <div class="chat_item_box">
      <div @click="clearMessages()"
           :style="'font-size:15px;'"
           class="chat_btn_box van-hairline--bottom">{{'清空聊天记录'}}</div>
    </div>
    <!--退群 解散群操作 -->
    <div v-if="conversationType == 3"
         @click="quitGroup()"
         :style="'font-size:15px;'"
         class="chat_item_box chat_btn_box van-hairline--bottom">{{isGroupAdmin?'解散并退出群聊':'退出群聊'}}</div>

  </div>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
  <van-popup v-model:show="showUser"
             round
             position="bottom"
             :style="{ height: '90%' }">
    <addressBook v-if="showUser"
                 ref="userComponts"
                 :data="selectUser"
                 :isSelectUser="true" />
    <footer class="footerBtn">
      <div class="footerBtnBox flex_box">
        <van-button @click="addGroup"
                    type="primary"
                    block>确定</van-button>
      </div>
    </footer>
  </van-popup>
</template>
<script>
import * as RongIMLib from '@rongcloud/imlib-next'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { NavBar, Sticky, CellGroup, Button, Field, Popup, Image as VanImage, Divider, ActionSheet, Grid, GridItem, Toast, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import addressBook from '../addressBook/addressBook'
export default {
  name: 'chatSetting',
  components: {
    addressBook,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Popup.name]: Popup,
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Divider.name]: Divider,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Field.name]: Field
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const $general = inject('$general')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      // eslint-disable-next-line eqeqeq
      title: route.query.conversationType == 3 ? '群聊信息' : '聊天设置',
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      conversationType: Number(route.query.conversationType),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      userName: '',
      headerImg: '',
      position: '',
      toBlack: false, // 是否在黑名单中
      notificationStatus: false, // 是否免打扰
      isGroupAdmin: false, // 是否群组管理员
      members: { more: false, show: false, moreNum: 4, search: '', data: [] }, // 是否有查看更多 多少触发查看 群成员管理
      groupBook: { has: false, name: '群书籍', allData: [], data: [] },
      groupSay: '',
      show: false,
      nowUserId: '',
      description: '',
      actions: [
        { name: '退出群组', color: '#ee0a24' }
      ],
      showUser: false,
      // 选择的用户集合 id name url notDel[是否可以删除选择]
      userComponts: null,
      selectUser: [
        // { id: 1, name: '超级管理员', url: '', notDel: true }
      ],
      ifReturn: false, // 是否返回人员
      ifReturnNull: false // 是否能选择返回空 默认不能
    })
    onMounted(() => {
      // eslint-disable-next-line eqeqeq
      if (data.conversationType == 3) {
        getChatInfo()
        getGroupList()
      } else {
        getUserInfo()
      }
      // getConversationNotification()
    })
    // const getConversationNotification = () => {
    //   RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
    //   RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
    //     console.log(res)
    //     const conversationType = data.conversationType
    //     const targetId = data.id
    //     RongIMLib.getConversationNotificationLevel({
    //       conversationType,
    //       targetId
    //     }).then(({ code, data }) => {
    //       console.log(code, data)
    //     })
    //   })
    // }
    const setConversationNotification = () => {
      const conversationType = data.conversationType
      const targetId = data.id
      const notificationLevel = data.notificationStatus ? 1 : -1
      //     - 1: 全部消息通知（接收全部消息通知 --显示指定关闭免打扰功能）
      //       0: 未设置（向上查询群或者APP级别设置）//存量数据中0表示未设置
      //       1: 群聊超级群仅 @消息通知（现在通知）单聊代表全部消息通知
      //       2: @指定用户通知
      //       3: @群用户组通知，通知用户组暂未实现，暂不暴露出去
      //       4: @群全员通知
      //       5: 消息通知被屏蔽，即不接收消息通知
      // 注意单聊的 level 只有 - 1、0、1

      RongIMLib.setConversationNotificationLevel({
        conversationType,
        targetId
      }, notificationLevel).then(({ code }) => {
        // 设置免打扰状态成功
        console.log(code, data)
      })
    }
    // 清空消息 移除会话
    const clearMessages = () => {
      const conversation = {
        conversationType: data.conversationType,
        targetId: data.id
      }
      var nowTime = new Date().getTime()
      console.log(nowTime + '清空消息', conversation)
      RongIMLib.clearHistoryMessages(conversation, nowTime).then(res => {
        if (res.code === 0) {
          console.log('清除成功')
        } else {
          console.log(res.code, res.msg)
        }
      }).catch(error => {
        console.log(error)
      })
      // RongIMLib.clearMessages(conversation).then(res => {
      //   console.log(res)
      //   if (res.code === 0) {
      //     console.log('成功清理此会话下的消息')
      //   }
      // }).catch(error => {
      //   console.log(error)
      // })
    }
    // 设置 屏蔽 和免打扰
    const setNotificationStatus = () => {
      setConversationNotification()
    }
    const setToBlack = () => {
      // web端暂时没有找到api
    }
    const getGroupList = async () => {
      var { data: gruopInfo } = await $api.rongCloud.getGroupMember({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        whetherToDiscuss: 1
      })
      data.members.data = []
      const newData = []
      gruopInfo.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        var item = {}; var itemData = _eItem
        item.id = itemData.id || ''
        item.name = itemData.userName || ''
        item.url = itemData.headImg
        item.isGroupOwner = itemData.isGroupOwner
        // 管理员到第一个去
        if (item.isGroupOwner) { newData.unshift(item) } else { newData.push(item) }
      })
      data.members.data = data.members.data.concat(newData)
    }
    const getChatInfo = async () => {
      var { data: gruopInfo } = await $api.rongCloud.getGroupInfo({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })
      data.userName = gruopInfo.groupName
      data.headerImg = gruopInfo.groupImgUrl
      // eslint-disable-next-line eqeqeq
      data.groupBook.has = gruopInfo.groupType == '2'
      // eslint-disable-next-line eqeqeq
      data.isGroupAdmin = gruopInfo.groupOwner == data.user.id
      data.groupSay = gruopInfo.openSay || ''
      if (data.groupBook.has) {
        getGroupBook()
      }
      console.log(gruopInfo)
    }
    const openSay = () => {
      router.push({ name: 'groupSayEdit', query: { id: data.id } })
    }
    const openGroupMem = row => {
      router.push({ name: 'memberList', query: { id: data.id, name: data.userName, isGroupAdmin: data.isGroupAdmin } })
    }
    const getGroupBook = async () => {
      var nowItem = data.groupBook
      var { data: list } = await $api.bookAcademy.getBookList({
        pageNo: 1, pageSize: 999, talkGroupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })
      nowItem.data = []
      nowItem.allData = []
      list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        var item = {}
        item.id = _eItem.id || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.url = _eItem.coverImgUrl || ''// 书图
        nowItem.allData.push(item.id)
        if (_eIndex < (data.isGroupAdmin ? 3 : 4)) {
          nowItem.data.push(item)
        }
      })
    }
    const getUserInfo = async (_item) => {
      var { data: userInfo } = await $api.rongCloud.getUserInfo({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })
      data.userName = userInfo.userName
      data.headerImg = userInfo.fullImgUrl
      data.position = userInfo.position
      console.log(userInfo)
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const previewImg = (info) => {
      ImagePreview({
        images: [info],
        closeable: true
      })
    }
    const openUser = (_item) => {
      if (data.isGroupAdmin && _item.id !== data.user.id) {
        data.description = '确定要踢出群组吗？'
        data.actions = [
          { name: '踢出群组', color: '#ee0a24' },
          { name: '查看信息' }
        ]
        data.show = true
        data.nowUserId = _item.id
        console.log('踢成员退出群')
      } else {
        router.push({ name: 'personData', query: { id: _item.id } })
      }
    }
    const quitGroup = () => {
      if (data.isGroupAdmin) {
        console.log('管理员解散群')
        data.description = '确定要解散群组吗？'
        data.actions = [
          { name: '解散群组', color: '#ee0a24' }
        ]
      } else {
        data.description = '确定要退出群组吗？'
        data.actions = [
          { name: '退出群组', color: '#ee0a24' }
        ]
        console.log('成员退出群')
      }
      data.show = true
    }
    const onSelect = (item) => {
      switch (item.name) {
        case '解散群组':
          disbandGroup()
          break
        case '退出群组':
          data.nowUserId = ''
          outGroup()
          break
        case '踢出群组':
          outGroup()
          break
        case '查看信息':
          router.push({ name: 'personData', query: { id: data.nowUserId } })
          break
      }
    }
    const addGroupBook = (type) => {
      var item = {}
      if (type === 2) {
        item = { businessType: type, data: data.groupBook.allData, groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1], title: '添加群书籍' }
      } else {
        item = { businessType: type, groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1], isGroupAdmin: data.isGroupAdmin, title: '群书籍' }
      }
      router.push({ name: 'addGroupBook', query: item })
    }
    const outGroup = async (_item) => {
      var res = await $api.rongCloud.outGroup({
        groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        userId: data.nowUserId || data.user.id
      })
      if (res) {
        Toast(data.nowUserId ? '踢人成功！' : '退群成功！')
        if (!data.nowUserId) {
          deletMsg()
          setTimeout(() => {
            onClickLeft()
          }, 1000)
        } else {
          getGroupList()
        }
      }
    }
    const disbandGroup = async (_item) => {
      var res = await $api.rongCloud.disbandGroup({
        groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        userId: data.user.id
      })
      if (res) {
        Toast('群组解散成功')
        deletMsg()
        setTimeout(() => {
          onClickLeft()
        }, 1000)
      }
    }
    const deletMsg = () => {
      const conversationType = 3
      RongIMLib.removeConversation({
        conversationType,
        targetId: data.id
      }).then(res => {
        console.log('删除消息记录：', res)
      })
    }
    const addGroupMember = () => {
      var newData = []
      data.selectUser = []
      data.members.data.forEach(item => {
        const items = { id: item.id, name: item.name, url: item.url, notDel: true }
        newData.push(items)
      })
      data.selectUser = data.selectUser.concat(newData)
      data.showUser = true
    }

    const addGroup = async () => {
      const userIds = []
      data.userComponts.selectUser.forEach(element => {
        userIds.push(element.id)
      })
      const res = await $api.rongCloud.addGroupUser({
        groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        groupName: data.userName,
        userIds: userIds.join(',')
      })
      if (res.errcode === 200) {
        Toast('拉人成功')
        getGroupList()
        data.showUser = false
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, openBookDetails, setNotificationStatus, setToBlack, clearMessages, openSay, openGroupMem, openUser, previewImg, quitGroup, onSelect, addGroupBook, addGroupMember, addGroup }
  }
}
</script>
<style lang="less" scoped>
.chatSetting {
  width: 100%;
  .chat_item_box {
    margin-top: 10px;
  }
  .chat_info_box {
    padding: 15px;
    background: #fff;
  }
  .chat_img {
    margin-right: 10px;
  }
  .chat_setting_name {
    font-weight: bold;
    color: #222;
  }
  .chat_setting_position {
    color: #999;
    margin-top: 5px;
  }
  .chat_btn_box {
    padding: 14px 0;
    box-sizing: border-box;
    text-align: center;
    background: #fff;
    color: #ff0000;
  }
  .chat_btn_box:active {
    background: rgba(0, 0, 0, 0.1);
  }

  .add_box {
    border: 1px dashed #e1e1e1;
    border-radius: 10px;
  }

  .group_list_box {
    background: #fff;
  }
  .search-dropdown-menu {
    margin-right: 5px;
    padding: 10px 0;
  }
  .van-dropdown-menu.van-hairline--top-bottom::after {
    border-width: 0 0;
  }
  .van-grid {
    background: #fff;
  }
  .group_admin {
    position: absolute;
    right: -0px;
    top: -0px;
    font-weight: bold;
  }
  .van-popup {
    width: 100%;
    height: 100%;
  }
  #app .van-search {
    padding: 14px;
  }
  #app .van-search__content {
    border-radius: 16px;
    background-color: #f4f4f4;
  }
  .nItem_box {
    background: #fff;
    margin-top: 10px;
  }
  .nItem_title_box {
    padding: 8px 16px;
  }
  .itemSex_item {
    width: 25%;
    padding: 10px 0;
  }
  .itemSex_name {
    color: #5e646d;
    text-align: center;
    margin-top: 5px;
  }
}
.footerBtn {
  background: #fff;
  padding: 5px 0;
  position: fixed;
  bottom: 0;
  width: 100%;
  .footerBtnBox {
    width: calc(100% - 20px);
    margin-left: 10px;
    .van-button + .van-button {
      width: calc(100% - 20px);
      margin-left: 10px;
    }
  }
}
</style>
