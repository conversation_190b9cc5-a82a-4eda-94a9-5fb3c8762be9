@import './mainModule.less';

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
main,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;
}

ol,
ul,
li {
  list-style: none;
}

input,
button,
textarea {
  border: 0;
  margin: 0;
  padding: 0;
  background-color: rgba(0, 0, 0, 0);
  outline: none;
}

div {
  font-size: 16px;
  // color: #262626;
  box-sizing: border-box;
}

pre {
  white-space: pre-wrap;
  font-family: Microsoft YaHei;
}

// 所有浏览器隐藏滚动条样式
.scrollBar {
  /*IE下隐藏滚动条*/
  -ms-scroll-chaining: chained;
  -ms-overflow-style: none;
  -ms-content-zooming: zoom;
  -ms-scroll-rails: none;
  -ms-content-zoom-limit-min: 100%;
  -ms-content-zoom-limit-max: 500%;
  // -ms-scroll-snap-type: proximity;
  -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
  -ms-overflow-style: none;
  overflow: auto;
  /*火狐下隐藏滚动条*/
  scrollbar-width: none;

  /*谷歌下隐藏滚动条*/
  &::-webkit-scrollbar {
    display: none;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsisTwo {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.ellipsisThree {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

:root {
  --van-blue: #0d75ff;
}

/* vant字体相关 */
#app .van-cell {
  // padding: 10px 16px;
  line-height: normal;
  font-size: inherit;
  font-family: inherit;
  color: #222;
}

#app .van-cell .van-cell__title {
  font-size: inherit;
  font-family: inherit;
}

#app .van-cell .van-cell__title span {
  font-size: inherit;
  font-family: inherit;
}

#app .van-dropdown-menu {
  height: auto;
  font-size: inherit;
  font-family: inherit;
}

#app .van-dropdown-menu .van-dropdown-menu__title {
  line-height: 1.4;
  height: auto;
  font-size: inherit;
  font-family: inherit;
}

#app .van-dropdown-menu__title::after {
  // border: 0.03px solid;
  border: 3px solid;
  border-color: transparent transparent #323232 #323232;
}

#app .van-dropdown-menu__title--active::after {
  border-color: transparent transparent currentColor currentColor;
}

#app .search_warp .van-dropdown-menu__bar {
  height: 100%;
}

#app .van-dropdown-menu div {
  font-size: inherit;
  font-family: inherit;
}

#app .van-dropdown-item,
#app .van-dropdown-item div {
  font-size: inherit;
  font-family: inherit;
}

#app .van-swipe-cell__right {
  font-size: inherit;
  font-family: inherit;
}

#app .van-swipe-cell__right .van-button {
  height: 100%;
}

#app .van-tabs {
  height: auto;
  font-size: inherit;
  font-family: inherit;
}

#app .van-tabs__wrap {
  height: auto;
  font-size: inherit;
  font-family: inherit;
}

#app .van-hairline--top-bottom::after,
.van-hairline-unset--top-bottom::after {
  border-width: 0px 0;
}

#app .van-tabs__nav,
#app .van-tab,
#app .van-tab__text {
  font-size: inherit;
  font-family: inherit;
}

#app .van-tag {
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  border-radius: 2px;
  padding: 0 4px;
}

#app .van-tag--round {
  border-radius: 999px;
}

#app .van-tabs__line {
  bottom: 3px;
}

#app .van-tab {
  padding: 9px 7px 5px 9px;
  min-width: auto;
}

#app .van-button__content {
  font-size: inherit;
  font-family: inherit;
}

#app .van-button,
#app .van-button .van-button__text {
  font-size: inherit;
  font-family: inherit;
}

#app .van-button {
  height: auto;
  line-height: 1.2;
  padding: 10px 12px;
}

#app .setting_item .van-dropdown-item .van-popup {
  padding-top: 10px;
}

#app .setting_item .van-dropdown-item .van-button {
  border-radius: 0;
}

.van-skeleton+.van-skeleton {
  margin-top: 20px;
}

#app .van-uploader__preview {
  margin: 0 8px 8px 0;
  position: relative;
}

.select_img_state {
  position: absolute;
  bottom: 0;
  right: -4px;
}

#app .van-uploader__preview-image {
  width: 77px;
  height: 77px;
}

#app .van-cell__value {
  margin: auto;
}

#app .van-field__left-icon {
  font-size: inherit;
  font-family: inherit;
  margin-right: 5px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  box-align: center;
  -moz-box-align: center;
  -webkit-box-align: center;
}

#app .van-field__left-icon .van-icon {
  font-size: inherit;
}

#app .van-search {
  font-size: inherit;
  font-family: inherit;
  padding: 10px 12px;
}

#app .van-search__content {
  font-size: inherit;
  font-family: inherit;
  padding-left: 0;
  padding: 0 12px;
}

#app .van-cell__value,
#app .van-cell__value span,
#app .van-field__body,
#app .van-switch__node {
  font-size: inherit;
  font-family: inherit;
}

#app .van-tabs__nav--line {
  padding-bottom: 7px;
}

#app .van-uploader__upload {
  width: 80px;
  height: 80px;
  margin: 0 8px 8px 0;
  border-radius: 4px;
}

#app .van-uploader__preview-delete {
  font-size: 20px;
  top: -4px;
  right: -4px;
}

#app .van-dialog__header,
#app .van-dialog__content,
#app .van-dialog__footer {
  font-size: inherit;
  font-family: inherit;
}

#app .van-dialog {
  width: 300px;
}

body .van-toast {
  padding: 8px 12px;
}

#app .van-radio-group,
#app .van-radio,
#app .van-checkbox-group,
#app .van-checkbox {
  font-size: inherit;
  font-family: inherit;
}

#app .van-checkbox__icon {
  font-size: inherit;
  font-family: inherit;
}

#app .van-radio__label,
#app .van-checkbox__label {
  line-height: 1.4;
  margin-left: 8px;
  font-size: inherit;
  font-family: inherit;
}

body .van-popup__close-iconfont--top-right,
#app .van-calendar__popup .van-popup__close-icon {
  top: 15px;
  right: 15px;
}

body .van-popup {
  font-size: inherit;
  font-family: inherit;
}

body .van-popup__close-icon {
  font-size: inherit;
}

#app .van-count-down {
  line-height: inherit;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
}

#app .van-grid-item__content,
#app .van-grid-item__text {
  font-size: inherit;
  font-family: inherit;
  color: #434444;
  text-align: center;
}

#app .van-grid-item__content {
  padding: 15px 0px;
}

#app .van-grid-item__text {
  margin-top: 3px;
}

#app .van-grid-item__content::after {
  border-width: 0;
}

#app .van-share-sheet__cancel {
  font-size: inherit;
  font-family: inherit;
  line-height: 2.7;
}

#app .van-share-sheet__header,
#app .van-share-sheet__title {
  font-size: inherit;
  font-family: inherit;
}

#app .van-share-sheet__options,
#app .van-share-sheet__option {
  font-size: inherit;
  font-family: inherit;
}

#app .van-share-sheet__icon {
  width: 48px;
  height: 48px;
}

#app .van-share-sheet__name {
  font-family: inherit;
  font-size: 12px;
}

#app .van-dropdown-menu__bar {
  height: inherit;
  background: rgba(0, 0, 0, 0);
  box-shadow: none;
}

#app .van-dropdown-item .van-popup .van-cell .van-cell__value {
  -webkit-box-flex: none;
  -webkit-flex: none;
  flex: none;
  margin-left: 15px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  box-align: center;
  -moz-box-align: center;
  -webkit-box-align: center;
  -webkit-justify-content: center;
  justify-content: center;
  -moz-box-pack: center;
  -webkit--moz-box-pack: center;
  box-pack: center;
}

#app .van-dropdown-menu .van-ellipsis {
  white-space: normal;
  text-overflow: clip;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

#app .van-sidebar {
  font-size: inherit;
  font-family: inherit;
  width: 110px;
}

#app .van-sidebar-item {
  font-size: inherit;
  font-family: inherit;
  padding: 20px 12px;
}

#app .van-sidebar-item__text {
  font-size: inherit;
  font-family: inherit;
}

#app .van-sidebar-item--select::before {
  width: 4px;
  height: 16px;
}

#app .van-empty__image {
  width: 160px;
  height: 160px;
}

#app .custom-image .van-empty__image {
  width: 90px;
  height: 90px;
}

#app .van-empty {
  padding: 32px 0;
  font-size: inherit;
  font-family: inherit;
}

#app .van-empty__description {
  font-size: inherit;
  font-family: inherit;
}

#app .van-cell-group {
  font-size: inherit;
  font-family: inherit;
}

#app .van-divider {
  font-size: inherit;
  font-family: inherit;
}

#app .van-cell__left-icon,
#app .van-cell__right-icon {
  font-size: inherit;
  line-height: 1.5;
  color: #B2B2B2;
}

body .van-dialog {
  border-radius: 5px;
}

body .van-dialog__message {
  font-size: inherit;
  font-family: inherit;
}

#app .van-swipe-cell,
#app .van-swipe-cell__wrapper {
  font-size: inherit;
  font-family: inherit;
}

#app .van-sticky {
  font-size: inherit;
  font-family: inherit;
}

#app .van-number-keyboard,
#app .van-number-keyboard__header,
#app .van-number-keyboard__title,
#app .van-number-keyboard__close {
  font-size: inherit;
  font-family: inherit;
}

#app .van-picker,
#app .van-picker__toolbar,
#app .van-picker__cancel,
#app .van-picker__title,
#app .van-picker__confirm {
  font-size: inherit;
  font-family: inherit;
}

#app .van-picker__toolbar {
  min-height: 44px;
  padding: 10px 0;
}

#app .van-picker__title {
  line-height: inherit;
}

#app .van-picker__columns,
#app .van-picker-column,
#app .van-picker-column__wrapper,
#app .van-picker-column__item,
#app .van-picker-column__item .van-ellipsis {
  font-size: inherit;
  font-family: inherit;
}

#app .van-field__label {
  margin-right: 0.1px;
  color: #323233;
}

#app .van-calendar,
#app .van-calendar__header,
#app .van-calendar__body,
#app .van-calendar__footer,
#app .van-calendar__header-title {
  font-size: inherit;
  font-family: inherit;
}

#app .van-calendar__header-subtitle,
#app .van-calendar__month-title {
  font-size: 14px;
}

#app .van-calendar__weekday {
  font-size: 12px;
}

#app .van-calendar__day {
  font-size: 16px;
}

#app .van-calendar__bottom-info {
  font-size: 11px;
}

#app .van-sidebar-item {
  line-height: 1.4;
}

#app .van-tree-select__item {
  padding: 0 0.3px 0 15px;
  line-height: 2.5;
}

#app .van-tree-select__content,
#app .van-tree-select__item {
  font-size: inherit;
  font-family: inherit;
}

#app .van-tree-select__selected {
  font-size: inherit;
  right: 15px;
  top: 43%;
}

#app .van-progress,
#app .van-progress__portion,
#app .van-progress__pivot {
  font-size: inherit;
  font-family: inherit;
}

#app .van-circle__text {
  font-size: inherit;
  font-family: inherit;
}

body .inherit {
  font-size: inherit;
  font-family: inherit;
}

.notText {
  width: 100%;
  text-align: center;
  padding: 15px 0;
  background: rgba(0, 0, 0, 0);
}

.none {
  display: none;
}

.text_one {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text_one2 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text_two {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
}

/* 2019年1月12日 10:14:06*兼容写法**************************************************************/
/*定义flex布局    */
.flex_box {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

/*flex占位    */
.flex_placeholder {
  box-flex: 1;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  flex: 1;
  -webkit-flex: 1;
}

/*flex排列方向 上下    */
.flex_flex_direction_column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  flex-direction: column;
  -webkit-flex-direction: column;
}

/*flex上下居中    */
.flex_align_center {
  align-items: center;
  -webkit-align-items: center;
  box-align: center;
  -moz-box-align: center;
  -webkit-box-align: center;
}


/*flex上下居下    */
.flex_align_end {
  align-items: flex-end;
  -webkit-align-items: flex-end;
  box-align: flex-end;
  -moz-box-align: flex-end;
  -webkit-box-align: flex-end;
}

/*flex左右居中    */
.flex_justify_content {
  -webkit-justify-content: center;
  justify-content: center;
  -moz-box-pack: center;
  -webkit--moz-box-pack: center;
  box-pack: center;
}

/* 左右 居左 */
.flex_justify-content_start {
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -moz-box-pack: flex-start;
  -webkit--moz-box-pack: flex-start;
  box-pack: flex-start;
}

/* 左右 居右 */
.flex_justify-content_end {
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  -moz-box-pack: flex-end;
  -webkit--moz-box-pack: flex-end;
  box-pack: flex-end;
}

.T-iframe {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
}

/* 常用流布局  vertical垂直布局 horizontal水平 item占满    */
.T-flexbox-vertical {
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-flex-flow: column;
  flex-flow: column;
  height: 100%;
  -webkit-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.T-flexbox-horizontal {
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-flex-flow: row;
  flex-flow: row;
  width: 100%;
  -webkit-flex-direction: row;
  flex-direction: row;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.T-flex-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

/* 占满了换行  下面的应该是不换行 */
.T-flex-flow-row-wrap {
  flex-direction: row;
  flex-wrap: wrap;
  -webkit-flex-direction: row;
  -webkit-flex-wrap: wrap;
}

.T-flex-flow-row-nowrap {
  flex-direction: row;
  flex-wrap: nowrap;
  -webkit-flex-direction: row;
  -webkit-flex-wrap: nowrap;
}

/* 新样式 */
.item_body_warp {
  padding: 9px 14px;
  position: relative;
}

.item_body {
  background: #FFF;
  border-radius: 14px;
  padding: 15px;
  position: relative;
  box-shadow: 0px 0px 10px -6px rgba(0, 0, 0, 0.4);
}

.content_box {
  height: auto;
  overflow: hidden;
}

.content_box_more {
  height: 85px;
}

.more_operating {
  padding: 12px 0;
  background: #FFF;
  position: relative;
}

.more_operating_hide::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 0;
  width: 100%;
  height: 15px;
  background: rgba(255, 255, 255, 0.55);
  z-index: 2;
}

.arrowOpen {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.arrowClose {
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.category_box {
  min-width: 70%;
  background: #FFF;
  padding: 10px 30px;
  border-radius: 10px;
}

.category_item {
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}

.category_item+.category_item {
  margin-top: 10px;
}

.item_download {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.5);
}

.item_Sound {
  position: absolute;
  left: 5px;
  top: 5px;
  width: 28px !important;
  height: 28px !important;
}

/* 右下角按钮样式  具体参考demo_page */
.footer_btn_box {
  position: fixed;
  bottom: 30px;
  right: 20px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  z-index: 9;
}

.footer_btn_box .footer_btn {
  padding: 10px 21px;
  opacity: 0.8;
  box-sizing: border-box;
  color: #FFF;
  text-align: center;
  margin-bottom: 8px;
  border-radius: 5px;
  max-width: 180px;
}

.footer_btn_box .van-button-box {
  margin-bottom: 8px;
  font-size: inherit;
  font-family: inherit;
}

.footer_btn_box .van-button {
  opacity: 0.8;
  border-radius: 5px;
  width: 100%;
}

.footer_btn_box .back_top {
  z-index: 9;
  margin: 0 auto;
  margin-bottom: 8px;
  text-align: center;
}

.footer_btn_box .back_top .van-iconfont-upgrade {
  background: #FFF;
  border-radius: 50%;
  color: #333;
}

/*自定义搜索**************************************************************/
.search_box {
  padding: 12px 14px;
  box-sizing: border-box;
  background: #fff;
}


.search_box .search_warp {
  min-height: 34px;
  background: #f8f8f8;
  border-radius: 5px;
  border: 0px solid #D6DBDE;
}

.search-dropdown-menu {
  padding-right: 13px;
}

.search_warp form {
  width: 1px;
  padding-left: 0.0px;
  padding-right: 13px;
}

.search_warp input {
  width: 100%;
  margin: auto;
  min-height: 30px;
}

.search_warp .search_btn {
  padding: 0 7px;
}

.search_warp .search_btn .van-icon {
  font-weight: bold;
}

/*数据列表**************************************************************/
.vue_newslist_box {}

.vue_newslist_box .van-swipe-cell {
  background: #FFF;
}

#app .vue_newslist_item {
  padding: 20px 15px;
  position: relative;
}

.vue_newslist_img {
  width: 90px;
  height: 70px;
  background-size: cover;
  -webkit-background-size: cover;
  background-position: 50%;
  border-radius: 2px;
  flex-shrink: 0;
}

.vue_newslist_warp {
  padding-bottom: 2px;
}

.vue_newslist_warp+.vue_newslist_img,
.vue_newslist_img+.vue_newslist_warp {
  margin-left: 12px;
}

.vue_newslist_title {
  line-height: 1.47;
  color: #222;
  margin-bottom: 9px;
  font-weight: 500;
  min-height: 3em;
}

.vue_newslist_time,
.vue_newslist_source {
  color: #999;
}

.vue_newslist_time+.vue_newslist_source {
  margin-left: 20px;
}

.vue_newslist_play {}

.vue_newslist_play img {
  margin-right: 6px;
}

.vue_newslist_play div {
  font-weight: 500;
}

.vue_newslist_summary {
  color: #888;
  margin: 12px 0 3px 0;
}

.vue_newslist_boxImg {
  width: 100%;
  height: 170px;
  position: relative;
  margin: 7px 0;
}

.vue_newslist_boxImg .van-swipe {
  height: 100%;
}

.vue_newslist_itemImg {
  width: 100%;
  height: 100%;
  background-size: cover;
  -webkit-background-size: cover;
  background-position: 50%;
}

.vue_newslist_state {
  padding: 2px 0 0 10px;
}

/*数据列表第二种**************************************************************/
.vue_newslist_ul {
  padding: 10px 15px;
}

.vue_newslist2_box {
  box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.08);
  border-radius: 4px;
  overflow: hidden;
  width: calc(100% - 32px);
  margin-top: 10px;
  margin-left: 16px;
}

.vue_newslist2_box+.vue_newslist2_box {
  border-top: 0;
  margin-top: 10px;
}

body .vue_newslist2_item {
  padding: 0;
}

.vue_newslist2_status {
  position: relative;
  color: #222;
  padding: 10px10px 5px;
  font-weight: 600;
}

.vue_newslist2_status .angle {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

.vue_newslist2_status_text {
  padding: 10px10px 5px;
}

.vue_newslist2_img {
  width: 137px;
  min-height: 103px;
  background-size: cover;
  -webkit-background-size: cover;
  background-position: 50%;
  border-radius: 2px;
}

.vue_newslist2_warp {
  padding: 0 10px;
}

.vue_newslist2_con+.vue_newslist2_img,
.vue_newslist2_img+.vue_newslist2_con {
  margin-left: 12px;
}

.vue_newslist2_title {
  line-height: 1.47;
  color: #222;
  margin-bottom: 9px;
  font-weight: 600;
}

.vue_newslist_top span {
  line-height: 1.4;
  padding: 0 10px;
}

.vue_newslist2_addData {
  padding: 10px 15px 0;
}

.vue_newslist2_addData li {
  padding: 3px 0;
  color: #666;
}

.vue_newslist2_btns {
  padding: 6px 0;
}

.vue_newslist2_btns li {
  width: 100%;
  padding: 4px 0;
}

/*通讯录 选择的人**************************************************************/
.sel_warp {
  padding: 5px;
  background: #FFF;
  box-sizing: border-box;
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.sel_warp .sel_item {
  width: 70px;
  text-align: center;
  display: inline-block;
  position: relative;
  margin: 10px 5px;
  box-sizing: border-box;
}

.sel_warp .sel_item .sel_name {
  color: #555;
  margin-top: 5px;
}

.sel_warp .sel_del {
  position: absolute;
  top: -5px;
  right: 2px;
  z-index: 3;
}

/*附件样式 2021-05-19**************************************************************/
.general_attach {
  padding: 14px;
}

.general_attach_item {
  padding: 12px;
  background: #F5F5FB;
  border-radius: 5px;
}

.general_attach_item+.general_attach_item {
  margin-top: 14px;
}

.general_attach_icon {
  margin-right: 7px;
  object-fit: cover;
}

.general_attach_name {
  color: #333333;
  font-weight: 500;
}

.general_attach_size {
  flex-shrink: 0;
  color: #999999;
  font-weight: 500;
  margin-left: 7px;
}

.general_attach_state {
  margin-left: 10px;
}

/* 大号附件 */
.general_attach_big_item {
  padding: 11px 14px;
  background: #FFF;
  width: 100%;
}

.general_attach_big_icon {
  margin-right: 15px;
  object-fit: cover;
}

.general_attach_big_name {
  font-weight: bold;
  color: #333333;
}

.general_attach_big_size {
  color: #999999;
  font-weight: 600;
}

/*列表样式示例在activity	2021-05-31**************************************************************/
.vue_newslist3_box {
  padding: 14px;
}

.vue_newslist3_item {
  padding: 12px 19px;
  border-radius: 10px;
}

.vue_newslist3_warp+.vue_newslist3_warp {
  margin-top: 14px;
}

.vue_newslist3_status {
  border-radius: 50%;
  min-width: 7px;
  min-height: 7px;
}

.vue_newslist3_status_text {
  font-weight: 600;
  margin: 5px;
}

.vue_newslist3_time {
  font-weight: 500;
  color: #999999;
}

.vue_newslist3_title {
  font-weight: 600;
  color: #333333;
  line-height: 1.53;
  margin: 5px 0;
}

body .vue_newslist3_item .van-button {
  padding: 6px 19px;
}

body .vue_newslist3 .van-skeleton__row {
  background-color: #FFF;
}

.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}

.attach_warp .cache span {
  color: blue;
}

/* 红点样式 2022-03-14 17:06:56 */
.redDot_box {
  background: #f92323;
  border-radius: 50%;
  z-index: 1;
  color: #FFF;
  white-space: nowrap;
}

.redDot_small {
  position: absolute;
  top: 2px;
  right: calc(50% - 23px);
}

.redDot_big {
  position: absolute;
  top: -4px;
  right: calc(50% - 28px);
}

/* 必要 zySelectUser组件所需样式 */
body .component_selectuser_warp {
  box-sizing: border-box;
}

body .component_selectuser_box {
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  over-flow: auto;
}

body .component_selectuser_item {
  text-align: center;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  margin-top: 5px;
  width: 63px;
  vertical-align: top;
}

body .component_selectuser_item+.component_selectuser_item {
  margin-left: 8px;
}

body .component_selectuser_del {
  position: absolute;
  top: -3px;
  right: 2px;
  z-index: 99;
}

body .component_selectuser_name {
  color: #333;
  margin-top: 5px;
  line-height: 1.3;
  white-space: normal;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.notRead {
  position: absolute;
  top: 0.0rem;
  right: 0rem;
  width: 8px;
  height: 8px;
  background: #E81B26;
  border-radius: 50%;
}