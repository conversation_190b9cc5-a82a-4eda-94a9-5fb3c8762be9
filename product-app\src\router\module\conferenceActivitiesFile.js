const activity = [
  {
    path: '/conferenceActivities',
    name: 'conferenceActivities',
    component: () => import('@/views/conferenceActivitiesFile/conferenceActivities.vue'),
    meta: {
      title: '会议活动'
    }
  },
  {
    path: '/meetingDetailFile',
    name: 'meetingDetailFile',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingDetailFile/meetingDetailFile.vue'),
    meta: {
      title: '会议详情', // 会议详情
      keepAlive: false
    }
  },
  {
    path: '/meetingfileList',
    name: 'meetingfileList',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingfile/meetingfileList.vue'),
    meta: {
      title: '附件', // 会议附件(外)
      keepAlive: false
    }
  },
  {
    path: '/meetingfileList2',
    name: 'meetingfileList2',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingfile/meetingfileList2.vue'),
    meta: {
      title: '附件', // 会议附件(内)
      keepAlive: false
    }
  },
  {
    path: '/ConferenceAffairsManagement',
    name: 'ConferenceAffairsManagement',
    component: () => import('@/views/conferenceActivitiesFile/meeting/ConferenceAffairsManagement/ConferenceAffairsManagement.vue'),
    meta: {
      title: '会务管理', // 会务管理
      keepAlive: false
    }
  },
  {
    path: '/meetingLeave',
    name: 'meetingLeave',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingLeave/meetingLeave.vue'),
    meta: {
      title: '会议请假', // 会议请假
      keepAlive: false
    }
  },
  {
    path: '/meetingleaveApproval',
    name: 'meetingleaveApproval',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingleaveApproval/meetingleaveApproval.vue'),
    meta: {
      title: '请假审核', // 请假审核
      keepAlive: false
    }
  },
  {
    path: '/meetingPersonnel',
    name: 'meetingPersonnel',
    component: () => import('@/views/conferenceActivitiesFile/meeting/meetingPersonnel/meetingPersonnel.vue'),
    meta: {
      title: '参会人员信息', // 参会人员信息
      keepAlive: false
    }
  },
  {
    path: '/AttendanceManagement',
    name: 'AttendanceManagement',
    component: () => import('@/views/conferenceActivitiesFile/meeting/AttendanceManagement/AttendanceManagement.vue'),
    meta: {
      title: '考勤管理', // 考勤管理
      keepAlive: false
    }
  },

  {
    path: '/activitesDetailFile',
    name: 'activitesDetailFile',
    component: () => import('@/views/conferenceActivitiesFile/activites/activitesDetailFile/activitesDetailFile.vue'),
    meta: {
      title: '活动详情', // 活动详情
      keepAlive: false
    }
  },
  {
    path: '/activitesfileList',
    name: 'activitesfileList',
    component: () => import('@/views/conferenceActivitiesFile/activites/activitesfile/activitesfileList.vue'),
    meta: {
      title: '附件', // 活动附件(外)
      keepAlive: false
    }
  },
  {
    path: '/activitesLeave',
    name: 'activitesLeave',
    component: () => import('@/views/conferenceActivitiesFile/activites/activitesLeave/activitesLeave.vue'),
    meta: {
      title: '活动请假', // 活动请假
      keepAlive: false
    }
  },
  {
    path: '/activityManagement',
    name: 'activityManagement',
    component: () => import('@/views/conferenceActivitiesFile/activites/activityManagement/activityManagement.vue'),
    meta: {
      title: '活动管理', // 活动管理
      keepAlive: false
    }
  },
  {
    path: '/activitesAttendanceManagement',
    name: 'activitesAttendanceManagement',
    component: () => import('@/views/conferenceActivitiesFile/activites/activitesAttendanceManagement/activitesAttendanceManagement.vue'),
    meta: {
      title: '活动管理', // 活动管理
      keepAlive: false
    }
  }
  // {
  //   path: '/vantEmptyGeneral',
  //   name: 'vantEmptyGeneral',
  //   component: () => import('@/views/component/vantEmptyGeneral.vue'),
  //   meta: {
  //     title: '暂无数据', // 缺省页面
  //     keepAlive: false
  //   }
  // },

]
export default activity
