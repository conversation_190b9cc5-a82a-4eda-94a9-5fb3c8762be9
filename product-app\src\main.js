import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import vant from './assets/vant'
import '@vant/touch-emulator'

import 'amfe-flexible'
import './assets/css/index.less'
import Hammer from 'hammerjs'

// 调试器
// import Vconsole from 'vconsole'
// new Vconsole() // eslint-disable-line

const app = createApp(App)
app.directive('tap', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('tap', binding.value)
  }
})
app.directive('doubletap', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('doubletap', binding.value)
  }
})
app.directive('press', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('press', binding.value)
  }
})
app.use(store).use(router).use(vant).mount('#app')
