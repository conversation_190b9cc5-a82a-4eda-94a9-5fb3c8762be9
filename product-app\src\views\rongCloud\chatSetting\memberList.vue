<template>
  <div class="chatSetting">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">

      </van-nav-bar>
      <div id="search"
           class="search_box">
        <div class="search_warp flex_box">
          <div @click="btnSearch();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#666'"
                      class-prefix="icon"
                      :name="'sousuo'"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"> <input id="searchInput"
                   class="flex_placeholder"
                   :style="'font-size:15px;'"
                   :placeholder="'请输入搜索内容'"
                   maxlength="100"
                   type="search"
                   v-model="seachText" />

          </form>
        </div>
      </div>
    </van-sticky>
    <van-tabs v-model:active="switchs.value"
              swipeable
              sticky
              offset-top="50px"
              :color="appTheme"
              :ellipsis="false"
              line-height='3'>
      <van-tab v-for="item in switchs.data"
               :title="item.label"
               :name="item.value"
               :key="item.value">
        <van-checkbox-group v-if="members.data.length != 0"
                            v-model="selects">
          <van-cell-group>
            <van-cell v-for="(item,index) in members.data"
                      clickable
                      :key="item.id"
                      v-show="item.name.indexOf(seachText) != -1"
                      @click="openDetails(item,index)">
              <div class="flex_box flex_align_center">
                <van-checkbox v-if="ifMultiple"
                              :disabled="userId == item.id"
                              :icon-size="20"
                              :style="'margin-right:10px;'"
                              :checked-color="appTheme"
                              :name="item.id"
                              ref="checkboxes"></van-checkbox>
                <div class="flex_placeholder flex_box flex_align_center">
                  <div style="position: relative;">
                    <img :style="'width:41px;height:41px;margin-right:10px;'"
                         :src="item.url"
                         alt=""
                         srcset="">
                    <div v-if="item.isGroupOwner"
                         :style="'font-size:12px;'+'color:'+appTheme"
                         class="group_admin">群主</div>
                  </div>
                  <div class="flex_placeholder">{{item.name}}</div>
                </div>
              </div>
            </van-cell>
            <div v-if="seachText && !hasSeach()"
                 class="notText"
                 :style="'font-size:12px;'+'padding:60px 0;'"
                 v-html="'没有找到与“<span class=\'inherit\' style=\'color:'+appTheme+';\'>'+seachText+'</span>”相关的联系人'"></div>
          </van-cell-group>
        </van-checkbox-group>
        <div v-else
             class="notText">暂无数据</div>
      </van-tab>
    </van-tabs>
    <van-overlay :show="overlayShow">
      <van-loading size="24px"
                   style="text-align:center;margin-top:40%;">加载中...</van-loading>
    </van-overlay>
    <van-action-sheet v-model:show="show"
                      :actions="actions"
                      :description="description"
                      cancel-text="取消"
                      @select="onSelect"
                      close-on-click-action />
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject, watch } from 'vue'
import { NavBar, Sticky, CellGroup, Button, Field, Popup, Image as VanImage, Divider, ActionSheet, Grid, GridItem, Toast, Loading, Overlay } from 'vant'
import { useRoute, useRouter } from 'vue-router'
export default {
  name: 'chatSetting',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Overlay.name]: Overlay,
    [Loading.name]: Loading,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Popup.name]: Popup,
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Divider.name]: Divider,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [Field.name]: Field
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const $general = inject('$general')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      // eslint-disable-next-line eqeqeq
      title: route.query.title || '群成员列表',
      id: route.query.id,
      name: route.query.name,
      user: JSON.parse(sessionStorage.getItem('user')),
      conversationType: Number(route.query.conversationType),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      isGroupAdmin: route.query.isGroupAdmin === 'true' || false, // 是否群组管理员
      members: { more: false, show: false, moreNum: 14, search: '', data: [] }, // 是否有查看更多 多少触发查看 群成员管理
      seachText: '', // 搜索词
      listData: [], // 列表数据
      switchs: {
        value: 1,
        data: [{
          label: '发言人列表',
          value: 1
        }, {
          label: '不发言人列表',
          value: 2
        }]
      },
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部 btn为按钮

      show: false,
      overlayShow: false,
      description: '',
      actions: [
        { name: '退出群组', color: '#ee0a24' }
      ],
      nowUserId: '',

      ifMultiple: false // 是否多选
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      data.pageNo = 1
      data.members.data = []
      data.loading = true
      data.finished = false
      getGroupMemberList()
    })
    onMounted(() => {
      console.log(data.isGroupAdmin)
      if (data.isGroupAdmin === true) {
        data.switchs.data = [{
          label: '发言人列表',
          value: 1
        }, {
          label: '不发言人列表',
          value: 2
        }]
      } else {
        data.switchs.data = [{
          label: '群成员',
          value: 1
        }]
      }
      getGroupMemberList()
    })

    const getGroupMemberList = async () => {
      data.overlayShow = true
      var { data: gruopInfo } = await $api.rongCloud.getGroupMember({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        whetherToDiscuss: data.switchs.value
      })
      if (data.switchs.value === 1) {
        data.switchs.data[0].label = '发言人列表(' + gruopInfo.length + '人)'
        if (data.isGroupAdmin === true) {
          data.switchs.data[0].label = '群成员(' + gruopInfo.length + '人)'
        }
      }
      data.members.data = []
      const newData = []
      data.overlayShow = false
      gruopInfo.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        var item = {}; var itemData = _eItem
        item.id = itemData.id || ''
        item.name = itemData.userName || ''
        item.url = itemData.headImg
        item.position = itemData.position || ''
        item.isGroupOwner = itemData.isGroupOwner

        // eslint-disable-next-line eqeqeq
        if (item.isGroupOwner && item.id == data.user.id) {
          data.isGroupAdmin = true
        }
        // eslint-disable-next-line eqeqeq
        if (item.id == data.user.id && data.ifMultiple && data.selects.length == 0) { // 是当前人 是多选 没有添加过
          data.selects.push(data.user.id)
        }
        // 管理员到第一个去
        if (item.isGroupOwner) { newData.unshift(item) } else { newData.push(item) }
      })
      data.members.data = data.members.data.concat(newData)
    }
    // 是否有搜索对象
    const hasSeach = () => {
      var hasSeach = false
      data.members.data.forEach(function (_eItem, _eIndex, _eArr) {
        // eslint-disable-next-line eqeqeq
        if (_eItem.name.indexOf(data.seachText) != -1) {
          hasSeach = true
        }
      })
      return hasSeach
    }
    const openDetails = (_item, _index) => {
      const menus = sessionStorage.getItem('menus')
      if (!_item.isGroupOwner && menus.indexOf('auth:talkroup:speak') >= 0) {
        data.nowUserId = _item.id
        data.description = '请选择操作'
        // eslint-disable-next-line eqeqeq
        if (data.switchs.value == 1) {
          data.actions = [
            { name: '禁止发言', color: '#ee0a24' },
            { name: '查看信息' }
          ]
        } else {
          data.actions = [
            { name: '允许发言', color: '#ee0a24' },
            { name: '查看信息' }
          ]
        }
        data.show = true
      } else {
        router.push({ name: 'personData', query: { id: _item.id } })
      }
    }
    const addGroupUser = async (type) => {
      const res = await $api.rongCloud.updateWhether({
        businessId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1],
        groupName: data.name,
        userId: data.nowUserId,
        whetherToDiscuss: type
      })
      if (res.errcode === 200) {
        Toast('操作成功！')
        getGroupMemberList()
        data.nowUserId = ''
      }
    }
    const onSelect = (item) => {
      switch (item.name) {
        case '允许发言':
          addGroupUser(1)
          break
        case '禁止发言':
          addGroupUser(2)
          break
        case '查看信息':
          router.push({ name: 'personData', query: { id: data.nowUserId } })
          break
      }
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, hasSeach, openDetails, onSelect }
  }
}
</script>
<style lang="less" scoped>
.chatSetting {
  width: 100%;
  .search_box {
    background: #f8f8f8;
  }
  .group_admin {
    position: absolute;
    right: -5px;
    top: -2px;
    font-weight: bold;
  }
}
</style>
