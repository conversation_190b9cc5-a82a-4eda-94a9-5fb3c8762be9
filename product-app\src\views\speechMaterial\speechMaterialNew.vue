<template>
  <div class="activityNew">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.meetName"
                   required
                   name="材料主题"
                   show-word-limit
                   label="材料主题"
                   placeholder="请输入材料主题"
                   :rules="rules.meetName" />
        <van-field v-model="form.spokesman"
                   name="发言人"
                   label-width="4.8em"
                   label="发言人"
                   disabled
                   placeholder="请输入发言人" />
        <div style="width: 100%;display: flex;justify-content: space-between; align-items:center;border-bottom: 1px solid #f8f8f8;">
          <van-cell title="会议名称"
                    class="titleStyle" />
          <van-dropdown-menu active-color="#1989fa"
                             class="marginTop"
                             :rules="[{ required: true, message: '请选择会议名称' }]">
            <van-dropdown-item v-model="form.typeSmall"
                               @close="close"
                               :options="typeSmall" />
          </van-dropdown-menu>
        </div>
        <van-field size="large"
                   class="time-box"
                   input-align="right"
                   label="时间"
                   readonly
                   v-model="form.signBeginTime"
                   placeholder="请选择时间"
                   :rules="rules.signBeginTime"
                   required
                   @click="openTimeFn()"
                   right-icon="clock-o" />
        <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="正文"
                   rows="6"
                   maxlength="2000"
                   show-word-limit
                   type="textarea"
                   placeholder="请输入正文"
                   :rules="rules.content" />
      </van-cell-group>
      <div class="newButton">
        <div class="T-flexbox-vertical flex_align_center flex_justify_content click"
             style="padding: 0 13px 0 8px;"
             @click="oneReset(0)"
             native-type="submit">
          <img :style="$general.loadConfigurationSize(14)"
               src="../../assets/img/icon_draft.png" />
          <div :style="$general.loadConfiguration(-6)+'color: #888;'">存草稿</div>
        </div>
        <div class="flex_placeholder">
          <van-button round
                      size="large"
                      color="#3088FE"
                      native-type="submit">
            提交</van-button>
        </div>
      </div>
    </van-form>

    <van-popup v-model:show="signBeginTimeShow"
               position="bottom">
      <van-datetime-picker v-model="signBeginTime"
                           title="选择时间"
                           :formatter="formatter"
                           type='datetime'
                           @confirm="signBeginTimeConfirm(signBeginTime)"
                           @cancel="signBeginTimeShow = false">
      </van-datetime-picker>
    </van-popup>
  </div>
</template>
<script>
import { onMounted, reactive, ref, toRefs, inject } from 'vue'
import { DatetimePicker, DropdownMenu, DropdownItem, Toast, Dialog } from 'vant'
import { useRouter, useRoute } from 'vue-router'
export default {
  name: 'activityNew',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem

  },
  setup () {
    const route = useRoute()
    const dayjs = require('dayjs')
    const router = useRouter()
    const $general = inject('$general')

    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      form: {
        typeSmall: '', // 会议名称
        meetName: '', // 主题
        content: '', // 内容
        signBeginTime: '', // 时间
        spokesman: '' // 发言人
      },
      id: route.query.id,
      rules: {
        meetName: [{ required: true, message: '请输入材料主题' }],
        signBeginTime: [{ required: true, message: '请选择时间' }],
        content: [{ required: true, message: '请输入内容' }]
      },
      typeSmall: [],
      signBeginTimeShow: false,
      signBeginTime: ''
    })
    const $api = inject('$api')
    const user = ref(null)

    onMounted(() => {
      data.form.spokesman = data.user.userName
      data.form.signBeginTime = dayjs().format('YYYY-MM-DD HH:mm')
      getSpeechNoticeMap()
      if (data.id) {
        getInfo()
      }
    })
    // 详情回显并编辑提交
    const getInfo = async () => {
      console.log('从详情进来的')
      const res = await $api.speechMaterial.conferencespeechInfo(data.id)
      console.log('请求详情=====', res)
      data.form.meetName = res.data.name
      // data.form.spokesman = res.data.userId
      data.form.typeSmall = res.data.meetNoticeId
      data.form.signBeginTime = res.data.speechDate
      data.form.content = res.data.content
    }
    // 提交
    const onSubmit = async (values) => {
      var datas = data.form
      if (data.id) {
        const res = await $api.speechMaterial.conferencespeechEdit({
          empty: '1',
          id: data.id,
          userId: data.user.id,
          speechState: 20,
          name: datas.meetName,
          meetNoticeId: datas.typeSmall,
          content: datas.content,
          speechDate: datas.signBeginTime
        })
        var { errcode, errmsg } = res
        if (errcode === 200) {
          Toast.success(errmsg)
          router.go(-1)
        }
      } else {
        const res = await $api.speechMaterial.conferencespeechAdd({
          empty: '1',
          userId: data.user.id,
          speechState: 20,
          name: datas.meetName,
          meetNoticeId: datas.typeSmall,
          content: datas.content,
          speechDate: datas.signBeginTime
        })
        console.log(res)
        var { errcode: errcodes, errmsg: errmsgs } = res
        if (errcodes === 200) {
          Toast.success(errmsgs)
          router.go(-1)
        }
      }
    }
    // 草稿箱
    const oneReset = (value) => {
      var datas = data.form
      if (value === 0) {
        Dialog.confirm({
          title: '',
          message: '确定要存草稿吗?'
        }).then(async () => {
          const res = await $api.speechMaterial.conferencespeechAdd({
            empty: '1',
            userId: data.user.id,
            speechState: 10,
            name: datas.meetName,
            meetNoticeId: datas.typeSmall,
            content: datas.content,
            speechDate: datas.signBeginTime
          })
          var { errcode, errmsg } = res
          if (errcode === 200) {
            Toast.success(errmsg)
            router.go(-1)
          }
        }).catch(() => {
          // on cancel
        })
      }
    }
    // 打开时间控件
    const signBeginTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.signBeginTime = new Date(_item.value.replace(/-/g, '/'))
      data.form.signBeginTime = _item.value
      data.signBeginTimeShow = false
    }
    // 点击打开时间  没有初始时间时 设置当前时间
    const openTimeFn = () => {
      if (!data.signBeginTime) {
        data.signBeginTime = new Date()
      } else {
        data.signBeginTime = new Date(data.form.signBeginTime.replace(/-/g, '/'))
      }
      console.log('data.signBeginTime', data.signBeginTime)
      data.signBeginTimeShow = true
    }
    // 格式化时间
    const formatter = (type, value) => {
      if (type === 'year') {
        return value + '年'
      } else if (type === 'month') {
        return value + '月'
      } else if (type === 'day') {
        return value + '日'
      } else if (type === 'hour') {
        return value + '时'
      } else if (type === 'minute') {
        return value + '分'
      }
      return value
    }
    // 获取会议名称
    const getSpeechNoticeMap = async () => {
      const res = await $api.speechMaterial.getSpeechNoticeMap({
        userId: data.user.id
      })
      console.log('获取会议名称', res)
      var arr = []
      res.data.forEach(item => {
        arr.push({ text: item.name, value: item.id })
      })
      data.typeSmall = arr
      console.log('data.typeSmall', data.typeSmall)
      data.form.typeSmall = data.typeSmall[0].value
    }
    // 关闭下拉菜单栏时触发(选择菜单栏里内容后触发)
    // const close = () => {
    //   var arrSmall = []
    //   data.meetType.forEach(itemBig => {
    //     if (data.form.meetType === itemBig.value) {
    //       itemBig.children.forEach(itemSmall => {
    //         arrSmall.push({ text: itemSmall.name, value: itemSmall.id })
    //       })
    //     }
    //   })
    //   data.typeSmall = arrSmall
    // }
    return { ...toRefs(data), $general, user, onSubmit, oneReset, close, signBeginTimeConfirm, formatter, openTimeFn, getSpeechNoticeMap }
  }
}
</script>
<style lang="less" >
.activityNew {
  background: #fff;
  .van-form {
    width: 100%;
    .van-cell-group--inset {
      margin: 0;
      .van-field {
        margin: 20px 0;
      }
    }
    .newContent {
      flex-wrap: wrap;
      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }
      .van-field__body {
        background-color: #f5f5f5;
        padding: 6px 12px;
      }
    }
    .van-field__control {
      font-size: 14px;
    }
    .time-box {
      .van-cell__title {
        width: 120px;
      }
    }
    .activity-box {
      .van-cell__title {
        width: 220px !important;
      }
    }
  }
  .titleStyle {
    background-color: #fff;
    height: 48px;
    // width: 100%;
    width: 50%;
  }
  .van-dropdown-menu__title {
    margin-right: 15px;
  }
  --van-dropdown-menu-box-shadow: 0;
  .marginTop {
    margin-top: 20px;
  }
  .newButton {
    display: flex;
    justify-content: space-around;
    padding: 36px 18px;
    // padding-bottom: 88px;
    .van-button {
      // width: 128px;
      // height: 36px;
    }
  }
}
</style>>
