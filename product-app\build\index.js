const os = require('os')
// 获取命令行变量
const configArgv = JSON.parse(process.env.npm_config_argv)
const original = configArgv.original.slice(1)
const stage = original[1] ? original[1].replace(/-/g, '') : ''
const equipment = original[2] ? original[2].replace(/-/g, '') : 'PC'
// 本地ip地址
let localUrl
try {
  const network = os.networkInterfaces()
  localUrl = network[Object.keys(network)[0]][1].address
} catch (e) {
  localUrl = 'localhost'
}
localUrl = 'http://' + localUrl + '/'

module.exports = {
  equipment, stage, localUrl
}
