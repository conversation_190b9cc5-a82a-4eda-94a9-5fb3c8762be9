import { HTTP } from '../http.js'
class speechMaterial extends HTTP {
  // 征稿通知列表
  speechnoticeList (params) {
    return this.request({ url: '/speechnotice/list?', data: params })
  }

  // 征稿通知详情
  speechnoticeInfo (params) {
    return this.request({ url: `/speechnotice/info/${params}` })
  }

  // 公开发言列表
  conferencespeechList (params) {
    return this.request({ url: '/conferencespeech/list?', data: params })
  }

  // 公开发言详情
  conferencespeechInfo (params) {
    return this.request({ url: `/conferencespeech/info/${params}` })
  }

  // 我的列表
  findMyConferenceSpeechs (params) {
    return this.request({ url: '/conferencespeech/findMyConferenceSpeechs?', data: params })
  }

  // 草稿箱列表
  findConferenceSpeechDrafts (params) {
    return this.request({ url: '/conferencespeech/findConferenceSpeechDrafts?', data: params })
  }

  // 删除草稿箱
  conferencespeechDels (params) {
    return this.request({ url: '/conferencespeech/dels?', data: params })
  }

  // 获取发言会议名称
  getSpeechNoticeMap (params) {
    return this.request({ url: '/speechnotice/getSpeechNoticeMap?', data: params })
  }

  // 提交会议发言
  conferencespeechAdd (params) {
    return this.request({ url: '/conferencespeech/add', data: params })
  }

  // 编辑会议发言
  conferencespeechEdit (params) {
    return this.request({ url: '/conferencespeech/edit', data: params })
  }
}
export {
  speechMaterial
}
