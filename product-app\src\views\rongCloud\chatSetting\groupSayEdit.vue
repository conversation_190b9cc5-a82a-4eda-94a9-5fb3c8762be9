<template>
  <div class="groupSayEdit">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="submit">
        <!-- <template #right>
          <van-icon name="guide-o"
                    size="20" />&nbsp;确定
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <van-field id="message"
               v-model="message"
               rows="11"
               type="textarea"
               show-word-limit
               maxlength="1000"
               placeholder="请输入公告内容"></van-field>
    <van-button @click="submit"
                type="parmary"
                :color="appTheme"
                block>确定</van-button>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import * as RongIMLib from '@rongcloud/imlib-next'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, Sticky, NavBar } from 'vant'
// import moment from 'moment'
export default {
  name: 'groupSayEdit',
  components: {
    [Button.name]: But<PERSON>,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      noteId: route.query.noteId,
      title: route.query.title || '编辑公告',
      user: JSON.parse(sessionStorage.getItem('user')),
      message: ''
    })
    onMounted(() => {
      getData()
      // RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
    })
    if (data.title) {
      document.title = data.title
    }
    const getData = async () => {
      var ret = await $api.rongCloud.getGroupInfo({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })
      if (ret && ret.data) {
        data.message = ret.data.openSay || ''
      }
    }
    const submit = async () => {
      var ret = await $api.rongCloud.editopensay({ id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1], openSay: data.message })
      if (ret) {
        Toast('修改' + (ret.errcode === 200 ? '成功' : '失败'))
        RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
          console.log(res)
          sendMessage()
        })
        setTimeout(() => {
          onClickLeft()
        }, 1000)
      }
    }
    const sendMessage = function () {
      if (data.message) {
        // 定义消息投送目标会话
        const conversation = { conversationType: 3, targetId: data.id }
        // 实例化待发送消息，RongIMLib.TextMessage 为内置文本型消息
        const message = new RongIMLib.TextMessage({ content: '最新群公告：' + data.message })
        // 发送
        RongIMLib.sendMessage(conversation, message).then(res => {
          console.log('发送群公告：', res)
        })
      } else {
        Toast('消息不能为空')
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), submit, onClickLeft }
  }
}
</script>
<style lang="less" scoped>
.groupSayEdit {
  width: 100%;
  background: #f9f9f9;
  position: relative;
  .van-button {
    width: calc(100% - 20px);
    margin: 10px;
  }
}
</style>
