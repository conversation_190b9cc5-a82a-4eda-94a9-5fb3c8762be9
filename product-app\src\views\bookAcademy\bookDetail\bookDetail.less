.bookDetail {
  width: 100%;

  .footer_btn {
    padding: 15px 0;
    width: 100%;
    text-align: center;
    line-height: 1;
  }



  .details_box {
    padding: 15px 10px 10px 10px;
  }

  .details_img {
    position: relative;
    margin-right: 15px;
    width: 129px;
    height: 165px;

    img {
      width: 100%;
      height: 100%;
    }

    .item_overdue {
      position: absolute;
      top: 0;
      right: 0;
      width: 52px;
      height: 52px;
    }
  }


  .details_entity {
    position: absolute;
    right: 0;
    top: 0;
    background: #4CD964;
    color: #FFF;
    padding: 2px 10px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    font-size: 10px;
  }

  .details_name {
    color: #222;
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 20px;
  }

  .details_label {
    padding: 10px 0 0 0;
  }

  .details_summary {
    color: #8B8A8A;
    padding: 0 10px;
    margin-bottom: 10px;
  }

  .details_statistics {
    border-radius: 10px;
    background: #FFF;
    width: 100%;
    box-shadow: 0px 0px 10px -6px rgba(0, 0, 0, 0.4);
  }

  .details_statistics_item {
    padding: 15px 0;
    width: 100%;
    text-align: center;
    color: #5E646D;
  }

  .details_statistics_line {
    width: 1px;
    height: 40px;
    background: #eee;
  }

  .commentList_hint {
    min-height: 45px;
    padding-left: 20px;
  }

  .qunw {
    color: #2286f5;
    padding: 0 0 5px 0px;
  }

}

.van-button {
  border: 0 !important;
}

#app .van-empty__image {
  width: auto;
}

.van-empty__image img {
  width: auto;
}

#app .van-empty__bottom .van-button {
  padding: 6px 18px;
}