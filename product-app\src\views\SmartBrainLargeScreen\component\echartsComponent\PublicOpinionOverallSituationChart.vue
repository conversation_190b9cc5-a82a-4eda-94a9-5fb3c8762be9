<template>
  <div class="public-opinion-chart">
    <div class="chart-container">
      <!-- 左侧环形图 -->
      <div class="donut-chart">
        <div :id="chartId" class="chart"></div>
      </div>
      <!-- 右侧指标 -->
      <div class="metrics-container">
        <div class="metric-item">
          <div class="metric-label">社情民意总件数</div>
          <div class="metric-value total-count">{{ totalCount }}</div>
        </div>
        <div class="metric-item" style="gap: 38px;">
          <div class="metric-label">采用总件数</div>
          <div class="metric-value adopted-count">{{ adoptedCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'PublicOpinionChart',
  props: {
    id: {
      type: String,
      default: ''
    },
    totalCount: {
      type: Number,
      default: 0
    },
    adoptedCount: {
      type: Number,
      default: 0
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null
    const selectedIndex = ref(0) // 当前选中的索引，默认第一个
    // 计算采用率
    const calculateAdoptedRate = () => {
      if (props.totalCount > 0) {
        return ((props.adoptedCount / props.totalCount) * 100).toFixed(2)
      }
      return props.adoptedRate
    }

    // 获取当前选中项的信息
    const getSelectedItemInfo = () => {
      const rate = calculateAdoptedRate()
      const remainingRate = (100 - parseFloat(rate)).toFixed(2)
      const data = [
        {
          value: parseFloat(rate),
          name: '采用率',
          label: '采用率',
          color: '#007AFF'
        },
        {
          value: parseFloat(remainingRate),
          name: '未采用',
          label: '未采用',
          color: '#FF6B6B'
        }
      ]
      return data[selectedIndex.value]
    }

    // 初始化图表
    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        const rate = calculateAdoptedRate()
        const remainingRate = (100 - parseFloat(rate)).toFixed(2)
        const selectedItem = getSelectedItemInfo()
        // 数据下限处理，保证最小宽度
        const minValue = 5
        const adoptedValue = Math.max(parseFloat(rate), minValue)
        const notAdoptedValue = Math.max(parseFloat(remainingRate), minValue)
        const option = {
          series: [
            {
              type: 'pie',
              radius: ['65%', '85%'],
              center: ['50%', '50%'],
              // roseType: 'radius',
              avoidLabelOverlap: true,
              label: {
                show: true,
                position: 'center',
                formatter: function () {
                  return `{rate|${selectedItem.value}%}\n{label|${selectedItem.label}}`
                },
                rich: {
                  rate: {
                    fontSize: 20,
                    color: '#333',
                    lineHeight: 30
                  },
                  label: {
                    fontSize: 14,
                    color: '#999',
                    lineHeight: 16
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {
                  value: adoptedValue,
                  name: '采用率',
                  itemStyle: {
                    color: '#007AFF'
                  }
                },
                {
                  value: notAdoptedValue,
                  name: '未采用',
                  itemStyle: {
                    color: '#FF6B6B'
                  }
                }
              ]
            }
          ]
        }
        chartInstance.setOption(option)
        // 添加点击事件监听
        chartInstance.off('click')
        chartInstance.on('click', (params) => {
          if (params.name === '采用率') {
            selectedIndex.value = 0
          } else if (params.name === '未采用') {
            selectedIndex.value = 1
          }
          // 重新渲染图表以更新label
          updateChartLabel()
        })
      })
    }

    // 更新图表label
    const updateChartLabel = () => {
      if (!chartInstance) return
      const selectedItem = getSelectedItemInfo()
      const option = chartInstance.getOption()
      option.series[0].label.formatter = function () {
        return `{rate|${selectedItem.value}%}\n{label|${selectedItem.label}}`
      }
      chartInstance.setOption(option)
    }

    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 监听数据变化
    watch(() => [props.totalCount, props.adoptedCount, props.adoptedRate], () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.public-opinion-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 10px;

    .donut-chart {
      flex: 1;
      height: 100%;
      max-width: 50%;

      .chart {
        width: 100%;
        height: 100%;
      }
    }

    .metrics-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 20px;

      .metric-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .metric-label {
          font-size: 14px;
          color: #666;
          font-weight: 400;
        }

        .metric-value {
          font-size: 20px;

          &.total-count {
            color: #3A93FF;
          }

          &.adopted-count {
            color: #FF738D;
          }
        }
      }
    }
  }
}
</style>
