<template>
  <div class="myCollection">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <van-search v-model="keyword"
                  @search="search"
                  @clear="search"
                  placeholder="请输入搜索内容" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <li v-for="(item,index) in listData"
              :key="index"
              class="van-hairline--bottom">
            <van-cell clickable
                      class="vue_newslist_item "
                      @click="openDetails(item)">
              <div class="flex_box">
                <img class="vue_newslist_img"
                     v-if="item.url"
                     :src="item.url" />
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                       :style="$general.loadConfiguration()">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="$general.loadConfiguration(-4)">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    {{item.title}}
                  </div>
                  <div v-if="item.url"
                       class="vue_newslist_time"
                       :style="$general.loadConfiguration(-3) + 'margin-bottom:0.09rem;'">{{item.time.split(' ')[0]}}</div>
                  <div class="flex_box flex_align_center">
                    <div v-if="!item.url"
                         class="vue_newslist_time"
                         :style="$general.loadConfiguration(-3)">{{item.time.split(' ')[0]}}</div>
                    <div class="vue_newslist_source"
                         :style="$general.loadConfiguration(-3)">{{item.source || item.createBy}}</div>
                    <div class="flex_placeholder"></div>
                    <div v-if="item.type"
                         class="vue_newslist_more_right"
                         :style="$general.loadConfiguration(-3)">
                      <van-tag plain
                               :color="appTheme">{{item.type}}</van-tag>
                    </div>
                  </div>
                </div>
              </div>
            </van-cell>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>

import { useRoute, useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet, Tag, NavBar, Sticky } from 'vant'
export default {
  name: 'myCollection',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [Tag.name]: Tag,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id || JSON.parse(sessionStorage.getItem('user').id) || '',
      pageNo: 1,
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      listData: []
    })
    onMounted(() => {
      // onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.loading = true
      getList()
    }
    // 搜索
    const search = () => {
      onRefresh()
    }
    // 获取列表
    const getList = async () => {
      var res = await $api.general.favoriteList({
        pageNo: data.pageNo,
        pageSize: 1000,
        keyword: data.keyword,
        createBy: data.id
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.keyId || ''
        item.baseId = item.id || ''
        item.source = item.org || ''
        item.time = item.createDate || ''
        item.relateType = item.type || ''
        item.type = item.typeName || ''
        item.class = item.typeName
        item.isTop = item.isTop || '' // 置顶 1是0否
        item.areaName = item.areaName || ''
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 进详情
    const openDetails = (row) => {
      if (row.relateType === '53') {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title, type: 'favorite53' } })
      } else {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title, type: 'favorite5' } })
      }
    }
    return { ...toRefs(data), $general, search, dayjs, onLoad, onRefresh, openDetails }
  }
}
</script>
<style lang="less">
.myCollection {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;
  #app .van-cell {
    padding: 10px 10px;
    box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.08);
    opacity: 1;
    border-radius: 4px;
  }
  .list_box {
    padding: 0 16px;
    margin-bottom: 10px;
  }
  .list_box:first-child {
    margin-top: 12px;
  }
  #app .van-tag {
    min-width: 60px;
    min-height: 16px;
    text-align: center;
  }

  footer {
    background: #fff;
    padding: 7px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
</style>
