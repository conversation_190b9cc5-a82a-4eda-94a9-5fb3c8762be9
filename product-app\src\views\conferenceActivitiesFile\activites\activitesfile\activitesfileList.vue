<template>
  <div class="meetingfileList">
    <van-pull-refresh v-model="refreshing"
                      style="min-height: 80vh;"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--展示大号附件-->
        <div v-if="listData.length != 0"
             class="general_attach_big">
          <div v-for="(item,index) in listData"
               :key="index"
               class="flex_box flex_align_center">
            <div class="general_attach_big_item van-hairline--bottom flex_box flex_align_center click"
                 @click="annexClick(item,false)">
              <img class="general_attach_big_icon"
                   :style="general.loadConfigurationSize([26,32])"
                   :src="require('../../../../assets/img/fileicon/'+item.iconInfo.name)" />
              <div class="flex_placeholder">
                <div class="general_attach_big_name text_one2"
                     :style="general.loadConfiguration(-1)">{{item.name}}</div>
                <div class="flex_box flex_align_center"
                     style="margin-top:6px;">
                  <div class="general_attach_big_size flex_placeholder"
                       :style="general.loadConfiguration(-4)">{{general.getFileSize(item.size)}}</div>
                  <div v-if="item.state != 2"
                       class="general_attach_state flex_box flex_align_center flex_justify_content"
                       :style="general.loadConfigurationSize([7,7])">
                    <van-icon v-if="item.state == 0"
                              class-prefix="iconfont"
                              color="#ccc"
                              :size="((appFontSize+3)*0.01)+'rem'"
                              name="xiazai"></van-icon>
                    <van-circle v-else-if="item.state == 1"
                                :size="((appFontSize+3)*0.01)+'rem'"
                                v-model="item.schedule"
                                :rate="item.schedule"
                                stroke-width="150"></van-circle>
                    <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                              v-else-if="item.state == 3"
                              color="#ccc"
                              :size="((appFontSize+3)*0.01)+'rem'"
                              name="warning-o"></van-icon>
                  </div>
                </div>
              </div>
              <van-switch v-if="management"
                          :active-color="appTheme"
                          @click.stop=""
                          @change="editIsAppShow(item)"
                          v-model="item.isShow"
                          :size="((appFontSize+8)*0.02)+'rem'"></van-switch>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
    <template v-if="showEmpty||listData.length == 0">
      <!-- 缺省图 -->
      <!-- <vantEmptyGeneral /> -->
    </template>
    <!--返回顶部 需要加一个空白占位 不然返回顶部 就会错位显示 -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box">
        {{'&nbsp;'}}
        <div :style="general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="((appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </ul>
    </transition>
    <!--不为一级页面时 适配底部条-->
    <footer :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
// import vantEmptyGeneral from '@/views/component/vantEmptyGeneral'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Toast } from 'vant'
export default {
  name: 'meetingfileList',
  components: {
    // vantEmptyGeneral,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      showEmpty: false,
      pageParam: { isDetails: false },
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      title: route.query.title || '',
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      scrollTop: 0, // 页面划动距离
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      listData: [], // 列表数据
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮
      management: route.query.management // 是否资料管理
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      if (data.id) {
        getDetails()
      } else {
        data.showEmpty = true
      }
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getDetails()
    }
    const onLoad = () => {

    }
    const getDetails = async () => {
      var postParam = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        meetId: data.id,
        isAppShow: 1
      }
      if (data.management) {
        postParam.isAppShow = ''
      }
      const res = await $api.conferenceActivitiesFile.getMaterainfoList(postParam) // 详情
      var { data: list } = res
      console.log('🚀 ~ file: meetingfileList.vue ~ line 222 ~ getDetails ~ list', list)
      data.loading = false
      data.refreshing = false
      data.finished = true
      if (general.isArray(list) && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.isShow = _eItem.isAppShow === 1 // 是否显示
          item.id = _eItem.id
          _eItem = _eItem.attachmentList[0]
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.iconInfo = general.getFileTypeAttr(_eItem.fileType)
          data.listData = data.listData.concat(item)
          console.log('data.listData', data.listData)
        })
        data.pageNo++
      }
    }
    // 切换是否公开
    const editIsAppShow = async (_item) => {
      console.log('_item====>', _item)
      const res = await $api.conferenceActivitiesFile.editIsAppShow(_item.id, _item.isShow ? 1 : 0)
      console.log('设置是否公开===', res)
      Toast('已设置' + (!_item.isShow ? '不' : '') + '公开')
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, general, confirm, editIsAppShow, annexClick }
  }
}
</script>
<style lang="less" scoped>
.meetingfileList {
  background: #f8f8f8;
}
</style>
