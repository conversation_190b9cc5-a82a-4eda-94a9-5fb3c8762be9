<template>
  <div class="memberReportList">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      success-text="刷新成功">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist3_box">
          <div v-for="(item,index) in listData"
               :key="index"
               class="vue_newslist3_warp">
            <van-cell clickable
                      class="vue_newslist3_item "
                      @click="details(item)">
              <div class="flex_box">
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                       :style="$general.loadConfiguration(1)">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="$general.loadConfiguration(-4)">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    <span class="inherit"
                          v-html="item.title"></span>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div class="vue_newslist_time"
                         :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.createBy}}</div>
                    <div class="flex_placeholder"></div>
                    <span class="social_status"
                          :style="$general.loadConfiguration(-3)">{{item.time}}</span>
                  </div>
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
    <div class="footer_box"
         v-if="footerBtns.length != 0">
      <template v-for="(aitem,aindex) in footerBtns"
                :key="aindex">
        <div class="footer_nBtn_box T-flexbox-vertical flex_align_center flex_justify_content"
             @click="footerBtnClick(aitem)">
          <van-icon v-if="aitem.icon"
                    :size="((appFontSize+5)*0.01)+'rem'"
                    :name="aitem.icon"></van-icon>{{aitem.name}}
        </div>
      </template>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
                   showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag } from 'vant'
import moment from 'moment'

export default {
  name: 'memberReportList',
  components: {
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      listData: [],
      footerBtns: [] // 底部按钮集合 top为返回顶部   btn为按钮
    })
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getMemberReportList()
    }
    onMounted(() => {
      getMemberReportList()
      var footer = [{ type: 'nBtn', name: '提交', click: 'submit', icon: 'edit' }]
      data.footerBtns = data.footerBtns.concat(footer)
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getMemberReportList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取委员述职列表
    const getMemberReportList = async () => {
      const res = await $api.memberReport.MemberReportList({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.time = (item.submissionTime) ? moment(item.submissionTime).format('YYYY-MM-DD HH:mm') : '' // 时间
      })
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 详情
    const details = (row) => {
      router.push({ name: 'memberReportDetails', query: { id: row.id } })
    }
    // 提交按钮跳转
    const footerBtnClick = (row, type) => {
      router.push({ name: 'memberReportNew' })
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, search, footerBtnClick, details }
  }
}
</script>
<style lang="less" scoped>
.memberReportList {
  background: #f8f8f8;
  .social_status {
    color: #b8b8b8;
  }
  .footer_box {
    position: fixed;
    right: 25px;
    bottom: 30px;
    border-radius: 50%;
    padding: 8px 12px;
    background-color: #3088fe;
    .footer_nBtn_box {
      color: #fff;
    }
  }
}
</style>
