<template>
  <div class="memberReportDetails">
    <div class="vue_newslist_item ">
      <div class="flex_box">
        <div class="flex_placeholder vue_newslist_warp">
          <div class="vue_newslist_title"
               :style="$general.loadConfiguration(6)+'font-weight:bold;line-height:1.5;'">
            {{title}}
          </div>
          <div class="flex_box flex_align_center flex_align_box">
            <div class="vue_newslist_source"
                 :style="$general.loadConfiguration(-2)">{{org}}</div>
            <div class="flex_placeholder"></div>
            <div class="
                 vue_newslist_time"
                 :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">
              {{dataTime}}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 展示内容content -->
    <div class="n_details_content"
         :style="$general.loadConfiguration(1)"
         v-html="content"></div>
    <!--展示附件-->
    <template v-if="attachInfo.data&&attachInfo.data.length != 0">
      <div class="general_attach"
           style="background-color: #fff;">
        <!-- @click="annexClick(item,false)" -->
        <div v-for="(item,index) in attachInfo.data"
             :key="index"
             @click="annexClick(item)"
             class="general_attach_item flex_box flex_align_center click">
          <img class="general_attach_icon"
               :style="$general.loadConfigurationSize([5,7])"
               :src="require(`../../assets/fileicon/${item.iconInfo.name}`)" />
          <div class="flex_placeholder flex_box flex_align_center">
            <div class="general_attach_name text_one2"
                 style="font-size: 14px;display: -webkit-box;">{{item.name}}</div>
            <div class="general_attach_size"
                 style="font-size: 12px;">{{$general.getFileSize(item.size)}}</div>
          </div>
        </div>
      </div>
    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage } from 'vant'
import moment from 'moment'

export default {
  name: 'memberReportDetails',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      id: route.query.id,
      title: '', // 标题
      dataTime: '', // 时间
      org: '', // 发布部门
      status: '',
      content: '', // 正文内容
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    onMounted(() => {
      getReportInfo()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataInfo = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getReportInfo()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取委员述职详情
    const getReportInfo = async () => {
      const res = await $api.memberReport.MemberReportInfo(data.id)
      var { data: info } = res
      data.title = info.title
      data.org = info.createBy
      data.dataTime = (info.submissionTime) ? moment(info.submissionTime).format('YYYY-MM-DD HH:mm') : '' // 时间
      data.content = info.content
      var attachmentList = info.attachmentList || []
      data.attachInfo.data = []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          data.attachInfo.data.push(item)
        })
      }
      console.log('附件信息======', data.attachInfo)
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, annexClick }
  }
}
</script>
<style lang="less" scoped>
.memberReportDetails {
  background: #fff;
  .n_details_content {
    text-indent: 2em;
    margin: 10px 10px 30px 10px;
    line-height: 27px;
  }
  .footer {
    padding: 10px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }
  .footer .van-button {
    padding: 10px 21px;
    border-color: #e2e2e2;
    font-weight: 600;
  }
}
</style>
