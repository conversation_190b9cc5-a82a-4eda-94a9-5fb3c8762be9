<template>
  <div class="partyBuildingList">
    <!-- 顶图 -->
    <div :style="'background-image:url('+require('../../assets/img/bg_partyBuilding.png')+')'"
         class="top_bgImg">
      <div class="yearTab">
        <div class="flex_placeholder"></div>
        <van-dropdown-menu :active-color="appTheme"
                           :style="$general.loadConfiguration(-3)">
          <van-dropdown-item v-model="years.value"
                             @change="dropdownChange(0)"
                             :options="years.data">
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad"
                :immediate-check="false">
        <!-- <div id="search"
             class="search_box flex_box flex_align_center"
             :style="$general.loadConfiguration()"
             style="position: absolute;top: 10%;right: 0;border-radius: 8px;z-index: 999;">
          <div class="flex_placeholder"></div>

        </div> -->
        <!-- tab -->
        <div v-if="headerItem.data.length > 1"
             :style="$general.loadConfiguration(1)">
          <van-tabs v-model="headerItem.value"
                    @click="tabClick"
                    :color="appTheme"
                    :title-active-color="appTheme"
                    :ellipsis="false">
            <van-tab v-for="(item,index) in headerItem.data"
                     :key="index"
                     :title="item.label"
                     :name="item.value"></van-tab>
          </van-tabs>
        </div>
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <div v-for="(item,index) in dataList"
               :key="index"
               class="van-hairline--bottom">
            <van-cell clickable
                      class="vue_newslist_item "
                      @click="openDetails(item)">
              <div class="flex_box">
                <img class="vue_newslist_img"
                     v-if="item.url"
                     :src="item.url" />
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                       :style="$general.loadConfiguration()">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="$general.loadConfiguration(-4)">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    <span class="inherit"
                          v-html="item.title"></span>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div class="vue_newslist_time">{{dayjs(item.time).format('YYYY-M-D')}}</div>
                    <div class="vue_newslist_source text_one2 flex_placeholder">{{item.source}}</div>
                  </div>
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'partyBuildingList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      headerItem: { value: '', data: [] },
      years: { value: '', data: [] }
    })

    onMounted(() => {
      var nowYear = 2023
      for (var i = 2021; i <= nowYear; i++) {
        data.years.data.push({ text: i, value: i })
      }
      data.years.value = nowYear
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getColumnList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getListData()
    }
    // 切换年份
    const dropdownChange = async () => {
      data.headerItem.value = ''
      getColumnList()
    }
    const getColumnList = async () => {
      if (data.headerItem.value) {
        getListData()
        return
      }
      const res = await $api.partyBuilding.findPartyConfigTypeAndName({
        module: 'information_party',
        parentId: 0,
        year: data.years.value
      })
      var { data: list } = res
      data.headerItem.data = []
      setListData(list)
      if (!data.headerItem.value) { // 没有值的时候 设置一个默认的
        data.headerItem.value = list[0].id
      }
      data.dataList = []
      getListData()
    }
    // 递归设置栏目
    const setListData = (_data) => {
      for (var i = 0; i < _data.length; i++) {
        var id = _data[i].id // id
        var value = _data[i].value // name
        var children = _data[i].children || []
        data.headerItem.data.push({ label: value, value: id })
        if (children.length !== 0) {
          setListData(children)
        }
      }
    }
    // 列表请求
    const getListData = async () => {
      const res = await $api.partyBuilding.partyList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        type: data.headerItem.value ? data.headerItem.value : '',
        module: 'information_party',
        state: 1
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.id || ''
        item.title = item.name || '' // 标题
        item.content = (item.content || '')
        item.source = item.org || item.createName || '' // 部门
        item.isRead = item.read ? '' : ''
        item.time = item.publicDate || '' // 时间
        item.isTop = item.isTop || '0' // 置顶 1是0否
      })
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 切换
    const tabClick = (_name, _item) => {
      data.headerItem.value = _name
      onRefresh()
    }
    const openDetails = (row) => {
      router.push({ name: 'partyBuildingDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), dayjs, onRefresh, onLoad, openDetails, $general, tabClick, dropdownChange }
  }
}
</script>
<style lang="less">
.partyBuildingList {
  width: 100%;
  min-height: 100vh;
  background: #f8f8f8;
  .top_bgImg {
    width: 100%;
    height: 220px;
    background-size: 100% 100%;
    position: relative;
    .yearTab {
      background: #fff;
      position: absolute;
      border-radius: 10px;
      width: 20%;
      right: 0;
      top: 80%;
      padding: 6px 10px;
    }
  }
  .vue_newslist_time {
    font-size: 14px;
  }
  .vue_newslist_source {
    font-size: 14px;
  }
}
</style>
