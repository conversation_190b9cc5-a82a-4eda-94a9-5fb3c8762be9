import { HTTP } from '../http.js'
class thinkTanks extends HTTP {
  // 智库列表
  thinkTanksList (params) {
    return this.request({ url: '/wholeuser/list?', data: params })
  }

  // 智库详情
  thinkTanksDetails (params) {
    return this.request({ url: `/wholeuser/info/${params}` })
  }

  // 智库详情(履职情况)
  findDutyDetail (params) {
    return this.request({ url: '/dutyconfig/findDutyDetail?', data: params })
  }

  // 履职成功库列表
  assemblyList (params) {
    return this.request({ url: '/assembly/list?', data: params })
  }

  // 履职成功库详情
  assemblyInfo (params) {
    return this.request({ url: `/assembly/info/${params}` })
  }
}
export {
  thinkTanks
}
