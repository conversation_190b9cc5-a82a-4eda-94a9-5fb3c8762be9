import { HTTP } from '../http.js'
class Networkpolitics extends HTTP {
  // 通过会议类型查询不同的文件
  list (params) {
    return this.request({ url: '/survey/findSurveyList', data: params })
  }

  // 通过会议类型查询不同的文件
  info (params) {
    return this.request({ url: `/survey/info/${params}` })
  }

  Advicelist (params) {
    return this.request({ url: '/surveytownhalladvice/appList', data: params })
  }

  adviceAdd (params) {
    return this.request({ url: '/surveytownhalladvice/add', data: params })
  }

  fabulous (url, params) {
    return this.request({ url: url, data: params })
  }

  delsComment (params) {
    return this.request({ url: '/surveytownhalladvice/dels', data: params })
  }

  getRedIds (params) {
    return this.request({ url: '/redPoint/getRedIds', data: params })
  }

  browseSave (params) {
    return this.request({ url: '/browse/save', data: params })
  }

  redPointSign (params) {
    return this.request({ url: '/redPoint/redPointSign', data: params })
  }

  getRedPointNumByModule (params) {
    return this.request({ url: '/redPoint/getRedPointNumByModule', data: params })
  }
}
export {
  Networkpolitics
}
