const recommend = () => import('@/views/bookAcademy/recommend/recommend')
const bookDetail = () => import('@/views/bookAcademy/bookDetail/bookDetail')
const bookReadUserList = () => import('@/views/bookAcademy/bookDetail/readUserList')
const bookReader = () => import('@/views/bookAcademy/bookReader/bookReader')
const bookDesk = () => import('@/views/bookAcademy/bookDesk/bookDesk')
const books = () => import('@/views/bookAcademy/bookDesk/books')
const notes = () => import('@/views/bookAcademy/bookDesk/notes')
const notesEdit = () => import('@/views/bookAcademy/bookDesk/notesEdit')
const notesDetails = () => import('@/views/bookAcademy/bookDesk/notesDetails')
const searchBook = () => import('@/views/bookAcademy/searchBook/searchBook')
const library = () => import('@/views/bookAcademy/library/library')
const libraryDetails = () => import('@/views/bookAcademy/libraryDetails/libraryDetails')
const bookNotice = () => import('@/views/bookAcademy/bookNotice/bookNotice')
const bookNoticeDetails = () => import('@/views/bookAcademy/bookNotice/bookNoticeDetails')
const commissarRecom = () => import('@/views/zxBookmain/commissarRecom/commissarRecom')

const bookAcademy = [{
  path: '/recommend',
  name: 'recommend',
  component: recommend,
  meta: {
    title: '推荐',
    keepAlive: true
  }
}, {
  path: '/bookDetail',
  name: 'bookDetail',
  component: bookDetail,
  meta: {
    title: '书籍详情',
    keepAlive: true
  }
}, {
  path: '/bookReadUserList',
  name: 'bookReadUserList',
  component: bookReadUserList,
  meta: {
    title: '阅读详情',
    keepAlive: true
  }
}, {
  path: '/bookReader',
  name: 'bookReader',
  component: bookReader,
  meta: {
    title: '书籍详情',
    keepAlive: true
  }
}, {
  path: '/bookDesk',
  name: 'bookDesk',
  component: bookDesk,
  meta: {
    title: '书桌',
    keepAlive: true
  }
}, {
  path: '/books',
  name: 'books',
  component: books,
  meta: {
    title: '图书',
    keepAlive: true
  }
}, {
  path: '/notesEdit',
  name: 'notesEdit',
  component: notesEdit,
  meta: {
    title: '编辑笔记',
    keepAlive: true
  }
}, {
  path: '/notes',
  name: 'notes',
  component: notes,
  meta: {
    title: '笔记',
    keepAlive: true
  }
}, {
  path: '/notesDetails',
  name: 'notesDetails',
  component: notesDetails,
  meta: {
    title: '笔记详情',
    keepAlive: true
  }
}, {
  path: '/searchBook',
  name: 'searchBook',
  component: searchBook,
  meta: {
    title: '搜索',
    keepAlive: true
  }
}, {
  path: '/library',
  name: 'library',
  component: library,
  meta: {
    title: '书库',
    keepAlive: true
  }
}, {
  path: '/libraryDetails',
  name: 'libraryDetails',
  component: libraryDetails,
  meta: {
    title: '书城',
    keepAlive: true
  }
}, {
  path: '/bookNotice',
  name: 'bookNotice',
  component: bookNotice,
  meta: {
    title: '通知公告',
    keepAlive: true
  }
}, {
  path: '/bookNoticeDetails',
  name: 'bookNoticeDetails',
  component: bookNoticeDetails,
  meta: {
    title: '通知公告详情',
    keepAlive: true
  }
}, {
  path: '/commissarRecom',
  name: 'commissarRecom',
  component: commissarRecom,
  meta: {
    title: '委员荐书',
    keepAlive: true
  }
}]
export default bookAcademy
