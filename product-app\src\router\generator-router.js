/**
 * 动态生成菜单
 * @param token
 * @returns {Promise}
 */
import router, { routes } from '@/router'
// import { routes } from '@/router'

export const addRoute = (item) => {
  routes.forEach(element => {
    // eslint-disable-next-line eqeqeq
    if (element.name == item.infoUrl2) {
      const newRouter = {}
      newRouter.path = item.infoUrl2 + item.type
      newRouter.name = item.infoUrl2 + item.type
      newRouter.meta = { title: item.name }
      newRouter.component = element.component
      router.addRoute('home', newRouter)
    }
  })
}

export const generatorDynamicRouter = (asyncMenus, n) => {
  try {
    const tabs = []
    asyncMenus.forEach(item => {
      // if (item.name === '首页') {
      //   // 设默认tab页
      //   sessionStorage.setItem('historyIndex', item.id)
      // }
      if (item.infoUrl2 && item.infoUrl2 !== '#') {
        item.infoUrl = item.infoUrl2 + item.type + '?title=' + item.name
        if (item.remarks) {
          item.infoUrl = item.infoUrl + '&' + item.remarks || ''
        }
        tabs.push({ ...item })
        addRoute(item)
      }
    })

    // const removeRoute = router.addRoute([])

    return Promise.resolve({
      tabs
      // routes: layout.children,
    })
  } catch (error) {
    console.error('生成路由时出错', error)
    // return Promise.reject(`生成路由时出错: ${error}`)
  }
}
