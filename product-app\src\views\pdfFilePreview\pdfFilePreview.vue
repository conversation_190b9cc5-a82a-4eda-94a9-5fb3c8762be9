<template>
  <div class="pdfFilePreview">
    <van-sticky>
      <van-nav-bar v-if="hasApi" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <template v-if="pdfUrl">
      <iframe :src="pdfUrl" style="width:100%;height: calc(100% - 35px);border:none;"></iframe>
    </template>
    <template v-else>
      <div style="color:#fff;text-align:center;padding-top:40vh;">未获取到PDF地址</div>
    </template>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { computed, ref, onMounted } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'pdfFilePreview',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const pdfUrl = computed(() => route.query.url)
    const hasApi = ref(true)
    const title = ref(route.query.title)
    if (title.value) {
      document.title = title.value
    }

    onMounted(() => {
      if (typeof (api) === 'undefined') {
        hasApi.value = false
      } else {
        hasApi.value = true
      }
    })
    const onClickLeft = () => {
      if (typeof (api) === 'undefined') return history.back()
      // eslint-disable-next-line no-undef
      api.closeWin()
    }
    return { pdfUrl, onClickLeft, title, hasApi }
  }
}
</script>
<style lang="less">
.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}

.pdfFilePreview {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}
</style>
