<template>
  <div class="MajorProjectsDetails">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <!-- 顶部背景图和标题 -->
    <div class="top-box">
      <div class="top-bg-box">
        <div class="top-bg-img" :class="{ 'no-img': !(details.imageListVo && details.imageListVo.length) }"
          :style="topBgStyle">
          <div class="top-title">{{ details.title }}</div>
        </div>
      </div>
    </div>
    <!-- 内容卡片区 -->
    <div class="card-box">
      <div class="row-info">
        <div class="row-item">
          <img src="../../../assets/img/icon_work_time.png" alt="" class="row-item-icon">
          <span class="row-item-time">{{ formatDate(details.publishDate) }}</span>
        </div>
        <div class="row-item" v-if="details.projectWay">
          <span class="tag">{{ details.projectWay }}</span>
        </div>
      </div>
      <div class="row-info">
        <div class="row-item" v-if="details.projectAreaName">
          <img src="../../../assets/img/icon_work_location.png" alt="" class="row-item-icon">
          <span class="row-item-time">{{ details.projectAreaName }}</span>
        </div>
      </div>
      <!-- 领导活动 -->
      <div class="section-header" v-if="details.projectAppeal">
        <img class="section-icon" src="../../../assets/img/icon_work_suggest.png" alt="">
        <span class="section-title">领导活动：</span>
      </div>
      <div class="section-content" @click="setImgBigger" v-html="details.projectAppeal"></div>
      <!-- 项目情况 -->
      <div class="section-header" style="margin-top: 20px;" v-if="details.solve">
        <img class="section-icon" src="../../../assets/img/icon_work_resolve.png" alt="">
        <span class="section-title">项目情况：</span>
      </div>
      <div class="section-content" @click="setImgBigger" v-html="details.solve"></div>
    </div>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed } from 'vue'
export default {
  name: 'MajorProjectsDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {}
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      getInfo()
    })
    // 详情请求
    const getInfo = async () => {
      const res = await $api.ImportantWork.info(data.id)
      var { data: details } = res
      data.details = details
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.section-content img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview({ images: img, startPosition: nowIndex, closeable: true })
      }
    }
    // 日期格式化
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const d = new Date(dateStr)
      const y = d.getFullYear()
      const m = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      return `${y}年${m}月${day}日`
    }
    // 顶部背景样式
    const topBgStyle = computed(() => {
      if (data.details.imageListVo && data.details.imageListVo.length) {
        return {
          backgroundImage: `url(${data.details.imageListVo[0].filePath})`
        }
      } else {
        return {}
      }
    })
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), $general, setImgBigger, formatDate, topBgStyle, onClickLeft }
  }
}
</script>
<style lang="less">
.MajorProjectsDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;
  padding: 0; // 顶部去掉内边距

  .top-box {
    min-height: 212px;
    background-color: rgba(205, 205, 205, 0.296);

    .top-bg-box {
      width: 100%;
      min-width: 100%;
      position: relative;
      margin-bottom: 16px;

      .top-bg-img {
        width: 100vw;
        min-width: 100%;
        height: 212px;
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: flex-end;

        &.no-img {
          background: linear-gradient(135deg, #4a7bdc 0%, #274b8e 100%);
        }

        .top-title {
          width: 100%;
          font-weight: bold;
          font-size: 21px;
          color: #FFFFFF;
          margin: 50px 20px;
          font-family: Source Han Serif SC, Source Han Serif SC;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          line-height: 1.3;
        }
      }
    }
  }

  .card-box {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.06);
    margin: -32px 0 0 0;
    padding: 22px 15px 24px 15px;
    position: relative;
    z-index: 2;

    .row-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      gap: 16px;

      .row-item {
        display: flex;
        align-items: center;
        font-size: 15px;
        color: #333;
        gap: 4px;

        .row-item-icon {
          width: 18px;
          height: 18px;
          margin-top: 1px;
        }

        .row-item-time {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }

        .tag {
          background: rgba(0, 122, 255, 0.12);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #007AFF;
          color: #3478f6;
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 500;
          font-size: 12px;
          color: #007AFF;
          padding: 2px 6px;
        }
      }
    }

    .section-header {
      display: flex;
      align-items: center;

      .section-icon {
        width: 18px;
        height: 18px;
        margin-top: 1px;
      }

      .section-title {
        font-family: Source Han Serif SC, Source Han Serif SC;
        font-weight: 600;
        font-size: 14px;
        color: #999999;
        margin-left: 5px;
      }
    }

    .section-content {
      font-family: Source Han Serif SC, Source Han Serif SC;
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      margin: 10px 0 0 30px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
