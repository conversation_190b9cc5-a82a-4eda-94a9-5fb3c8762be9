<template>
  <div :id="id" class="chart"></div>
</template>

<script>
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref, nextTick } from 'vue'

export default {
  name: 'barChart',
  props: {
    id: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    color: {
      type: [String, Array],
      default: () => ['#6ABFFF']
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const getXData = () => props.data.map(item => item.name)
    const getYData = () => props.data.map(item => Number(item.value))

    // 初始化图表
    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: '{b}: {c}',
            confine: true,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'transparent',
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            extraCssText: 'border-radius: 4px; padding: 8px 12px;'
          },
          grid: {
            left: 0,
            right: 0,
            top: 10,
            bottom: 0,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: getXData(),
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              color: '#666',
              fontSize: 12,
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#F0F0F0'
              }
            },
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              color: '#BFBFBF',
              fontSize: 12
            }
          },
          series: [
            {
              type: 'bar',
              data: getYData(),
              barWidth: 16,
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: props.color[0] },
                    { offset: 1, color: props.color[1] }
                  ]
                }
              }
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return { chartId }
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
