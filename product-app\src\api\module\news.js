import { HTTP } from '../http.js'
class news extends HTTP {
  // 获取首页专题轮播图
  getSpecialsubjectLists (params) {
    return this.request({
      url: '/specialsubjectinfo/details',
      data: params
    })
  }

  // 获取资讯栏目
  getNewsTree (params) {
    return this.request({
      url: '/zyinfostructure/tree',
      data: params
    })
  }

  // 获取顶部轮播图
  getTopList (params) {
    return this.request({
      url: '/zyinfodetail/top/list',
      data: params
    })
  }

  // 获取顶部轮播图
  zyinfodetaillist (params) {
    return this.request({
      url: '/zyinfodetail/list',
      data: params
    })
  }

  // 获取资讯列表
  getAppList (params) {
    return this.request({
      url: '/zyinfodetail/appList',
      data: params
    })
  }

  // 获取资讯详情
  getNewsDetail (params) {
    return this.request({
      url: '/zyinfodetail/info/' + params.id,
      data: params
    })
  }

  // 获取专题详情列表
  getInforelationList (params) {
    return this.request({
      url: '/inforelation/list',
      data: params
    })
  }

  // 获取专题详情
  getSpecialsubjectinfo (params) {
    return this.request({
      url: '/specialsubjectinfo/info/' + params.id,
      data: params
    })
  }

  // 获取专题列表2
  getSpecialsubjectRelateinfoAndColumnListVos (params) {
    return this.request({
      url: '/zySpecialsubjectRelateinfo/getSpecialsubjectRelateinfoAndColumnListVos',
      data: params
    })
  }

  // 获取专题详情栏目
  getSpecialsubjectColumnList (params) {
    return this.request({
      url: '/zySpecialsubjectColumn/list',
      data: params
    })
  }

  // 获取专题详情列表
  getSpecialsubjectRelateinfoList (params) {
    return this.request({
      url: '/zySpecialsubjectRelateinfo/list',
      data: params
    })
  }

  // 获取专题资讯详情
  getSpecialsubjectnewsInfo (params) {
    return this.request({
      url: '/specialsubjectnews/info/' + params.id,
      data: params
    })
  }

  // 获取专题列表
  getSpecialsubjectList (params) {
    return this.request({
      url: '/specialsubjectinfo/list',
      data: params
    })
  }

  // 获取数字文史栏目
  getlhinfostructuretTree (params) {
    return this.request({
      url: '/lhinfostructure/tree',
      data: params
    })
  }

  // 获取数字文史列表
  getlhinfostructuretList (params) {
    return this.request({
      url: '/lhinfodetail/list',
      data: params
    })
  }

  // 获取数字文史统计人数
  getqdliteraryhistoryInfo (params) {
    return this.request({
      url: '/qdliteraryhistory/info/' + params
    })
  }

  // 获取滚动报道
  getScrollingReportList (params) {
    return this.request({
      url: '/zyinforeportpic/scrolling/report/list',
      data: params
    })
  }
}
export {
  news
}
