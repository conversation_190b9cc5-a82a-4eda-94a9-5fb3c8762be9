<template>
  <div class="proposalNew">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.title"
                   required
                   name="提案标题"
                   maxlength="30"
                   show-word-limit
                   label-width="4.8em"
                   label="提案标题"
                   placeholder="请输入提案标题"
                   :rules="rules.title" />
        <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   label="是否联名">
          <template #input>
            <van-switch v-model="form.ifJointly"
                        size="20" />
          </template>
        </van-field>
        <van-field is-link
                   label-width="4.8em"
                   v-if="form.ifJointly"
                   name="user"
                   label="联名人"
                   @click="userShow = !userShow">
          <template #input>
            <div class="userBox">
              <div class="userItem"
                   v-for="item in userData"
                   :key="item.userId">
                <div class="userImg"><img :src="item.headImg"
                       alt=""></div>
                <div class="userName">{{item.name}}</div>
              </div>
            </div>
          </template>
        </van-field>
        <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   label="是否调研">
          <template #input>
            <van-switch v-model="form.ifInvestigate"
                        size="20" />
          </template>
        </van-field>
        <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   label="是否公开">
          <template #input>
            <van-switch v-model="form.ifAdvicePublic"
                        size="20" />
          </template>
        </van-field>
        <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   label="是否涉密">
          <template #input>
            <van-switch v-model="form.ifSecret"
                        size="20" />
          </template>
        </van-field>
        <!-- <van-field is-link
                   label-width="4.8em"
                   name="picker"
                   label="主题词"
                   @click="typeShow = !typeShow">
          <template #input>
            <div class="newDivBox">
              <div class="newDiv">农村</div>
              <div class="newDiv">经济</div>
              <div class="newDiv">发展经济</div>
              <div class="newDiv">发展农村</div>
              <div class="newDiv">发展长沙</div>
              <div class="newDiv">发展乡镇</div>
            </div>
          </template>
        </van-field> -->
        <van-field is-link
                   label-width="4.8em"
                   name="unit"
                   label="希望送交 办理单位"
                   @click="unitShow = !unitShow">
          <template #input>
            <div class="newDivBox">
              <div class="newDiv"
                   v-for="item in unitData"
                   :key="item.id">{{item.value}}</div>
            </div>
          </template>
        </van-field>
        <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="正文"
                   rows="6"
                   maxlength="2000"
                   show-word-limit
                   type="textarea"
                   placeholder="请输入正文"
                   :rules="rules.content" />
        <van-field v-model="form.contactUserName"
                   name="提案联系人姓名"
                   label-width="6.6em"
                   label="提案联系人姓名"
                   placeholder="请输入提案联系人姓名" />
        <van-field v-model="form.contactUserAddress"
                   name="提案联系人电话"
                   label-width="6.6em"
                   label="提案联系人电话"
                   placeholder="请输入提案联系人电话" />
        <van-field v-model="form.contactUserPhone"
                   name="提案联系人地址"
                   label-width="6.6em"
                   label="提案联系人地址"
                   placeholder="请输入提案联系人地址" />
      </van-cell-group>
      <div class="newButton">
        <van-button type="primary"
                    @click="logo = false"
                    native-type="submit">存为草稿</van-button>
        <van-button type="primary"
                    @click="logo = true"
                    native-type="submit">提交</van-button>
      </div>
    </van-form>
    <van-popup v-model:show="userShow"
               position="bottom"
               :style="{height:'80%'}">
      <selectUser ref="user"
                  :data="defaultUser"></selectUser>
    </van-popup>
    <van-popup v-model:show="unitShow"
               position="bottom"
               :style="{height:'80%'}">
      <chooseHandleUnit ref="unit"
                        :data="defaultUnit"></chooseHandleUnit>
    </van-popup>
    <van-popup v-model:show="typeShow"
               position="bottom"
               :style="{height:'80%'}">
      <subjectTerm ref="type"
                   :data="defaultType"></subjectTerm>
    </van-popup>
  </div>
</template>
<script>
import { Toast } from 'vant'
import { useRouter } from 'vue-router'
import { inject, reactive, ref, toRefs, watch } from 'vue'
import selectUser from './components/selectUser'
import chooseHandleUnit from './components/chooseHandleUnit'
import subjectTerm from './components/subjectTerm'
export default {
  name: 'proposalNew',
  components: {
    selectUser,
    chooseHandleUnit,
    subjectTerm
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      logo: true,
      form: {
        title: '',
        ifJointly: false, // 是否联名提案
        ifInvestigate: false, // 是否调研
        ifAdvicePublic: false, // 是否公开
        ifSecret: false, // 是否涉密
        content: '',
        contactUserName: '', // 联系人姓名
        contactUserAddress: '', // 联系人地址
        contactUserPhone: '' // 联系人电话
      },
      rules: {
        title: [{ required: true, message: '请输入提案标题' }],
        content: [{ required: true, message: '请输入正文' }]
      },
      userData: [],
      userShow: false,
      defaultUser: [],
      unitData: [],
      unitShow: false,
      defaultUnit: [],
      typeData: [],
      typeShow: false,
      defaultType: []
    })
    const user = ref(null)
    const unit = ref(null)
    const type = ref(null)
    watch(() => data.userShow, () => {
      if (!data.userShow) {
        data.userData = user.value.selectedUser
      }
    })
    watch(() => data.unitShow, () => {
      if (!data.unitShow) {
        data.unitData = unit.value.selectedUnit
      }
    })
    watch(() => data.typeShow, () => {
      if (!data.typeShow) {
        data.typeData = type.value.defaultType
      }
    })
    const onSubmit = () => {
      if (data.logo) {
        generalAdd()
      } else {
        generalAdd(1)
      }
    }
    const generalAdd = async (type) => {
      var userData = []
      var hopeGroupIds = []
      var hopeGroupNames = []
      data.userData.forEach(item => {
        userData.push(item.userId)
      })
      data.unitData.forEach(item => {
        hopeGroupIds.push(item.id)
        hopeGroupNames.push(item.value)
      })
      var datas = {
        title: data.form.title,
        content: data.form.content,
        draftsFlag: type,
        sourceType: '2',
        mainSubmitUser: data.user.id,
        ifJointly: data.form.ifJointly ? '1' : '0',
        jointSubmitUserIds: userData.join(','),
        ifInvestigate: data.form.ifInvestigate ? '1' : '0', // 是否调研
        ifAdvicePublic: data.form.ifAdvicePublic ? '1' : '0', // 是否公开
        ifSecret: data.form.ifSecret ? '1' : '0', // 是否涉密
        contactUserName: data.form.contactUserName, // 联系人姓名
        contactUserAddress: data.form.contactUserAddress, // 联系人地址
        contactUserPhone: data.form.contactUserPhone, // 联系人电话
        hopeGroupIds: hopeGroupIds.join(','),
        hopeGroupNames: hopeGroupNames.join(',')
      }
      const res = await $api.general.general('/proposal/add', datas)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    return { ...toRefs(data), user, unit, type, onSubmit }
  }
}
</script>
<style lang="less">
.proposalNew {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  .van-form {
    width: 100%;
    .newContent {
      flex-wrap: wrap;
      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }
      .van-field__body {
        background-color: #e8e8e8;
        padding: 6px 12px;
      }
    }
    .van-cell-group {
      width: 100%;
      margin: 0;
      border-radius: 0;
    }
    .van-cell__title {
      label {
        font-size: 15px;
        font-weight: 600;
        color: #333333;
      }
    }
    .van-field__control {
      font-size: 14px;
    }
    .van-switch__node {
      font-size: 20px;
    }
    .newDivBox {
      .newDiv {
        display: inline-block;
        height: 21px;
        background: #dcebff;
        opacity: 1;
        border-radius: 2px;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 21px;
        color: #3088fe;
        padding: 0 6px;
        margin-right: 6px;
      }
    }

    .userBox {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      max-height: 110px;
      overflow-y: scroll;
      .userItem {
        display: flex;
        align-items: center;
        padding: 2px 0;
        padding-right: 6px;
        margin-right: 12px;
        margin-bottom: 6px;
        .userImg {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          overflow: hidden;
          img {
            height: 100%;
            margin: auto;
            display: block;
          }
        }
        .userName {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 17px;
          color: #666666;
          padding-left: 6px;
        }
      }
    }
    .newButton {
      display: flex;
      justify-content: space-around;
      padding: 36px 18px;
      padding-bottom: 88px;
      .van-button {
        width: 128px;
        height: 36px;
      }
    }
  }
}
</style>
