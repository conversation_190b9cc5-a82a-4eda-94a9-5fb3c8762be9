<template>
  <div class="proposalSatisfactionDetails">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="satisfactionDetails"
           v-if="details.lastEvaluate">
        <div class="satisfactionName">办理单位</div>
        <div class="satisfactionText">
          <span v-for="(item, index) in details.flowTransactList"
                :key="index">{{item.transactTypName}}:{{item.groupName}}</span>
        </div>
        <div class="satisfactionName">电话/电邮沟通</div>
        <div class="satisfactionText">{{details.lastEvaluate.contactPhone == 'empty'?'':details.lastEvaluate.contactPhone}}</div>
        <div class="satisfactionName">信函沟通</div>
        <div class="satisfactionText">{{details.lastEvaluate.contactLetter == 'empty'?'':details.lastEvaluate.contactLetter}}</div>
        <div class="satisfactionName">当面沟通</div>
        <div class="satisfactionText">{{details.lastEvaluate.contactFace == 'empty'?'':details.lastEvaluate.contactFace}}</div>
        <div class="satisfactionName">未联系</div>
        <div class="satisfactionText">{{details.lastEvaluate.contactNone == 'empty'?'':details.lastEvaluate.contactNone}}</div>
        <div class="satisfactionName">办理态度</div>
        <div class="satisfactionText">{{details.lastEvaluate.attitudeView == 'empty'?'':details.lastEvaluate.attitudeView}}</div>
        <div class="satisfactionName">办理结果</div>
        <div class="satisfactionText">{{details.lastEvaluate.resultView == 'empty'?'':details.lastEvaluate.resultView}}</div>
        <div class="satisfactionName">对提案工作的建议</div>
        <div class="satisfactionText">{{details.lastEvaluate.advice == 'empty'?'':details.lastEvaluate.advice}}</div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'proposalSatisfactionDetails',
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id,
      details: {},
      refreshing: false
    })
    onMounted(() => {
      flowEvaluateDetail()
    })
    const onRefresh = () => {
      setTimeout(() => {
        flowEvaluateDetail()
      }, 520)
    }
    // 列表请求
    const flowEvaluateDetail = async () => {
      const res = await $api.proposal.flowEvaluateDetail({
        dataId: data.id
      })
      var { data: details } = res
      console.log(details)
      details.flowTransactList.forEach(item => {
        if (item.transactType === '1') {
          item.transactTypName = '主办'
        }
        if (item.transactType === '2') {
          item.transactTypName = '协办'
        }
        if (item.transactType === '3') {
          item.transactTypName = '分办'
        }
      })
      data.details = details
      data.refreshing = false
    }
    return { ...toRefs(data), onRefresh }
  }
}
</script>
<style lang="less">
.proposalSatisfactionDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .satisfactionDetails {
    padding: 16px;
    .satisfactionName {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
      padding-bottom: 9px;
    }
    .satisfactionText {
      font-size: 15px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 21px;
      color: #666666;
      padding-bottom: 9px;
      span + span {
        margin-left: 12px;
      }
    }
  }
}
</style>
