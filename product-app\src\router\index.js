import {
  createRouter,
  createWebHashHistory
} from 'vue-router'
import store from '../store'
import api from '../api'
import general from '../assets/js/general'

const files = require.context('./module', false, /\.js$/)
var pages = []
files.keys().forEach(key => {
  pages = pages.concat(files(key).default)
})

export const routes = [{
  path: '/login',
  name: 'login',
  component: () => import('@/views/main/login.vue')
},
{
  path: '/',
  name: 'home',
  component: () => import('@/views/main/home.vue'),
  children: [
    {
      path: '/module',
      name: 'module',
      component: () => import('@/views/module/module.vue')
    },
    {
      path: '/Networkpolitics',
      name: 'Networkpolitics',
      component: () => import('@/views/Networkpolitics/NetworkpoliticsList.vue')
    },
    {
      path: '/zxBookmain',
      name: 'zxBookmain',
      component: () => import('@/views/zxBookmain/zxBookmain.vue')
    },
    {
      path: '/msgListHome',
      name: 'msgListHome',
      component: () => import('@/views/rongCloud/msgList/msgList.vue')
    },
    {
      path: '/bookDeskHome',
      name: 'bookDeskHome',
      component: () => import('@/views/bookAcademy/bookDesk/books.vue')
    },
    {
      path: '/homePageZX',
      name: 'homePageZX',
      component: () => import('@/views/homePage/homePageZX.vue')
    },
    {
      path: '/myUser',
      name: 'myUser',
      component: () => import('@/views/myUser/myUser.vue')
    }
  ]
},
...pages
]
const router = createRouter({
  history: createWebHashHistory(),
  routes
})
router.beforeEach(async (to, from, next) => {
  console.log('青岛政协进来了。')
  document.title = to.query.title || '青岛政协'
  const token = sessionStorage.getItem('token')
  const userid = to.query.userId
  const pcToken = to.query.token
  const areaId = to.query.areaId || ''
  // const userToken = to.query.userToken
  const userToken = ''// 先用空的，保证请求不到接口还可以跳转到登录页手机动登录
  if (areaId) {
    sessionStorage.setItem('areaId', areaId)
  }
  if (userToken) {
    sessionStorage.setItem('otherToken', 'Bearer ' + userToken)
    getPersonal(to, from, next)
  } else if (pcToken) {
    sessionStorage.setItem('token', JSON.stringify(pcToken.includes('Bearer') ? pcToken : ('Bearer ' + pcToken)))
    sessionStorage.setItem('Sys_token', JSON.stringify(pcToken.includes('Bearer') ? pcToken : ('Bearer ' + pcToken)))
    changearea(to, from, next)
    next()
  } else if (userid) {
    getOtherToken(userid, to, from, next)
  } else {
    if (token) {
      if (to.name === 'login') {
        next({
          name: 'home'
        })
      } else {
        const tabbarList = store.getters.tabbarList
        const isHasRoute = hasRoute.isPath(to.path)
        if (!tabbarList.length) {
          await store.dispatch('getAppList')
          if (!isHasRoute) {
            next({ ...to, replace: true })
          } else {
            next()
          }
        } else {
          next()
        }
        // const ret = await api.Networkpolitics.getRedPointNumByModule({ module: 'app' })
        // const list = ret ? ret.data || [] : []
        // if (list && list.length !== 0) {
        //   list.forEach(function (_eItem, _eIndex, _eArr) {
        //     switch (_eItem.code) {
        //       case 'survey':
        //         store.commit('setOnlinePoliticalDiscussionRedDotsNumber', _eItem.count)
        //         break
        //       case 'meet':
        //         store.commit('setModuleRedDotsNumber1', _eItem.count || 0)
        //         break
        //       case 'activity':
        //         store.commit('setModuleRedDotsNumber2', _eItem.count || 0)
        //         break
        //       case 'memberead':
        //         store.commit('setModuleRedDotsNumber3', _eItem.count || 0)
        //         break
        //       case 'social_check_red_point':
        //         store.commit('setModuleRedDotsNumber4', _eItem.count || 0)
        //         break
        //       case 'vote':
        //         store.commit('setModuleRedDotsNumber5', _eItem.count || 0)
        //         break
        //       case 'questionnaire':
        //         store.commit('setModuleRedDotsNumber6', _eItem.count || 0)
        //         break
        //       default:
        //         break
        //     }
        //   })
        // }
      }
    } else {
      console.log(11111)
      // if (to.name !== 'login') {
      //   next({
      //     name: 'login'
      //   })
      // } else {
      //   next()
      // }
      if (to.name !== 'login') {
        // if (to.name === 'RandomClapping' || to.name === 'userFeedbackAdd') {
        //   next()
        // } else {
        //   next({
        //     name: 'login'
        //   })
        // }
        const mobile = location.search.slice(8, 19)
        if (mobile) {
          if (!isNaN(mobile)) {
            try {
              const ret = await api.general.authenticationPcLogin({ userMobile: mobile })
              const shandongToken = ret.data.data
              sessionStorage.setItem('token', JSON.stringify(shandongToken))
              await changearea(to, from, next)
              next()
            } catch (error) {
              console.log('error==>', error)
              next({
                name: 'login'
              })
            }
          } else {
            next({
              name: 'login'
            })
          }
        } else {
          next({
            name: 'login'
          })
        }
      } else {
        next()
      }
    }
  }
})

const hasRoute = (k, val) => {
  const routes = router.getRoutes()
  return routes.findIndex(item => item[k] === val) > -1
}
['name', 'path'].forEach(k => {
  const name = 'is' + k.charAt(0).toLocaleUpperCase() + k.substring(1)
  hasRoute[name] = (val) => {
    return hasRoute(k, val)
  }
})

// 新增
export const handleAdd = (item) => {
  router.options.routes.forEach(element => {
    // eslint-disable-next-line eqeqeq
    if (element.name == item.infoUrl2) {
      const newRouter = {}
      newRouter.path = item.infoUrl2 + item.type
      newRouter.name = item.infoUrl2 + item.type
      newRouter.meta = { title: item.name }
      newRouter.component = element.component
      router.addRoute('home', newRouter)
    }
  })
}

const getPersonal = async (to, from, next) => {
  const {
    custom: user
  } = await api.general.getPersonal({
    params: {}
  })
  const useridN = user.userguid
  getOtherToken(useridN, to, from, next)
}
const getOtherToken = async (useridN, to, from, next) => {
  sessionStorage.setItem('useridN', useridN)
  sessionStorage.setItem('token', '')
  const { data: token } = await api.general.calogin({ certId: useridN })
  sessionStorage.setItem('token', JSON.stringify(token))
  sessionStorage.setItem('Sys_token', JSON.stringify(token))
  changearea(to, from, next)
}

// 切换系统登录
const changearea = async (to, from, next) => {
  const res = await api.general.changearea()
  var {
    data: {
      token,
      user,
      menus,
      areas
    }
  } = res
  sessionStorage.setItem('menus', JSON.stringify(menus))
  sessionStorage.setItem('token', JSON.stringify(token))
  sessionStorage.setItem('Sys_token', JSON.stringify(token))
  sessionStorage.setItem('user', JSON.stringify(user))
  sessionStorage.setItem('areas', JSON.stringify(areas))
  sessionStorage.setItem('areaId', user.areaId)

  areas.forEach(item => {
    if (item.id === user.areaId) {
      sessionStorage.setItem('areaName', JSON.stringify(item.value))
    }
  })
  nologin(to, from, next)
  findRolesByUserId()
  // appList()
  await store.dispatch('getAppList')
}
// 获取用户角色
const findRolesByUserId = async () => {
  // const { data: roleList } = await api.general.findRolesByUserId()
  // sessionStorage.setItem('roleList', roleList)
}
// 切换系统登录
const nologin = async (to, from, next) => {
  const res = await api.general.nologin({ codes: 'logo,AcademyAppId,AcademyRentId,rongCloudIdPrefix,appGrayscale,setWaterMark,fileStoreUrl,fileVisitUrl,BigDataUser,qrCode,appHomeBg,appTheme,memberRequiredId,appMyBg' })
  var { data: { logo, AcademyAppId, AcademyRentId, rongCloudIdPrefix, appGrayscale, setWaterMark, fileStoreUrl, fileVisitUrl, BigDataUser, qrCode, appHomeBg, appTheme, memberRequiredId, appMyBg } } = res
  sessionStorage.setItem('qrCode', qrCode)
  sessionStorage.setItem('tomcatAddress', 'https://www.cszysoft.com:9091/')
  sessionStorage.setItem('logo', logo)
  sessionStorage.setItem('AcademyAppId', AcademyAppId)
  sessionStorage.setItem('AcademyRentId', AcademyRentId)
  sessionStorage.setItem('rongCloudIdPrefix', rongCloudIdPrefix)
  sessionStorage.setItem('appGrayscale', appGrayscale)
  sessionStorage.setItem('setWaterMark', setWaterMark)
  sessionStorage.setItem('fileStoreUrl', fileStoreUrl)
  sessionStorage.setItem('fileVisitUrl', fileVisitUrl)
  sessionStorage.setItem('BigDataUser', BigDataUser)
  sessionStorage.setItem('appHomeBg', appHomeBg)
  sessionStorage.setItem('appTheme', appTheme)
  sessionStorage.setItem('memberRequiredId', memberRequiredId)
  sessionStorage.setItem('appMyBg', appMyBg)
  general.setWaterMark()
  general.appGrayscale()
}

export async function setupRouter (app) {
  app.use(router)
  await router.isReady()
}

export default router
