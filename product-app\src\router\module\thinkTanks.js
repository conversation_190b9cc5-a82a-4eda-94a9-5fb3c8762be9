const thinkTanksList = () => import('@/views/thinkTanks/thinkTanksList')
const thinkTanksDetails = () => import('@/views/thinkTanks/thinkTanksDetails')
const performanceDutiesList = () => import('@/views/thinkTanks/performanceDutiesList')
const performanceDutiesDetails = () => import('@/views/thinkTanks/performanceDutiesDetails')
const resultDetails = () => import('@/views/thinkTanks/resultDetails')

const thinkTanks = [{
  path: '/thinkTanksList',
  name: 'thinkTanksList',
  component: thinkTanksList,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/thinkTanksDetails',
  name: 'thinkTanksDetails',
  component: thinkTanksDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/performanceDutiesList',
  name: 'performanceDutiesList',
  component: performanceDutiesList,
  meta: {
    title: '履职情况列表',
    keepAlive: true
  }
}, {
  path: '/performanceDutiesDetails',
  name: 'performanceDutiesDetails',
  component: performanceDutiesDetails,
  meta: {
    title: '履职情况详情',
    keepAlive: true
  }
}, {
  path: '/resultDetails',
  name: 'resultDetails',
  component: resultDetails,
  meta: {
    title: '履职成功库详情',
    keepAlive: true
  }
}]
export default thinkTanks
