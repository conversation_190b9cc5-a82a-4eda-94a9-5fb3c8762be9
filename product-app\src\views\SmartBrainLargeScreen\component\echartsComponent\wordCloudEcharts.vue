<template>
  <div :id="chartId" class="word-cloud-echarts"></div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'
import 'echarts-wordcloud'

export default defineComponent({
  name: 'WordCloudEcharts',
  props: {
    id: { type: String, required: true },
    wordList: { type: Array, default: () => [] },
    colorList: {
      type: Array,
      default: () => [
        '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
        '#8B5CF6', '#06B6D4', '#F97316', '#EC4899',
        '#84CC16', '#6366F1', '#14B8A6', '#F43F5E'
      ]
    },
    sizeRange: { type: Array, default: () => [8, 35] }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const renderChart = () => {
      if (!chartInstance) {
        chartInstance = echarts.init(document.getElementById(chartId.value))
      }

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          show: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: params => {
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.data.name}</div>
              <div style="color: #ccc;">热度: ${params.data.value}</div>
            </div>`
          }
        },
        series: [
          {
            type: 'wordCloud',
            shape: 'circle',
            left: 'center',
            top: 'center',
            width: '100%',
            height: '90%',
            right: null,
            bottom: null,
            sizeRange: [12, 30], // 进一步减小字体大小范围
            rotationRange: [5, -5], // 进一步减少倾斜角度，从-20到20改为-15到15
            rotationStep: 5, // 进一步减少旋转步长，从10改为5
            gridSize: 14,
            drawOutOfBound: false,
            layoutAnimation: true,
            textStyle: {
              fontFamily: 'PingFang SC, Microsoft YaHei, sans-serif',
              fontWeight: '500',
              color: function (params) {
                // 直接随机取颜色
                return props.colorList[Math.floor(Math.random() * props.colorList.length)]
              }
            },
            emphasis: {
              focus: 'self',
              textStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.4)'
              }
            },
            data: props.wordList
          }
        ]
      }

      chartInstance.setOption(option)
    }

    onMounted(() => {
      renderChart()
    })

    onBeforeUnmount(() => {
      chartInstance && chartInstance.dispose()
    })

    watch(() => props.wordList, () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      renderChart()
    }, { deep: true })

    return {
      chartId
    }
  }
})
</script>

<style scoped>
.word-cloud-echarts {
  width: 100%;
  height: 200px;
}
</style>
