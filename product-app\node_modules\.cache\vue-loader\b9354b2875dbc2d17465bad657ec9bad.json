{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue", "mtime": 1755081172689}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB1c2VSb3V0ZSB9IGZyb20gJ3Z1ZS1yb3V0ZXInDQppbXBvcnQgeyBvbk1vdW50ZWQsIHJlYWN0aXZlLCB0b1JlZnMgfSBmcm9tICd2dWUnDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNYWpvclByb2plY3RzRGV0YWlsc0NvcHknLA0KICBjb21wb25lbnRzOiB7DQogIH0sDQogIHNldHVwICgpIHsNCiAgICBjb25zdCByb3V0ZSA9IHVzZVJvdXRlKCkNCiAgICBjb25zdCBkYXRhID0gcmVhY3RpdmUoew0KICAgICAgdGl0bGU6IHJvdXRlLnF1ZXJ5LnRpdGxlIHx8ICfor6bmg4UnLA0KICAgICAgaWQ6IHJvdXRlLnF1ZXJ5LmlkLA0KICAgICAgZGV0YWlsczoge30NCiAgICB9KQ0KICAgIG9uTW91bnRlZCgoKSA9PiB7DQogICAgfSkNCiAgICByZXR1cm4geyAuLi50b1JlZnMoZGF0YSkgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\ImportantWork\\MajorProjects\\MajorProjectsDetailsCopy.vue"], "names": [], "mappings": ";AAMA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/ImportantWork/MajorProjects/MajorProjectsDetailsCopy.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"MajorProjectsDetailsCopy\">\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { onMounted, reactive, toRefs } from 'vue'\r\nexport default {\r\n  name: 'MajorProjectsDetailsCopy',\r\n  components: {\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const data = reactive({\r\n      title: route.query.title || '详情',\r\n      id: route.query.id,\r\n      details: {}\r\n    })\r\n    onMounted(() => {\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.MajorProjectsDetailsCopy {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n</style>\r\n"]}]}