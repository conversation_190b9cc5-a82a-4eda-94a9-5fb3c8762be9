<template>
  <div class="NetworkpoliticsList">
    <van-pull-refresh v-model="refreshing"
                      style="min-height: 80vh;"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!-- 话题推荐 -->
        <div v-if="hotList.data.length > 0"
             class="hotRecommend">
          <div class="hot_title_top flex_box flex_align_center ">
            <div class="hint_default_icon"
                 :style="'background:'+appTheme+''"></div>
            <div class="hint_default_name">{{hotList.name}}</div>
          </div>
          <div>
            <van-tabs v-model:active="hotList.value"
                      swipeable
                      :ellipsis="false"
                      line-width="0"
                      :lazy-render="false">
              <van-tab v-for="(item,index) in hotList.data"
                       :key="index">
                <template v-slot:title>
                  <div @click="openDetails(item)"
                       class="members_item"
                       :style="'box-shadow: 0 1px 3px 0 '+appTheme+';background: linear-gradient('+$general.colorRgba(appTheme,0.7)+','+$general.colorRgba(appTheme,0.7)+'), url('+item.BGurl+') no-repeat'">
                    <div class="T-flexbox-vertical">
                      <img :style="$general.loadConfigurationSize([1,0])"
                           style="margin-bottom: 4px;"
                           src="../../assets/img/colon_colon.png"
                           alt="">
                      <div class="hot_title"
                           v-html="item.title"></div>
                    </div>
                  </div>
                </template>
              </van-tab>
            </van-tabs>
          </div>
        </div>
        <!--数据列表-->
        <ul v-if="listData.length != 0"
            style="margin-top: 16px;">
          <div v-for="(item,index) in listData"
               :key="index"
               class="van-hairline--bottom">
            <van-cell clickable
                      class="vue_newslist2_item"
                      @click="openDetails(item,index)">
              <div style="position: relative;">
                <!-- v-if="$general.getItemForKey(item.id,redIds,'id')" -->
                <div v-if="redIds.includes(item.id)"
                     style="right:8px;"
                     class="notRead"></div>
              </div>
              <div class="flex_box vue_newslist2_warp">
                <div class="flex_placeholder vue_newslist2_con">
                  <div>
                    <div class="vue_newslist2_title"
                         v-html="item.title"></div>
                    <div class="timeClass">
                      {{dayjs(item.starTime).format('YYYY-MM-DD')}}</div>
                  </div>
                  <div v-if="!item.organizeType"
                       class="timeClass">
                    <span class="inherit">{{item.browseCount}}阅读</span>
                    <span class="inherit"
                          style="margin-left: 4px;">{{item.commentCount}}评论</span>
                  </div>
                  <div v-else
                       class="organizeTypeClass">{{item.organizeType}}</div>
                </div>
                <div v-if="item.url">
                  <img :src="item.url"
                       alt=""
                       style="width: 3.8rem;height: 2.6rem;border-radius: 0.1rem">
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
/* eslint-disable */
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
export default {
  name: 'NetworkpoliticsList',
  components: {
  },
  setup () {
    const store = useStore()
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      active: '0',
      appTheme: sessionStorage.getItem('appTheme'),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      listData: [],
      seachText: '',
      hotList: {
        name: "话题推荐",
        value: '',
        data: []
      },
      redIds: []
    })

    onMounted(() => {
    })

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      // getRedPointNum()
      getList()
      getRedIds()
    }
    // 统计网络议政红点数量
    // const getRedPointNum = async () => {
    //   const ret = await $api.Networkpolitics.getRedPointNumByModule({ module: 'app' })
    //   const list = ret ? ret.data || [] : []
    //   if (list && list.length !== 0) {
    //     list.forEach(function (_eItem, _eIndex, _eArr) {
    //       if (_eItem.code === 'survey') {
    //         console.log('_eItem.count---->', _eItem.count)
    //         store.commit('setOnlinePoliticalDiscussionRedDotsNumber', _eItem.count + 1)
    //       }
    //     })
    //   }
    // }

    const onLoad = () => {
      data.loading = true
      getList()
      getRedIds()
    }
    // 获取未读红点
    const getRedIds = async () => {
      // var nowKey = getItemForKey(that.pageParam.relateType, controlChart, 'value')
      const res = await $api.Networkpolitics.getRedIds({ type: 'survey' })
      console.log('res==>>>', res)
      data.redIds = res ? res.data || [] : []
    }
    // 获取网络议政列表
    const getList = async () => {
      data.hotList.data = []
      var res = []
      var datas = {
        pageNo: data.pageNo,
        pageSize: 10,
        keyword: data.seachText,
        isSeeAll: 1,
        isPush: 1,
        publisher: 1 || 0
      }
      res = await $api.Networkpolitics.list(datas)
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.id || '' // id
        item.relateType = item.type || 5
        item.isTop = item.isTop || '' // 是否置顶
        item.title = item.title || '' // 标题
        item.time = item.starTime // 时间
        item.browseCount = item.browseCount || '0' // 浏览数
        item.commentCount = item.commentCount || '0' // 评论数
        item.shareCount = item.shareCount || '0' // 分享数
        item.surveyList = item.surveyList || [] // 子议题
        item.department = item.organizeType || '' // 发布部门
        item.userName = item.author || ''//发起人
        var attachmentList = item.attachmentList || []
        var bgList = attachmentList.filter(function (v) {
          return v.moduleType === 'splashBackGround'
        });
        var coverList = attachmentList.filter(function (v) {
          return v.moduleType === 'splashImg'
        });
        if (bgList.length) {
          item.BGurl = bgList[0].filePath
        }
        if (!bgList.length && coverList.length) {
          item.BGurl = coverList[0].filePath
        }
        if (coverList.length) {
          item.url = coverList[0].filePath
        }
        if (item.isTop == '1') {
          data.hotList.data.push(item)
          // data.hotList.data = data.hotList.data.concat(item)
        }
        // console.log('data.hotList==>', data.hotList)
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      data.pageNo = data.pageNo + 1
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }

    }
    const openDetails = (row) => {
      router.push({ name: 'NetworkpoliticsDetails', query: { id: row.id } })
    }
    const swipeTouchStart = (_item, _length) => {
      console.log('window.swipeStatus==>', window.swipeStatus)
      if (window.swipeStatus) {
        clearTimeout(window.swipeStatus)
        window.swipeStatus = null
      }
      window.notClose = true
    }
    return { ...toRefs(data), onRefresh, dayjs, onLoad, openDetails, $general, swipeTouchStart }
  }
}
</script>

<style lang="less" >
.NetworkpoliticsList {
  width: 100%;
  .hotRecommend {
    padding: 0 12px;
    background-color: #fff;
    padding-bottom: 12px;
    margin-bottom: 12px;
    .hot_title_top {
      font-weight: bold;
      color: #333333;
      padding: 12px 0;
      .hint_default_icon {
        width: 4px;
        margin-right: 8px;
        height: 17px;
      }
      .hint_default_name {
        font-size: 17px;
      }
    }
    .members_item {
      position: relative;
      padding: 12px;
      width: 130px;
      height: 160px;
      border-radius: 10px;
      box-sizing: border-box;
      overflow: hidden;
      background-size: 100% 100% !important;
      .hot_title {
        font-weight: bold;
        color: #ffffff;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
        white-space: normal;
        line-height: 1.8;
        font-size: 15px;
      }
    }
  }
  .vue_newslist2_item {
    padding: 15px 10px;
    .vue_newslist2_title {
      margin-bottom: 10px;
      font-size: 16px;
      color: #333;
      font-weight: bold;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
    }

    .timeClass,
    .browse {
      font-weight: 500;
      margin-top: 5px;
    }
    .organizeTypeClass {
      margin-top: 30px;
      font-weight: 500;
      color: #666666;
      font-size: 14px;
    }

    .timeClass {
      color: #666666;
      font-size: 14px;
    }

    .browse {
      color: #999999;
    }
  }
  .vue_newslist2_box + .vue_newslist2_box {
    border-top: 0rem solid rgba(0, 0, 0, 0);
  }
}
</style>

