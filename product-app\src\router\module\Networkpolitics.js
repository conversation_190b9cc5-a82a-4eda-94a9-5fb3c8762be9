const NetworkpoliticsList = () => import('@/views/Networkpolitics/NetworkpoliticsList')
const NetworkpoliticsDetails = () => import('@/views/Networkpolitics/NetworkpoliticsDetails')
const NetworkCommentDetails = () => import('@/views/Networkpolitics/NetworkCommentDetails')

const Networkpolitics = [{
  path: '/NetworkpoliticsList',
  name: 'NetworkpoliticsList',
  component: NetworkpoliticsList,
  meta: {
    title: '网络议政列表',
    keepAlive: true
  }
},
{
  path: '/NetworkpoliticsDetails',
  name: 'NetworkpoliticsDetails',
  component: NetworkpoliticsDetails,
  meta: {
    title: '网络议政详情',
    keepAlive: true
  }
},
{
  path: '/NetworkCommentDetails',
  name: 'NetworkCommentDetails',
  component: NetworkCommentDetails,
  meta: {
    title: '评论详情',
    keepAlive: true
  }
}]
export default Networkpolitics
