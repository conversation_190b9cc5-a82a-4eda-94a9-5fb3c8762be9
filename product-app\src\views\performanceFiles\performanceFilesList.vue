<template>
  <div class="performanceFilesList">
    <header id="dblclick"
            class="header flex_box"
            :style="'padding-top:'+safeAreaTop+'px; background:' + (dataList.length==0?appTheme:scrollTop>0?T.colorRgba(appTheme,scrollTop/100):'transparent')">
      <div class="flex_placeholder flex_box">
        <div class="flex_placeholder flex_box flex_align_center flex_justify_content">
          <div @click.stop="selYear()"
               class="flex_box flex_align_center"
               style="padding: 5px 8px;">
            <span :style="$general.loadConfiguration()"
                  class="title_name text_one2"
                  v-html="years.value+'履职排行'"></span>
            <van-icon :color="'#FFF'"
                      :size="((appFontSize-6)*0.01)+'rem'"
                      name="arrow-down"></van-icon>
          </div>
        </div>
      </div>
    </header>
    <div v-if="dataList.length != 0"
         class="performance_ranking_imgbox"
         :style="'padding-top:'+safeAreaTop+'px;'">
      <div class="performance_ranking_box">
        <div class="flex_box flex_align_center flex_justify_content">
          <img class="performance_ranking_station"
               src="../../assets/img/icon_performance_ranking.png"
               alt="" />
        </div>
      </div>
      <div class="performance_ranking_box">
        <div class="flex_box flex_align_center flex_justify_content">
          <div>
            <div class="flex_box flex_align_center"
                 style="margin-bottom: 5px;">
              <div v-for="(item,index) in [2,1,3]"
                   :key="index"
                   class="station_item T-flexbox-vertical flex_align_center flex_justify_content"
                   :style="index==1?'padding-bottom:75px;':'padding-bottom:20px'">
                <template v-if="dataList.length > item">
                  <div @click="openDetails(dataList[item-1])"
                       class="user_radius station_img_box"
                       :style="$general.loadConfigurationSize(index==1?50:40)+'border:2px solid '+(index==1?'#FFD423':'#E7E8EB')+';'">
                    <div class="station_num_box flex_box flex_align_center flex_justify_content">
                      <div :style="$general.loadConfiguration(-4)+'background: '+(index==1?'#FFD423':'#E7E8EB')+';'"
                           class="station_num">{{dataList[item-1].num}}</div>
                    </div>
                    <img class="user_radius"
                         style="width: 100%;height: 100%;border-radius: 10px;"
                         :src="dataList[item-1].url" />
                  </div>
                  <div :style="$general.loadConfiguration(-4)+''"
                       class="station_name">{{dataList[item-1].name}}</div>
                </template>
              </div>
            </div>
            <div :style="$general.loadConfiguration(-6)+'color:#FCBBBC;margin-bottom:5px;text-align: center;'">本排行为实时数据</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else
         :style="'height:'+(safeAreaTop+46)+'px;width:100%;'"></div>
    <van-search v-model="keyword"
                @search="search"
                @clear="search"
                placeholder="请输入搜索内容" />
    <ul v-if="dataList.length != 0"
        class="vue_newslist_box"
        style="background: #FFF;">
      <li v-for="(item,index) in dataList"
          :key="index"
          @click="openDetails(item)"
          class="flex_box flex_align_center van-hairline--bottom click"
          style="padding: 12px 24px 12px 8px;">
        <div style="min-width: 32px;margin-right:10px;"
             class="flex_box flex_justify_content">
          <div v-if="item.ranking > 3"
               :style="$general.loadConfiguration(1)+'font-weight: 500;color:#ccc;'">{{item.ranking}}</div>
          <van-icon v-else
                    :size="((appFontSize+10)*0.025)+'rem'"
                    :color="['#ffeb43','#e4e7e7','#ff9265'][item.ranking-1]"
                    :name="'medal'"></van-icon>
        </div>
        <div class="user_radius"
             :style="$general.loadConfigurationSize(20)+'margin-right:8px;'">
          <img style="width: 100%;height: 100%;object-fit: contain;"
               :src="item.url" />
        </div>
        <div class="text_one2 flex_placeholder"
             :style="$general.loadConfiguration(-2)+'color:#333;margin-right:0.3rem;'">{{item.name}}</div>
        <div :style="$general.loadConfiguration(-2)+'color:#333;margin-right:0.23rem;'">{{item.num}}</div>
        <div @click.stop="fabulousInfo(item)"
             class="flex_box flex_align_center flex_justify_content"
             style="padding: 7px 0;min-width:32px;">
          <van-icon :color="item.isFabulou?appTheme:'#ABABAB'"
                    :size="((appFontSize+4)*0.03)+'rem'"
                    :name="item.isFabulou?'good-job':'good-job-o'"></van-icon>
          <div v-if="item.fabulouCount > 0"
               :style="$general.loadConfiguration(-4)+'margin-left: 4px;color: #333;'">{{item.fabulouCount}}</div>
        </div>
      </li>
    </ul>
  </div>
  <van-action-sheet v-model:show="showAction"
                    :actions="years.data"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { Image as VanImage, Icon, Dialog, ActionSheet, Overlay } from 'vant'
export default {
  name: 'performanceFilesList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [Icon.name]: Icon,
    [VanImage.name]: VanImage
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      appFontSize: $general.data.appFontSize,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      refresh: null,
      pageNo: 1,
      pageSize: 500,
      total: 0,
      dataList: [],
      myDuty: {}, // 我的排名
      years: { value: '', data: [], dataFiter: [] },
      showAction: false,
      description: '选择年份'
    })
    onMounted(() => {
      var nowYear = new Date().getFullYear()
      data.years.dataFiter = []
      for (var i = 2018; i <= nowYear; i++) {
        data.years.data.push({ name: i })
        data.years.dataFiter.push({ text: i, value: i })
      }
      data.years.value = nowYear
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      // data.pageNo = data.pageNo += 1
      // getList()
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    // 获取列表
    const getList = async () => {
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        year: data.years.value,
        areaId: sessionStorage.getItem('areaId')
      }
      const res = await $api.performanceFiles.getGenerateDuty(param)
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.userId || ''
        item.num = item.num || ''
        item.url = item.headImg || ''
        item.name = item.userName || ''
        item.ranking = item.ranking || ''
        item.fabulouCount = item.fabulouNum || ''
        item.isFabulou = item.isFabulou === 1
      })
      data.dataList = data.dataList.concat(list)
      console.log('data.dataList==>', data.dataList)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 进详情
    const openDetails = (rows) => {
      router.push({ name: 'performanceFilesTypeList', query: { id: rows.id, title: rows.name, year: data.years.value } })
    }
    // 打开年份筛选
    const selYear = () => {
      data.showAction = true
    }
    // 选择年份
    const onSelect = (_item) => {
      data.years.value = _item.name
      onRefresh()
    }
    // 点赞
    const fabulousInfo = (_item) => {
      _item.isFabulou = !_item.isFabulou
      if (_item.isFabulou) {
        _item.fabulouCount++
      } else {
        _item.fabulouCount--
      }
      if (_item.isFabulou) {
        $api.general.saveFabulous({
          keyId: _item.id,
          type: '31',
          areaId: sessionStorage.getItem('areaId')
        }).then(res => {
          console.log('点赞=>', res)
        })
      } else {
        $api.general.delFabulous({
          keyId: _item.id,
          type: '31',
          areaId: sessionStorage.getItem('areaId')
        }).then(res => {
          console.log('取消点赞=>', res)
        })
      }
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, openDetails, selYear, search, fabulousInfo, onSelect }
  }
}
</script>
<style lang="less" scoped>
.performanceFilesList {
  background: #f8f8f8;
  .header {
    position: fixed;
    width: 100%;
    z-index: 1;
  }
  .performance_ranking_imgbox {
    width: 100%;
    height: 280px;
    background: url(../../assets/img/bg_performance_ranking.png) no-repeat;
    background-size: 100%;
    position: relative;
  }
  .performance_ranking_box {
    position: absolute;
    bottom: 0;
    width: 100%;
    .performance_ranking_station {
      height: 90px;
    }
    .station_item {
      width: 85px;
      .station_img_box {
        overflow: visible;
        position: relative;
        border-radius: 10px;
        .station_num_box {
          position: absolute;
          bottom: -15px;
          left: 0;
          right: 0;
          .station_num {
            padding: 3px 7px;
            line-height: 1;
            border-radius: 6px;
            color: #333;
          }
        }
      }
      .station_name {
        color: #fff;
        margin-top: 18px;
      }
    }
  }

  .search_box {
    background: #ffffff;
  }

  .station_ci {
    margin-top: 15px;
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
    border-radius: 12px;
    padding: 2px 12px;
    color: #fff;
    line-height: 1.2;
  }

  .station_ci_icon {
    margin-right: 3px;
  }

  .header {
    position: fixed;
    width: 100%;
    z-index: 1;
  }

  .title_name {
    font-weight: bold;
    color: #fefefe;
    margin-right: 5px;
  }

  .rankMe {
    position: absolute;
    right: 0;
    top: 0;
    padding: 1px 8px;
    color: #ffffff;
    border-bottom-left-radius: 5px;
  }
}
</style>
