<template>
  <div class="home">
    <div class="T-flexbox-vertical">
      <template v-if="footerBtns.active===0">
        <van-tabs v-model:active="switchs.value"
                  :color="appTheme"
                  :offset-top="isShowHead?'46px':'0'"
                  :title-active-color="appTheme"
                  :ellipsis="false">
          <van-tab v-for="(item,index) in switchs.data"
                   :key="index"
                   :title="item.label"
                   :name="item.value">
            <div id="search"
                 style="border-radius: 10px;display: flex;align-items: center;"
                 class="search_box"
                 :style="$general.loadConfiguration() ">
              <div class="search_warp flex_box"
                   style="flex: 5;">
                <div @click="search();"
                     class="search_btn flex_box flex_align_center flex_justify_content">
                </div>
                <form class="flex_placeholder flex_box flex_align_center search_input"
                      action="javascript:return true;">
                  <input id="searchInput"
                         class="flex_placeholder"
                         :style="$general.loadConfiguration(-1)"
                         placeholder="请输入搜索内容"
                         maxlength="100"
                         type="search"
                         ref="btnSearch"
                         @keyup.enter="search()"
                         v-model="seachText" />
                  <div v-if="seachText"
                       @click="seachText='';search();"
                       class="search_btn flex_box flex_align_center flex_justify_content">
                    <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                              :color="'#ccc'"
                              :name="'clear'"></van-icon>
                  </div>
                </form>
              </div>
            </div>
            <van-pull-refresh v-model="refreshing"
                              @refresh="onRefresh"
                              style="min-height: 600px;">
              <van-list v-model:loading="loading"
                        :finished="finished"
                        finished-text="没有更多了"
                        offset="52"
                        @load="onLoad">
                <template v-if="item.value === 'learning'">
                  <ul class="opinion_box">
                    <li v-for="(item,index) in listData"
                        :key="index"
                        class="opinion_item click"
                        @click="openBookDetails(item)">
                      <div class="opinion_title text_two"
                           v-html="item.title"></div>
                      <div class="opinion_addWarp flex_box flex_align_center">
                        <div v-if="readTypes != '200'">{{item.source || item.createBy}}</div>
                        <div class="flex_placeholder"></div>
                        <div class="opinion_time">{{dayjs(item.time).format('YYYY-MM-DD')}}</div>
                      </div>
                    </li>
                  </ul>
                </template>
                <template v-else>
                  <div class="listBox">
                    <div v-for="item in recommendationList"
                         :key="item.id">
                      <!--一本书样式-->
                      <li v-if="item.type=='typeOne'"
                          class="item_body_warp"
                          @click="openBookDetails(item)">
                        <div class="item_body typeOne T-flexbox-vertical flex_align_center">
                          <div v-if="item.title"
                               class="item_title"
                               v-html="item.title"></div>
                          <div v-if="item.smallTitle"
                               class="item_smallTitle"
                               v-html="item.smallTitle"></div>
                          <div class="item_img">
                            <div v-if="item.txt.state == 1"
                                 class="item_download flex_box flex_align_center flex_justify_content"
                                 :style="'font-size: 14px;'">
                              <van-circle fill="#FFF"
                                          :size="56"
                                          v-model="item.txt.schedule"
                                          :rate="item.txt.schedule"
                                          stroke-width="100"
                                          :text="item.txt.schedule+'%'"></van-circle>
                            </div>
                            <img v-if="item.txt.bookType == '2'"
                                 class="item_Sound"
                                 :src="icon_hasSound" />
                            <img v-if="item.url"
                                 :src="item.url"
                                 style="object-fit: cover;border-radius: 2px;" />
                            <div v-if="item.entity"
                                 class="item_entity"
                                 v-html="'实体书'"></div>
                          </div>
                          <div v-if="item.name"
                               class="item_name"
                               v-html="item.name"></div>
                          <div v-if="item.hint"
                               class="item_hint"
                               v-html="item.hint"></div>
                          <div v-if="item.btn.show"
                               class="flex_box flex_align_center flex_justify_content">
                            <div class="item_btn_box">
                              <van-button style="min-height: 40px;"
                                          :plain="item.btn.plain"
                                          @click.stop="listBtnClick(item)"
                                          round
                                          type="info"
                                          size="large"
                                          :color="appTheme">{{item.btn.text}}</van-button>
                            </div>
                          </div>
                        </div>
                      </li>
                      <!--六本书样式-->
                      <li v-else-if="item.type=='typeSex'"
                          class="item_body_warp">
                        <div class="item_body">
                          <div v-if="item.title"
                               class="item_title"
                               v-html="item.title"></div>
                          <van-empty v-if="!item.list || item.list.length == 0"
                                     :style="'font-size:14px'"
                                     :image="pageNot.url"
                                     :description="item.notText"></van-empty>
                          <div v-else
                               class="itemSex_box flex_box T-flex-flow-row-wrap">
                            <div @click="openBookDetails(nItem)"
                                 v-for="nItem in item.list"
                                 :key="nItem.id"
                                 class="itemSex_item">
                              <div :style="'width:93px;height:119px;position: relative;'">
                                <img v-if="nItem.txt.bookType == '2'"
                                     class="item_Sound"
                                     :src="icon_hasSound" />
                                <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                                     :src="nItem.img.url" />
                              </div>
                              <div v-if="nItem.name"
                                   class="itemSex_name text_two"
                                   v-html="nItem.name"></div>
                            </div>
                          </div>
                          <div v-if="item.btn.show"
                               class="flex_box flex_align_center flex_justify_content">
                            <div class="item_btn_box">
                              <van-button @click.stop="listBtnClick(item)"
                                          round
                                          type="info"
                                          size="large"
                                          :color="appTheme">{{item.btn.text}}</van-button>
                            </div>
                          </div>
                        </div>
                      </li>
                      <!--三本书样式-->
                      <li v-else-if="item.type=='typeThree'"
                          class="item_body_warp">
                        <div class="item_body">
                          <div v-if="item.title"
                               class="item_title"
                               v-html="item.title"></div>
                          <van-empty v-if="!item.list || item.list.length == 0"
                                     :style="'font-size:14px'"
                                     :image="icon_no_data"
                                     :description="item.notText"></van-empty>
                          <template v-else>
                            <van-cell v-for="nItem in item.list"
                                      :key="nItem.id"
                                      @click="openBookDetails(nItem)"
                                      style="padding:0;">
                              <div class="itemThree_item flex_box">
                                <div :style="'width:93px;height:117px;position: relative;'">
                                  <img v-if="nItem.txt.bookType == '2'"
                                       class="item_Sound"
                                       :src="icon_hasSound" />
                                  <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                                       :src="nItem.img.url" />
                                </div>
                                <div class="flex_placeholder"
                                     style="padding-top: 15px;padding-left: 10px;width:100%;">
                                  <div v-if="nItem.name"
                                       class="itemThree_name text_one2"
                                       v-html="nItem.name"></div>
                                  <div v-if="nItem.author"
                                       class="itemThree_author text_one2"
                                       :style="'font-size: 11px;color:'+appTheme"
                                       v-html="nItem.author"></div>
                                  <div v-if="nItem.summary"
                                       class="itemThree_summary text_two"
                                       v-html="nItem.summary"></div>
                                </div>
                              </div>
                            </van-cell>
                          </template>
                          <div v-if="item.btn.show"
                               class="flex_box flex_align_center flex_justify_content">
                            <div class="item_btn_box">
                              <van-button @click.stop="listBtnClick(item)"
                                          round
                                          type="info"
                                          size="large"
                                          :color="appTheme">{{item.btn.text}}</van-button>
                            </div>
                          </div>
                        </div>
                      </li>
                      <!--笔记样式-->
                      <li v-else-if="item.type=='typeNotes'"
                          class="item_body_warp">
                        <div class="item_body">
                          <div style="position: absolute;right: 0.25rem;top:0;">
                            <van-icon :size="39"
                                      :color="appTheme"
                                      name="bookmark"></van-icon>
                          </div>
                          <div v-if="item.title"
                               class="item_title"
                               v-html="item.title"></div>
                          <van-empty v-if="!item.list || item.list.length == 0"
                                     :style="'font-size:14px'"
                                     :image="icon_no_data"
                                     :description="item.notText"></van-empty>
                          <template v-else>
                            <div v-for="(nItem,nIndex) in item.list"
                                 :key="nItem.id"
                                 class="itemNotes_item flex_box">
                              <div @click="openPersonalDataDetails({id:nItem.userId})">
                                <img :style="'width:40px;height:40px;object-fit: contain;border-radius:50%;'"
                                     :src="nItem.url" />
                                <div class="itemNotes_name flex_placeholder text_one2"
                                     :style="'margin-top:0.08rem;text-align: center;'"
                                     v-html="nItem.userName"></div>
                              </div>
                              <div class="flex_placeholder itemNotes_item_box"
                                   :class="nIndex!=item.list.length-1?'van-hairline--bottom':''">
                                <div @click="showNoteDetails(nItem)"
                                     class="itemNotes_content"
                                     v-html="nItem.showAll?nItem.allContent:nItem.content"></div>
                                <div class="flex_box flex_align_end">
                                  <div class="flex_placeholder">
                                    <div v-if="nItem.name"
                                         :style="'font-size: 12px;'"><span @click="openBookDetails(nItem)"
                                            :style="'font-size: 12px;color:'+nItem.Tcolor">{{nItem.name}}</span>{{nItem.author}}</div>
                                  </div>
                                  <div class="flex_box flex_align_center">
                                    <div @click="clickLickBtn(nItem)"
                                         class="flex_box flex_align_end">
                                      <van-icon :size="18"
                                                :color="nItem.islike?appTheme:'#999'"
                                                name="good-job"></van-icon>
                                      <span :style="'font-size: 12px;color:'+(nItem.islike?appTheme:'#999')">{{nItem.likeNum}}</span>
                                    </div>
                                  </div>
                                </div>

                              </div>
                            </div>
                          </template>
                          <div v-if="item.btn.show"
                               class="flex_box flex_align_center flex_justify_content">
                            <div class="item_btn_box">
                              <van-button round
                                          type="info"
                                          size="large"
                                          :color="appTheme">{{item.btn.text}}</van-button>
                            </div>
                          </div>
                        </div>
                      </li>
                      <!--名言样式-->
                      <li v-else-if="item.type=='typeWords'"
                          class="item_body_warp">
                        <div class="item_body">
                          <div v-if="item.title"
                               class="item_title"
                               :style="'color:'+item.color"
                               v-html="item.title"></div>
                          <div class="itemWords_content_box"
                               :style="'border:1px solid '+item.color">
                            <div class="itemWords_content">
                              <div class="itemWords_content_hint"
                                   :style="'top:-0.65rem;left:-0.45rem;color:'+item.color">“</div>
                              <div class="itemWords_content_hint"
                                   :style="'bottom:-0.05rem;;right:-0.3rem;color:'+item.color">”</div>
                              <div :style="'font-size:14px;color:222;line-height: 1.6;'"
                                   v-html="item.content"></div>
                            </div>
                            <div v-if="item.name"
                                 class="itemWords_author"
                                 :style="'font-size:14px;'">—— {{item.author}} ·<span @click="openBookDetails(item)"
                                    :style="'font-size:14px;text-decoration:underline;color:'+item.Tcolor">{{item.name}}</span></div>
                          </div>
                          <div v-if="item.btn.show"
                               class="flex_box flex_align_center flex_justify_content">
                            <div class="item_btn_box">
                              <van-button round
                                          type="info"
                                          size="large"
                                          :color="appTheme">{{item.btn.text}}</van-button>
                            </div>
                          </div>
                        </div>
                      </li>
                      <!--大咖样式-->
                      <li v-else-if="item.type=='typeBig'"
                          class="item_body_warp">
                        <div class="item_body">
                          <div v-if="item.title"
                               v-html="item.title"></div>
                          <div class="flex_box flex_align_center"
                               style="padding: 0.1rem 0;">
                            <img v-if="item.user.url"
                                 :src="item.user.url"
                                 style="object-fit: contain;border-radius: 50%;margin-right: 0.1rem;width:40px;height:40px;"
                                 alt="" />
                            <div class="flex_placeholder">
                              <div class="flex_box flex_align_center">
                                <div class="flex_placeholder flex_box flex_align_center T-flex-flow-row-wrap">
                                  <div v-html="item.user.name"></div>
                                  <div v-for="(nItem) in item.user.tags"
                                       :key="nItem.id"
                                       :style="'font-size:12px;padding:0.04rem;'">
                                    <van-tag plain
                                             round
                                             type="primary">{{nItem}}</van-tag>
                                  </div>
                                </div>
                                <div :style="'font-size:14px;padding:0.05rem;'">
                                  <van-tag style="padding: 0.02rem 0.1rem;"
                                           plain
                                           round
                                           type="primary">{{'私信'}}</van-tag>
                                </div>
                              </div>
                              <div :style="'font-size:13px;margin-top:0.04rem;color:#888'"
                                   v-html="item.user.profession"></div>
                            </div>
                          </div>
                          <div :style="'font-size:14px;padding:0.1rem 0;'"
                               v-html="'他的书籍'"></div>
                          <van-empty v-if="!item.list || item.list.length == 0"
                                     :style="'font-size:14px;'"
                                     :image="icon_no_data"
                                     :description="item.notText"></van-empty>
                          <div v-else
                               class="itemSex_box flex_box T-flex-flow-row-wrap">
                            <div v-for="(nItem) in item.list"
                                 :key="nItem.id"
                                 class="itemSex_item">
                              <img v-if="nItem.url"
                                   :style="'width:93px;height:119px;object-fit: contain;border-radius: 0.02rem;'"
                                   :src="nItem.url" />
                              <div v-if="nItem.name"
                                   class="itemSex_name text_one2"
                                   v-html="nItem.name"></div>
                            </div>
                          </div>
                        </div>
                      </li>
                    </div>
                  </div>
                </template>
              </van-list>
            </van-pull-refresh>
          </van-tab>
        </van-tabs>
      </template>
      <template v-else-if="footerBtns.active===1">
        <div class="library">
          <div style="padding: 11px 16px;">
            <div class="search_warp flex_box flex_align_center">
              <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="18"
                          :color="'#A5A5A5'"
                          name="search"></van-icon>
              </div>
              <form @click="openSearch()"
                    class="flex_placeholder flex_box flex_align_center search_input"
                    action="javascript:return true;"><input disabled=""
                       id="searchInput"
                       :style="'font-size:15px;'"
                       :placeholder="seachSuggest"
                       maxlength="100"
                       type="search"
                       ref="btnSearch"
                       @keyup.enter="btnSearch()"
                       v-model="seachText" /></form>
            </div>
          </div>
          <!--书库banner-->
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad">
              <div v-if="carouselList.length != 0"
                   style="padding: 10px 16px;">
                <van-swipe class="carouselMap"
                           :autoplay="3000"
                           :indicator-color="appTheme"
                           :show-indicators="true">
                  <van-swipe-item @click="openBookDetails(item)"
                                  v-for="(item) in carouselList"
                                  :key="item.id">
                    <img class="carousel_img"
                         :src="item.url" />
                    <div class="carousel_elBox flex_box flex_align_center">
                      <div class="carousel_title text_one2"
                           :style="'font-size:15px;'"
                           v-html="item.title"></div>
                    </div>
                  </van-swipe-item>
                </van-swipe>
              </div>
              <!--书库分类-->
              <div style="padding: 11px 16px;">
                <div v-for="(item) in classification"
                     :key="item.id">
                  <div class="card_item"
                       v-if="item.data.length != 0">
                    <div class="flex_box flex_align_center"
                         style="padding: 10px;">
                      <div class="flex_placeholder"
                           :style="'font-size:17px;font-weight: bold;'">{{item.name}}</div>
                      <div @click="openBookStoreDetails(item)"
                           class="flex_box flex_align_center">
                        <div :style="'font-size:12px;font-weight: bold;color:#999'">{{'查看更多'}}</div>
                        <van-icon style="margin-left: 4px;"
                                  :size="12"
                                  :color="'#999'"
                                  :name="'arrow'"></van-icon>
                      </div>
                    </div>
                    <van-empty v-if="!item.data || item.data.length == 0"
                               :style="'font-size:14px;'"
                               :image="pageNot.url"
                               :description="'暂无数据'"></van-empty>
                    <div v-else
                         class="itemSex_box flex_box T-flex-flow-row-wrap">
                      <div v-for="(nItem) in item.data"
                           :key="nItem.id"
                           @click="openBookDetails(nItem)"
                           class="itemSex_item click">
                        <div :style="'width:60px;height:81px;margin:auto;position: relative;'">
                          <img v-if="nItem.txt.bookType == '2'"
                               class="item_Sound"
                               :src="icon_hasSound" />
                          <img v-if="nItem.txt.isAvailable == '0'"
                               class="item_overdue"
                               src="../../assets/img/overdue.png" />
                          <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                               :src="nItem.img.url" />
                        </div>
                        <div v-if="nItem.name"
                             class="itemSex_name text_one2"
                             :style="'font-size:12px;'"
                             v-html="nItem.name"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!--卡片分类-->
              <div style="padding: 11px 16px;">
                <div v-for="(item) in card"
                     :key="item.id">
                  <div class="card_item"
                       v-if="item.data.length != 0">
                    <div class="flex_box flex_align_center"
                         style="padding: 10px;">
                      <van-icon style="margin-right: 4px;"
                                :size="18"
                                :color="item.color"
                                :name="item.icon"></van-icon>
                      <div class="flex_placeholder"
                           :style="'font-size:14px;'">{{item.name}}</div>
                      <div @click="getTypesData(item,1)"
                           class="flex_box flex_align_center">
                        <div :style="'font-size:14px;font-weight: bold;color:'+appTheme">{{'换一批'}}</div>
                        <van-icon style="margin-left: 4px;"
                                  :size="18"
                                  :color="appTheme"
                                  :name="'replay'"></van-icon>
                      </div>
                    </div>
                    <van-empty v-if="!item.data || item.data.length == 0"
                               :style="'font-size:14px;'"
                               :image="icon_no_data"
                               :description="'暂无数据'"></van-empty>
                    <div v-else
                         class="itemSex_box flex_box T-flex-flow-row-wrap">
                      <div v-for="(nItem) in item.data"
                           :key="nItem.id"
                           @click="openBookDetails(nItem)"
                           class="itemSex_item click">
                        <div :style="'width:60px;height:81px;margin:auto;position: relative;'">
                          <img v-if="nItem.txt.bookType == '2'"
                               class="item_Sound"
                               :src="icon_hasSound" />
                          <img v-if="nItem.txt.isAvailable == '0'"
                               class="item_overdue"
                               src="../../assets/img/overdue.png" />
                          <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                               :src="nItem.img.url" />
                        </div>
                        <div v-if="nItem.name"
                             class="itemSex_name text_one2"
                             :style="'font-size:14px;'"
                             v-html="nItem.name"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </van-list>
          </van-pull-refresh>
        </div>
      </template>
      <template v-else-if="footerBtns.active===2">
        <div class="bookNotice">
          <div id="search"
               class="search_box"
               style="background:#f8f8f8;">
            <div class="search_warp flex_box">
              <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="16"
                          :color="'#757575'"
                          name="search"></van-icon>
              </div>
              <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                       :placeholder="'搜索'"
                       maxlength="100"
                       type="search"
                       @keyup.enter="btnSearch"
                       v-model="keyword" /></form>
            </div>
          </div>
          <!--数据列表-->
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad">
              <!--历史阅读list-->
              <ul v-if="history.length != 0"
                  class="history_box">
                <van-cell clickable
                          @click="openBookDetails(nItem)"
                          v-for="(nItem,nIndex) in history"
                          :key="nIndex"
                          style="padding:0;">
                  <div class="history_item flex_box">
                    <div :style="'width:93px;height:117px;'+'position: relative;'">
                      <div v-if="nItem.txt.state == 1"
                           class="item_download flex_box flex_align_center flex_justify_content"
                           :style="'font-size:14px;'">
                        <van-circle fill="#FFF"
                                    :size="56"
                                    v-model="nItem.txt.schedule"
                                    :rate="nItem.txt.schedule"
                                    stroke-width="100"
                                    :text="nItem.txt.schedule+'%'"></van-circle>
                      </div>
                      <img v-if="nItem.txt.bookType == '2'"
                           class="item_Sound"
                           :style="'width:28px;height:28px;'"
                           :src="icon_hasSound" />
                      <img v-if="nItem.txt.isAvailable == '0'"
                           class="item_overdue"
                           src="../../assets/img/overdue.png" />
                      <img style="width: 100%;height: 100%;object-fit: cover;border-radius: 2px;"
                           :src="nItem.img.url" />
                    </div>
                    <div class="flex_placeholder"
                         style="padding-top: 5px;padding-left: 10px;">
                      <div v-if="nItem.name"
                           class="history_name text_one2"
                           :style="'font-size:15px;'"
                           v-html="nItem.name"></div>
                      <div v-if="nItem.author"
                           class="history_author text_one2"
                           :style="'font-size:12px;'+'color:'+appTheme"
                           v-html="nItem.author"></div>
                      <div v-if="nItem.summary"
                           class="history_summary text_two"
                           :style="'font-size:12px;'"
                           v-html="nItem.summary"></div>
                      <div v-if="nItem.txt.bookType == '3'"
                           class="history_progress flex_box flex_align_center">
                        <div class="flex_placeholder">
                          <van-progress :percentage="nItem.progress"
                                        :color="appTheme"
                                        track-color="#D8D8D8"
                                        :show-pivot="false"></van-progress>
                        </div>
                        <div class="history_progress_text"
                             :style="'font-size:12px;'">{{nItem.progress.toFixed(1)}}%</div>
                      </div>
                      <div v-else
                           class="itemSex_progress text_one2"
                           :style="'font-size:12px;'"
                           v-html="('已阅读第'+nItem.progress+'章')"></div>

                      <div @click.stop="goBookReader(nItem.txt,nItem.id)"
                           :style="'font-size:13px;'">
                        <van-button round
                                    size="small"
                                    type="default">继续阅读</van-button>
                      </div>
                    </div>
                  </div>
                </van-cell>
              </ul>
              <!--数据列表-->
              <!-- v-if="!switchs.value?!isNull:true" -->
              <ul>
                <li class="item_body_warp">
                  <div class="item_body"
                       style="padding: 15px 0;">
                    <div class="flex_box flex_align_center van-hairline--bottom"
                         style="margin-bottom: 10px;padding-right: 15px;">
                      <!-- <div class="flex_placeholder"
                           :class="switchs.data.length < 4?'flex_box':''"
                           :style="'font-size:15px;'">
                        <van-tabs v-model="switchs.value"
                                  @click="tabClick"
                                  swipe-threshold="1"
                                  :color="appTheme"
                                  :ellipsis="false">
                          <van-tab v-for="(item,index) in switchs.data"
                                   :title="item.label"
                                   :key="index"
                                   :name="item.value"></van-tab>
                        </van-tabs>
                      </div> -->
                      <!-- <div @click="isSetting = !isSetting;listSelect=[];"
                           style="flex-shrink:0;padding: 8px;flex-shrink:0;">
                        <van-icon v-if="!isSetting"
                                  :size="21"
                                  :color="appTheme"
                                  :name="'setting-o'"></van-icon>
                        <div v-else
                             :style="'font-size:15px;'+'padding-right:4px;margin-right:-18px;width:55px;'">
                          <van-tag style="padding: 1px 10px;"
                                   :color="appTheme"
                                   round
                                   type="primary">{{'完成'}}</van-tag>
                        </div>
                      </div> -->
                    </div>
                    <van-empty v-if="dataList.length == 0"
                               :style="'font-size:14px;'"
                               :image="icon_no_data"
                               :description="'暂无数据'"></van-empty>
                    <van-checkbox-group v-else
                                        ref="checkboxGroup"
                                        v-model="listSelect">
                      <div class="itemSex_box flex_box T-flex-flow-row-wrap">
                        <div v-for="(nItem,nIndex) in dataList"
                             :key="nIndex"
                             class="itemSex_item"
                             @click="clickBook(nItem,nIndex)">
                          <div :style="'width:93px;height:119px;'+'position: relative;'">
                            <div v-if="nItem.txt.state == 1"
                                 class="item_download flex_box flex_align_center flex_justify_content"
                                 :style="'font-size:14px;'">
                              <van-circle fill="#FFF"
                                          :size="56"
                                          v-model="nItem.txt.schedule"
                                          :rate="nItem.txt.schedule"
                                          stroke-width="100"
                                          :text="nItem.txt.schedule+'%'"></van-circle>
                            </div>
                            <img v-if="nItem.txt.bookType == '2'"
                                 class="item_Sound"
                                 :style="'width:28px;height:28px;'"
                                 :src="icon_hasSound" />
                            <img v-if="nItem.txt.isAvailable == '0'"
                                 class="item_overdue"
                                 src="../../assets/img/overdue.png" />
                            <img style="width: 100%;height: 100%;object-fit: cover;border-radius: 2px;"
                                 :src="nItem.img.url" />
                            <van-checkbox v-if="isSetting"
                                          :icon-size="21"
                                          :checked-color="appTheme"
                                          :ret="'checkbox'+nItem.id"
                                          class="checkbox_item"
                                          :name="nItem"></van-checkbox>
                          </div>
                          <div v-if="nItem.name"
                               class="itemSex_name text_one2"
                               :style="'font-size:15px;'"
                               v-html="nItem.name"></div>
                          <div class="itemSex_progress text_one2"
                               :style="'font-size:12px;'"
                               v-html="nItem.txt.bookType == '3'?('已阅读'+(nItem.progress.toFixed(1))+'%'):('第'+nItem.progress+'章')"></div>
                        </div>
                      </div>
                    </van-checkbox-group>
                  </div>
                </li>
              </ul>
            </van-list>
          </van-pull-refresh>
          <!--底下设置的 时候全选界面-->
          <template v-if="isSetting">
            <div style="height: 40px;"></div>
            <div v-if="isSetting"
                 class="select_footer">
              <div class="select_footer_box flex_box flex_align_center">
                <van-checkbox v-if="dataList.length != 0"
                              @click="if($refs['checkboxGroup'])$refs['checkboxGroup'].toggleAll(isAllSelect);"
                              :icon-size="21"
                              v-model="isAllSelect"
                              :checked-color="appTheme">全选</van-checkbox>
                <div class="flex_placeholder"></div>
                <div v-if="dataList.length != 0"
                     class="flex_box"
                     style="margin-left:10px;">
                  <div @click.stop="clickAddCategory()"
                       :style="'padding-right:10px;'">
                    <van-tag style="padding: 2px 10px;"
                             :color="appTheme"
                             plain
                             round
                             type="primary">{{'加入分类'}}</van-tag>
                  </div>
                  <div @click.stop="clickDelete()">
                    <van-tag style="padding: 2px 10px;"
                             :color="appTheme"
                             plain
                             round
                             type="primary">{{'删除'}}</van-tag>
                  </div>
                </div>
                <div v-else
                     @click="clickAddCategory(1)"
                     :style="'padding-right:10px;'">
                  <van-tag style="padding: 2px 10px;"
                           :color="appTheme"
                           plain
                           round
                           type="primary">{{'分类管理'}}</van-tag>
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
      <div class="T-flex-item"></div>
      <footer v-if="footerBtns.data.length != 0"
              :style="'padding-bottom:'+((safeAreaBottom)*0.01)+'rem'"
              class="flex_box">
        <template v-for="(item,index) in footerBtns.data"
                  :key="index">
          <div class="footer_item T-flexbox-vertical flex_align_center flex_justify_content"
               @click="switchFooter(index,item)">
            <van-icon :color="appTheme"
                      :size="((appFontSize+5)*0.01)+'rem'"
                      class-prefix="iconfont"
                      :name="item.icon"></van-icon>
            <p class="footer_item_p text_one2"
               :style="$general.loadConfiguration(-6)+';'+(footerBtns.active == index?'color:'+appTheme:'color:#adadad')">{{item.title}}</p>
            <p v-if="item.pointNumber > 0"
               class="flex_box flex_align_center flex_justify_content text_one"
               :class="item.pointType == 'big'?'footer_item_hot_big':'footer_item_hot'"
               :style="item.pointType == 'big'?$general.loadConfiguration(-6)+$general.loadConfigurationSize(2):$general.loadConfigurationSize(-6)"
               v-html="item.pointType == 'big'?(item.pointNumber>99?'99+':item.pointNumber):''"></p>
          </div>
        </template>
      </footer>
    </div>
  </div>
</template>
<script>
import { reactive, toRefs, inject, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Empty } from 'vant'
export default ({
  name: 'bookHome',
  components: {
    [Empty.name]: Empty
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: sessionStorage.getItem('appTheme'),
      isShowHead: $isShowHead,
      active: '',
      seachText: '',
      pageNo: 1,
      pageSize: 10,
      user: JSON.parse(sessionStorage.getItem('user')),
      switchs: { value: '', data: [{ label: '学习参考', value: 'learning' }, { label: '推荐', value: 'recommend' }] },
      footerBtns: {
        active: 0,
        data: [
          { id: '335633278637703168', title: '阅读', icon: 'http://test.dc.cszysoft.com:21408/lzt/images/热门推荐.png', infoUrl: 'read', pointType: 'big', pointNumber: 0, remarks: '' },
          { id: '335633485739851776', title: '书库', icon: 'http://test.dc.cszysoft.com:21408/lzt/images/立方体 立体 多面体 方块 (1).png', infoUrl: 'stackBook', pointType: 'big', pointNumber: 0, remarks: '' },
          { id: '335633485739851776', title: '书桌', icon: 'http://test.dc.cszysoft.com:21408/lzt/images/我的.png', infoUrl: 'desk', pointType: 'big', pointNumber: 0, remarks: '' }
        ]
      },
      loading: false,
      finished: false,
      refreshing: false,
      listData: [],
      readTypes: '',
      recommendationList: [],
      carouselList: [],
      classification: [],
      card: [
        { name: '热门图书', icon: 'fire-o', color: '#FF4029', pageNo: 1, pageSize: 4, lisType: 'hotest​', data: [] },
        { name: '最新图书', icon: 'new-arrival-o', color: '#067DFF', pageNo: 1, pageSize: 4, lisType: 'newest​', data: [] },
        { name: '猜你喜欢', icon: 'like-o', color: '#F960D0', pageNo: 1, pageSize: 4, lisType: 'readest', data: [] }
      ],
      isAllSelect: false, // 是否全选
      isSetting: false,
      history: [],
      dataList: []
    })

    onMounted(() => {
      if (sessionStorage.getItem('memberRequiredId')) {
        data.readTypes = '200'
      }
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.recommendationList = []
      data.loading = true
      data.finished = false
      getStudyList()
      getRecommendationList()
      getBookListData()
      getReadbookinfo()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getStudyList()
    }
    // 搜索
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getStudyList()
    }
    // 底部点击切换
    const switchFooter = (_index, _item) => {
      data.footerBtns.active = _index
    }
    // 获取学习参考列表
    const getStudyList = async () => {
      const res = await $api.news.getAppList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText,
        structureId: sessionStorage.getItem('memberRequiredId') || '',
        module: 3,
        auditingFlag: 1,
        isAppShow: 1
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.relateId || item.id || ''
        item.title = item.title || ''
        if ((data.seachText).replace(/(^\s*)|(\s*$)/g, '')) {
          item.title = item.title.replace(new RegExp(data.seachText, 'g'), '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>')
        }
        // 去除HTML中的注释、去除HTML标签、去除HTML标签中的属性、去除所有空白字符即回车换行
        item.content = item.content ? item.content : ''
        item.source = item.source || ''
        item.createBy = item.createBy || ''
        item.isRead = item.isRead ? '1' : '0'// 是否已读
        item.time = item.publishDate || item.createDate || ''
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 获取推荐列表
    const getRecommendationList = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        isIssue: 1
      }
      var res = []
      res = await $api.bookAcademy.recommendlList(datas)
      var { data: list } = res
      var dataListNew = []
      list.forEach((element, index) => {
        var item = {}
        item.index = index + ((data.pageNo - 1) * data.pageSize)
        var itemType = element.type
        if (itemType === 'book') { // 书籍
          element = element.book
          item.count = element.count || ''
          item.type = element.count === 3 ? 'typeThree' : element.count === 6 ? 'typeSex' : 'typeOne'
          item.title = element.mainTitle || ''
          item.smallTitle = element.subhead || ''
          var books = element.books || []
          if (books.length < 1) { // 没关联书籍 但是发布了 不显示
            return
          }
          item.books = books
          if (item.type === 'typeOne') {
            item.nowSwitch = 0// 现在的位置角标
            item.maxSwitch = books.length// 共有几次可以切换 为1时不能切换
            showItemData(item)
            item.btn = { show: true, text: item.maxSwitch > 1 ? '换一本' : '加入书架', plain: item.maxSwitch === 1 }
            if (item.maxSwitch === 1) {
              // item.btn.text = ''
              existMyBook(item)// 如果只有一本 检查是否在书架中
              // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
            }
          } else if (item.type === 'typeThree' || item.type === 'typeSex') {
            item.nowSwitch = 0// 现在的位置角标  向上取整
            item.maxSwitch = Math.ceil(books.length / item.count)
            item.btn = { show: item.maxSwitch > 1, text: '换一批' }
            item.notText = '暂无数据'
            showItemData(item)
          }
        } else if (itemType === 'goldword') { // 金玉良言
          element = element.goldword
          item.type = 'typeWords'
          item.title = '金玉良言'
          item.color = '#DCB769'
          item.Tcolor = '#1987FF'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.content = element.content || ''
          item.id = element.bookId || ''
          item.author = element.authorName || ''
          item.name = element.bookName || ''
        } else if (itemType === 'note') { // 阅读笔记
          element = element.note
          item.type = 'typeNotes'
          item.title = '精选笔记'
          item.btn = { show: false, text: '换一批' }
          item.notText = '暂无数据'
          item.list = []
          var nItem = { islike: element.hasClick, likeNum: element.likenum }
          nItem.url = element.headImg || ''
          nItem.userName = element.createBy || ''
          nItem.userId = element.userId || ''
          nItem.content = element.noteContent || ''
          nItem.allContent = ''
          nItem.showAll = false
          nItem.noteId = element.id || ''
          nItem.id = element.bookId || ''
          nItem.Tcolor = '#1987FF'
          nItem.author = element.authorName || ''
          nItem.name = element.bookName || ''
          item.list.push(nItem)
        } else {

        }
        dataListNew.push(item)
      })
      data.recommendationList = data.recommendationList.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    // 进详情
    const openBookDetails = (row) => {
      if (row.type) {
        router.push({ name: 'newsDetails', query: { id: row.id, title: row.title, relateType: row.type } })
      } else {
        router.push({ name: 'bookDetail', query: { id: row.id } })
      }
    }
    // 展示 一本 三本 六本 书
    const showItemData = (_item) => {
      var books = _item.books
      if (_item.type === 'typeOne') {
        _item.id = books[_item.nowSwitch].bookId || ''
        _item.url = books[_item.nowSwitch].coverImgUrl || ''
        _item.txt = { url: books[_item.nowSwitch].bookContentUrl || '', state: 0, schedule: -1, name: books[_item.nowSwitch].bookName || '', bookType: books[_item.nowSwitch].bookType || '3' }
        _item.name = books[_item.nowSwitch].bookName || ''
        _item.hint = books[_item.nowSwitch].recommendWord || ''
      } else if (_item.type === 'typeThree' || _item.type === 'typeSex') {
        var nowData = []
        if (_item.maxSwitch > 1) {
          books.forEach((_eItem, _eIndex, _eArr) => {
            var itemSwitch = Math.ceil((_eIndex + 1) / (_item.count))
            if (itemSwitch - 1 === _item.nowSwitch) {
              nowData.push(_eItem)
            }
          })
        }
        if (nowData.length < _item.count) { // 翻页少于当前数量 再加上去
          var nowLength = _item.count - nowData.length
          books.forEach((_eItem, _eIndex, _eArr) => {
            if (_eIndex < nowLength) {
              nowData.push(_eItem)
            }
          })
        }
        _item.list = []
        nowData.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
          var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
          item.id = _eItem.bookId || ''// 书本id
          item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          // that.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
          _item.list.push(item)
        })
      }
    }
    // 检查是否在书架
    const existMyBook = async (_item) => {
      var datas = {
        bookId: _item.id
      }
      var { data: list } = await $api.bookAcademy.existBook(datas)
      _item = data.dataList[_item.index]
      _item.btn.existMyBook = list || false
      _item.btn.text = _item.btn.existMyBook ? '继续阅读' : '加入书架'
      _item.btn.show = true
    }
    // 列表点击 换一本或加入书架
    const listBtnClick = (_item) => {
      if (_item.maxSwitch === 1) { // 为1时 加入书架
        if (_item.btn.existMyBook) {
          goBookReader(_item.txt, _item.id)
        } else {
          data.optionItem = _item
          data.addCategory = true
          getTypeList()// 获取所有分类
        }
      } else { // 换一本
        if (_item.nowSwitch + 1 >= _item.maxSwitch) {
          _item.nowSwitch = 0
        } else {
          _item.nowSwitch++
        }
        showItemData(_item)
      }
    }
    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }
    // 获取所有分类
    const getTypeList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getALlTypeList(datas)
      data.switchs.data = [{ label: '所有书籍', value: '' }]
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        data.switchs.data.push({ label: _eItem.typeName, value: _eItem.id })
      })
    }
    // 书库搜索
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    // 获取书库数据
    const getBookListData = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getTypefourList(datas)
      var dataListNew = []
      list.forEach((_nItem, _nIndex, _nArr) => { // item index 原数组对象
        var nItem = { id: _nItem.firstTypeId || '', name: _nItem.firstTypeName || '', data: [] }
        var books = _nItem.books || []
        books.forEach((_nItem, _nIndex, _nArr) => {
          var item = { img: { url: _nItem.coverImgUrl || '' }, txt: { isAvailable: _nItem.isAvailable, url: _nItem.bookContentUrl || '', state: 0, schedule: -1, name: _nItem.bookName || '', bookType: _nItem.bookType || '' } }
          item.id = _nItem.id || ''// 书本id
          item.name = (_nItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_nItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_nItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          nItem.data.push(item)
        })
        dataListNew.push(nItem)
      })
      data.classification = dataListNew
      var RollBookListNew = []
      var { data: RollBookList } = await $api.bookAcademy.getRollBookList({ pageNo: 1, pageSize: 10, isIssue: 1 })
      RollBookList.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {}
        item.id = _eItem.bookId || ''
        item.url = _eItem.coverImgUrl || ''
        item.title = _eItem.recommendedWord || ''
        RollBookListNew.push(item)
      })
      data.carouselList = RollBookListNew

      data.card.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        getTypesData(_eItem)
      })
      data.loading = false
      data.refreshing = false
      data.finished = true
    }
    // 获取书库的分类
    const getTypesData = async (_item, type) => {
      if (type) {
        data.pageNo = data.pageNo + 1
      } else {
        data.pageNo = 1
      }
      var { data: BookList } = await $api.bookAcademy.getBookList({ pageNo: data.pageNo, pageSize: _item.pageSize || data.pageSize, lisType: _item.lisType })
      if (BookList && BookList.length !== 0) {
        _item.data = []
        BookList.forEach((_nItem, _nIndex, _nArr) => { // item index 原数组对象
          var item = { img: { url: _nItem.coverImgUrl || '' }, txt: { isAvailable: _nItem.isAvailable, url: _nItem.bookContentUrl || '', state: 0, schedule: -1, name: _nItem.bookName || '', bookType: _nItem.bookType || '' } }
          item.id = _nItem.id || ''// 书本id
          item.name = (_nItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_nItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_nItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          _item.data.push(item)
        })
      } else {
        data.pageNo = 0
        // getTypesData(_item)
      }
    }
    // 跳转更多
    const openBookStoreDetails = (item) => {
      router.push({ name: 'libraryDetails', query: { id: item.id, title: item.name } })
    }
    // 获取历史读书记录
    const getReadbookinfo = async () => {
      const lastReadBook = localStorage.getItem('myLastBook' + data.user.id)
      if (lastReadBook) {
        const { data: _eItem } = await $api.bookAcademy.readbookinfo({ bookId: lastReadBook })
        data.history = []
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
        item.itemId = _eItem.id || ''// 书桌id
        item.id = _eItem.bookId || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.progress = Number(_eItem.readPercent || '0')// 进度
        data.history = data.history.concat(item)
      }
      getData()
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        typeId: data.switchs.value

      }
      const { data: list, total } = await $api.bookAcademy.getMyBookList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
        item.id = _eItem.bookId || ''// 书本id
        item.businessId = _eItem.id || ''// 业务id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.progress = Number(_eItem.readPercent || '0')// 进度
        var sItem = $general.getItemForKey(item.id, data.dataList, 'id')
        if (!sItem) { newData.push(item) }
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    return { ...toRefs(data), onRefresh, onLoad, search, $general, switchFooter, dayjs, openBookDetails, listBtnClick, openSearch, openBookStoreDetails, getTypesData }
  }
})
</script>

<style lang="less" scoped>
footer {
  width: 100%;
  background-color: #fff;
  border-top: 1px solid #fafafa;
  position: fixed;
  bottom: 0;
  .footer_item {
    width: 100%;
    position: relative;
    padding: 10px 0;
    .footer_item_p {
      margin-top: 3px;
    }
    .footer_item_hot {
      position: absolute;
      top: 5px;
      right: calc(50% - 2px);
      background: #f92323;
      border-radius: 50%;
    }
    .footer_item_hot_big {
      position: absolute;
      top: 0.05rem;
      right: calc(50% - 2px);
      background: #f92323;
      border-radius: 50%;
      color: #fff;
    }
  }
  .footer_item img {
    margin-bottom: 2px;
  }
}

.home {
  width: 100%;
  min-height: 100%;
  background: #fff;
  padding-bottom: 80px;
  .opinion_box {
    padding: 5px 12px 12px 12px;
    .opinion_item {
      padding: 12px;
      box-shadow: 0px 0px 5px 0px #e4e4e4;
      border-radius: 5px;
      .opinion_title {
        font-weight: 500;
        color: #333333;
        line-height: 1.53;
        font-size: 15px;
      }
      .opinion_addWarp {
        margin-top: 12px;
        font-size: 12px;
        color: #999;
        .opinion_time {
          font-size: 12px;
          color: #999;
        }
      }
    }
    .opinion_item + .opinion_item {
      margin-top: 12px;
    }
  }
  .listBox {
    .item_title {
      color: #222;
      font-weight: bold;
      padding: 6px 0;
      text-align: center;
      font-size: 19px;
    }

    .typeOne .item_title {
      padding: 13px 0;
    }

    .item_smallTitle {
      color: #8b8a8a;
      text-align: center;
      font-size: 13px;
    }

    .item_img {
      margin: 10px 0 4px 0;
      position: relative;
      width: 129px;
      height: 165px;
    }

    .item_img img {
      width: 100%;
      height: 100%;
    }

    .item_Sound {
      width: 28px !important;
      height: 28px !important;
    }

    .item_entity {
      position: absolute;
      right: 0;
      top: 0;
      background: #4cd964;
      color: #fff;
      padding: 2px 10px;
      border-bottom-left-radius: 5px;
      border-top-right-radius: 5px;
      font-size: 10px;
    }

    .item_name {
      color: #222;
      font-weight: 600;
      margin-top: 4px;
      text-align: center;
      font-size: 15px;
    }

    .item_hint {
      color: #8b8a8a;
      margin-top: 7px;
      text-align: center;
      font-size: 13px;
    }

    .item_btn_box {
      width: 252px;
      margin: 20px 0 10px 0;
      font-size: 15px;
    }

    .itemSex_box {
      margin: 0 -6px;
    }

    .itemSex_item {
      width: 33.33%;
      padding: 0 6px 5px 6px;
    }

    .itemSex_name {
      color: #222;
      font-weight: 500;
      margin-top: 0;
      padding-left: 5px;
      font-size: 15px;
    }

    .itemThree_item {
      padding: 6px 0;
    }

    .itemThree_name {
      color: #222;
      font-weight: 400;
      margin-top: 0;
      margin-bottom: 3px;
      font-size: 15px;
    }

    .itemThree_summary {
      font-weight: 400;
      margin-top: 8px;
      color: #8b8a8a;
      font-size: 12px;
    }

    .itemNotes_item {
      padding-top: 12px;
    }

    .itemNotes_item_box {
      padding: 0 0 15px 10px;
    }

    .itemNotes_name {
      font-weight: 500;
      color: #222222;
      font-size: 12px;
    }

    .itemNotes_content {
      color: #222222;
      padding-bottom: 6px;
      font-size: 13px;
    }

    .itemNotes_from {
      padding: 5px;
      border-radius: 6px;
    }

    .itemNotes_author {
      margin-top: 3px;
    }

    .itemWords_content_box {
      margin: 10px 0;
      padding: 16px 16px;
    }

    .itemWords_content {
      position: relative;
    }

    .itemWords_content_hint {
      position: absolute;
      background: #fff;
      font-size: 56px;
      width: 22px;
      height: 26px;
      font-family: Helvetica;
    }

    .itemWords_author {
      text-align: right;
      margin-top: 15px;
    }
  }
  .library {
    width: 100%;
    background: #fff;

    .top {
      width: 100%;
      height: 50px;
      background: #0271e3;
      color: #fff;
      font-size: 20px;
      line-height: 50px;
    }

    .search_warp {
      width: 100%;
      background: #f9f9f9;
      min-height: 35px;
      border-radius: 35px;
      padding: 0 10px;
    }

    .carouselMap {
      width: 100%;
      height: auto;
      border-radius: 10px;
      overflow: hidden;
    }

    .carouselMap .carousel_img {
      position: relative;
      width: 100%;
      height: 154px;
      object-fit: cover;
    }

    .carouselMap .carousel_elBox {
      width: 100%;
      height: 28px;
      position: absolute;
      bottom: 0;
      background: rgba(0, 0, 0, 0.15);
      color: #fff;
    }

    .carouselMap .carousel_title {
      font-weight: 400;
      color: #fff;
      line-height: 1.3;
      padding: 0 5px;
    }

    .carouselMap .van-swipe__indicators {
      bottom: 3px;
    }

    .carouselMap .van-swipe__indicator {
      background-color: #9c9c9c;
      opacity: 1;
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .van-swipe__indicator:not(:last-child) {
      margin-right: 0.1px;
    }

    .classification_item {
      padding: 15px 0;
      width: 25%;
      text-align: center;
      font-weight: bold;
    }

    .card_item {
      box-shadow: 0px 0px 26px -11px rgba(0, 0, 0, 0.4);
      background: #fff;
      margin-top: 10px;
      padding: 5px;
      border-radius: 16px;
    }

    .itemSex_box {
      margin: 0 -6px;
    }

    .itemSex_item {
      width: 25%;
      padding: 0 6px 5px 6px;
    }

    .itemSex_name {
      color: #5e646d;
      font-weight: 500;
      margin-top: 0px;
      text-align: center;
    }

    .item_overdue {
      position: absolute;
      top: 0;
      right: 0;
      width: 52px;
      height: 52px;
    }
  }
}
</style>
