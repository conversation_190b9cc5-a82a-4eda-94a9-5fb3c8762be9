<template>
  <div class="home">
    <router-view></router-view>
    <van-tabbar v-model="active"
                :active-color="appTheme">
      <van-tabbar-item v-for="item in tabbarList"
                       :key="item.id"
                       :icon="active==item.id?item.selectIconUrl:item.iconUrl"
                       :to="item.infoUrl"
                       :badge="getallNumber(item)>0 ? getallNumber(item) > 99 ? '99+' : getallNumber(item) : ''"
                       :name="item.id">{{item.name}}</van-tabbar-item>
      <!-- :badge="item.pointNumber>0?item.pointNumber:''" -->
    </van-tabbar>
  </div>
</template>

<script>
import { computed, defineComponent, reactive, toRefs, inject, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'Home',
  setup () {
    const store = useStore()
    const router = useRouter()
    const $isShowHead = inject('$isShowHead')
    const state = reactive({
      appTheme: sessionStorage.getItem('appTheme'),
      isShowHead: $isShowHead,
      active: '',
      tabbarList: computed(() => store.getters.tabbarList),
      onlinePoliticalDiscussionRedDots: computed(() => store.state.onlinePoliticalDiscussionRedDots),
      ModuleRedDotsNumber1: computed(() => store.state.ModuleRedDotsNumber1),
      ModuleRedDotsNumber2: computed(() => store.state.ModuleRedDotsNumber2),
      ModuleRedDotsNumber3: computed(() => store.state.ModuleRedDotsNumber3),
      ModuleRedDotsNumber4: computed(() => store.state.ModuleRedDotsNumber4),
      ModuleRedDotsNumber5: computed(() => store.state.ModuleRedDotsNumber5),
      ModuleRedDotsNumber6: computed(() => store.state.ModuleRedDotsNumber6)
    })

    watch(() => state.active, val => {
      const historyIndex = sessionStorage.getItem('historyIndex')
      if (!val && historyIndex) {
        state.active = historyIndex
      } else if (val) {
        sessionStorage.setItem('historyIndex', val)
      }
    }, { immediate: true })

    const init = () => {
      const historyIndex = sessionStorage.getItem('historyIndex') || ''
      if (historyIndex) return
      const homeItem = state.tabbarList.find(({ name }) => name === '首页')
      console.log('homeItem===>', homeItem)
      state.active = homeItem.id
      var myParam = { title: homeItem.name }
      if (homeItem.remarks) {
        const a = homeItem.remarks.split('&')
        a.forEach(element => {
          myParam[element.split('=')[0]] = element.split('=')[1]
        })
      }
      router.push({ path: homeItem.infoUrl, query: myParam })
      // router.replace({ path: homeItem.infoUrl })
    }
    const appList = () => store.dispatch('getAppList')
    onMounted(() => {
      init()
      appList()
    })
    const getallNumber = (item) => {
      if (item.infoUrl2 === 'NetworkpoliticsList') {
        return state.onlinePoliticalDiscussionRedDots
      } else if (item.infoUrl2 === 'module') {
        return state.ModuleRedDotsNumber1 + state.ModuleRedDotsNumber2 + state.ModuleRedDotsNumber3 + state.ModuleRedDotsNumber4 + state.ModuleRedDotsNumber5 + state.ModuleRedDotsNumber6
      } else {
        return ''
      }
    }
    return {
      ...toRefs(state), getallNumber
    }
  }
})
</script>

<style lang="less" scoped>
.home {
  width: 100%;
  .van-tabbar {
    height: 60px;
  }
  .van-tabbar ::v-deep .van-tabbar-item__text {
    font-size: 12px !important;
    line-height: 12px;
  }
  .van-tabbar ::v-deep .van-tabbar-item__icon {
    font-size: 20px !important;
  }
  .van-tabbar ::v-deep .van-tabbar-item__icon {
    font-size: 36px !important;
  }
  .van-tabbar
    ::v-deep
    .van-tabbar-item:nth-child(2)
    .van-tabbar-item__icon
    .van-icon
    .van-icon__image {
    height: 32px !important;
  }
}
</style>
