<template>
  <div class="myList">
    <van-tabs v-model:active="switchs.value"
              :color="appTheme"
              swipeable
              sticky
              :offset-top="isShowHead?'46px':'0'"
              :title-active-color="appTheme"
              :ellipsis="false">
      <van-tab v-for="(item,index) in switchs.data"
               :key="index"
               :title="item.label"
               :name="item.value">
        <!-- 搜索框 -->
        <div id="search"
             style="border-radius: 10px;"
             class="search_box"
             :style="$general.loadConfiguration() ">
          <div class="search_warp flex_box">
            <div @click="search();"
                 class="search_btn flex_box flex_align_center flex_justify_content">
            </div>
            <form class="flex_placeholder flex_box flex_align_center search_input"
                  action="javascript:return true;">
              <input id="searchInput"
                     class="flex_placeholder"
                     :style="$general.loadConfiguration(-1)"
                     placeholder="请输入搜索内容"
                     maxlength="100"
                     type="search"
                     ref="btnSearch"
                     @keyup.enter="search()"
                     v-model="seachText" />
              <div v-if="seachText"
                   @click="seachText='';search();"
                   class="search_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                          :color="'#ccc'"
                          :name="'clear'"></van-icon>
              </div>
            </form>
          </div>
        </div>
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <template v-if="item.value === 'my'">
              <!--数据列表-->
              <ul class="vue_newslist3_box">
                <div v-for="(item,index) in mylistData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <van-cell clickable
                            class="vue_newslist3_item "
                            @click="mydetails(item,2)">
                    <div class="flex_box">
                      <div class="flex_placeholder vue_newslist_warp">
                        <div class="vue_newslist_title text_two"
                             :style="$general.loadConfiguration(1)">
                          <span v-if="item.isTop == '1'"
                                class="vue_newslist_top"
                                :style="$general.loadConfiguration(-4)">
                            <van-tag plain
                                     :color="appTheme">置顶</van-tag>
                          </span>
                          <span class="inherit"
                                v-html="item.name"></span>
                        </div>
                        <div class="flex_box flex_align_center">
                          <div class="vue_newslist_time"
                               :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.time}}</div>
                          <div class="flex_placeholder"></div>
                          <span v-if="item.status"
                                class="social_status"
                                :style="$general.loadConfiguration(-3)+'margin-left:24px;color:'+getStatusColor(item.status,1)">
                            <van-tag :color="getStatusColor(item.status,0)">{{item.status}}</van-tag>
                          </span>
                        </div>
                      </div>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </template>
            <template v-else>
              <!--数据列表-->
              <ul class="vue_newslist3_box">
                <div v-for="(item,index) in draftsListData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <van-cell clickable
                            class="vue_newslist3_item "
                            @click="draftsdetails(item)">
                    <div class="flex_box">
                      <div class="flex_placeholder vue_newslist_warp">
                        <div class="vue_newslist_title text_two"
                             :style="$general.loadConfiguration(1)">
                          <span v-if="item.isTop == '1'"
                                class="vue_newslist_top"
                                :style="$general.loadConfiguration(-4)">
                            <van-tag plain
                                     :color="appTheme">置顶</van-tag>
                          </span>
                          <span class="inherit"
                                v-html="item.name"></span>
                        </div>
                        <div class="flex_box flex_align_center">
                          <div class="vue_newslist_time"
                               :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.time}}</div>
                          <div class="flex_placeholder"></div>
                          <van-icon @click.stop="socialDel(item,index,false)"
                                    color="#CCCCCC"
                                    :size="(($general.appFontSize+2)*1)+'px'"
                                    name="delete-o"></van-icon>
                          <span v-if="item.status"
                                class="social_status"
                                :style="$general.loadConfiguration(-3)+'margin-left:24px;color:'+getStatusColor(item.status,1)">
                            <van-tag :color="getStatusColor(item.status,0)">{{item.status}}</van-tag>
                          </span>
                        </div>
                      </div>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </template>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
                   showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Dialog, Toast, Tag } from 'vant'
import moment from 'moment'

export default {
  name: 'myList',
  components: {
    [Tag.name]: Tag,
    [Dialog.Component.name]: Dialog.Component,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      switchs: { value: 'all', data: [{ label: '我的', value: 'my' }, { label: '草稿箱', value: 'drafts' }] },
      mylistData: [],
      draftsListData: []
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      data.pageNo = 1
      data.mylistData = []
      data.draftsListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getMyList()
      getdraftsList()
    })
    const search = () => {
      data.pageNo = 1
      data.mylistData = []
      data.draftsListData = []
      data.loading = true
      data.finished = false
      getMyList()
      getdraftsList()
    }
    onMounted(() => {
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.mylistData = []
      data.draftsListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getMyList()
      getdraftsList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取我的列表
    const getMyList = async () => {
      const res = await $api.speechMaterial.findMyConferenceSpeechs({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText
      })
      var { data: list, total } = res
      console.log('我的列表====', list)
      list.forEach(item => {
        item.time = item.speechDate ? moment(item.speechDate).format('YYYY-M-D') : ''
        item.status = item.speechStateName
      })
      data.mylistData = data.mylistData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // // 数据全部加载完成
      if (data.mylistData.length >= total) {
        data.finished = true
      }
    }
    // 获取草稿箱列表
    const getdraftsList = async () => {
      const res = await $api.speechMaterial.findConferenceSpeechDrafts({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText,
        speechState: 10
      })
      var { data: list, total: total1 } = res
      console.log('草稿箱列表====', list)
      list.forEach(item => {
        item.time = item.speechDate ? moment(`${item.speechDate}`).format('YYYY-MM-DD') : '' // 时间
      })
      data.draftsListData = data.draftsListData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.draftsListData.length >= total1) {
        data.finished = true
      }
    }
    // 删除草稿箱
    const socialDel = async (_item) => {
      Dialog.confirm({
        title: '提示',
        message: '您将删除本条数据！'
      }).then(async () => {
        // on confirm
        const res = await $api.speechMaterial.conferencespeechDels({ ids: _item.id })
        if (res) {
          Toast('删除成功')
          onRefresh()
        }
      }).catch(function () {
        // on cancel
      })
    }
    // 获取各状态颜色   0背景 1颜色
    const getStatusColor = (_status, _type) => {
      var colors = {
        征稿中: ['#E8F7FF', '#3E9DFF'],
        已采纳: ['#E8F7FF', '#3E9DFF'],
        未开始: ['#FFF6D0', '#F6AE31']
      }
      try {
        return colors[_status][_type]
      } catch (e) {
        return _type === 0 ? '#eee' : '#999'
      }
    }
    // 进入我的详情
    const mydetails = (row, type) => {
      router.push({ name: 'speechMaterialDetails', query: { id: row.id, type: type } })
    }
    // 进入草稿箱的详情
    const draftsdetails = (row, type) => {
      router.push({ name: 'speechMaterialNew', query: { id: row.id } })
    }

    return { ...toRefs(data), onRefresh, onLoad, $general, mydetails, draftsdetails, search, getStatusColor, socialDel }
  }
}
</script>
<style lang="less" scoped>
.myList {
  background: #f8f8f8;
  .social_status .van-tag {
    border-radius: 3px;
    padding: 2px 8px;
    color: inherit;
  }
}
</style>
