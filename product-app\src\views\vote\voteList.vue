<template>
  <div class="voteList">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      success-text="刷新成功">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul v-if="listData.length != 0"
            class="vue_newslist3_box">
          <div v-for="(item,index) in listData"
               :key="index"
               class="vue_newslist3_warp">
            <van-cell clickable
                      class="vue_newslist3_item"
                      @click="details(item)">
              <div v-if="voteredIds.includes(item.id)"
                   style="right:8px;"
                   class="notRead"></div>
              <div class="flex_box flex_align_center">
                <div class="vue_newslist3_status"
                     :style="$general.loadConfigurationSize(-5)+$general.getColorStatus(item.state,'bg')"></div>
                <div class="vue_newslist3_status_text flex_placeholder"
                     :style="$general.loadConfiguration(-2)+$general.getColorStatus(item.state,'text')">{{item.state}}</div>
                <div class="vue_newslist3_time"
                     :style="$general.loadConfiguration(-3)+'font-family: Source Han Serif SC;'">{{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}</div>
              </div>
              <div class="vue_newslist3_title"
                   :style="$general.loadConfiguration()"
                   v-html="item.title"></div>
              <div class="flex_box "
                   :class="!item.hasManagement?'flex_align_center':'flex_align_end'"
                   :style="'margin-top:'+(item.hasManagement?'7':'20')+'px;'">
                <div class="flex_placeholder"
                     :style="$general.loadConfiguration(-3)+'color:#999;'">查看详情</div>
                <div v-if="item.hasManagement"
                     :style="$general.loadConfiguration(-3)">
                  <van-button @click.stop="openManagement(item)"
                              round
                              type="info"
                              size="large"
                              :color="appTheme">活动管理</van-button>
                </div>
                <van-icon v-else
                          :size="((appFontSize-5)*0.01)+'rem'"
                          color="#999"
                          name="arrow"></van-icon>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
                   showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag } from 'vant'

export default {
  name: 'voteList',
  components: {
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      listData: [],
      voteredIds: []
    })
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getVoteList()
    }
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getVoteList()
      getMeetRedIds()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取列表未读红点
    const getMeetRedIds = async () => {
      const res = await $api.Networkpolitics.getRedIds({ type: 'vote' })
      data.voteredIds = res ? res.data || [] : []
    }
    // 获取投票列表
    const getVoteList = async () => {
      const res = await $api.vote.voteList({
        pageNo: 1,
        pageSize: 99,
        isPublish: 1,
        publishSet: 1,
        keyword: data.seachText
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.time = item.startTime || '' // 时间
        item.state = item.voteStatusValue
        item.title = (item.theme || '').replace(new RegExp(data.seachText, 'g'), data.seachText ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>' : '')
      })
      data.listData = data.listData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 详情
    const details = (row) => {
      router.push({ name: 'voteDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), dayjs, onRefresh, onLoad, $general, search, details }
  }
}
</script>
<style lang="less" scoped>
.voteList {
  background: #f8f8f8;
  .social_status {
    color: #b8b8b8;
  }
  .footer_box {
    position: fixed;
    right: 25px;
    bottom: 30px;
    border-radius: 50%;
    padding: 8px 12px;
    background-color: #3088fe;
    .footer_nBtn_box {
      color: #fff;
    }
  }
}
</style>
