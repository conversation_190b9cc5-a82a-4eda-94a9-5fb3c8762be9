<template>
  <div class="CPPCCJournalList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div @click="openSearch()">
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text=""
                offset="52"
                @load="onLoad"
                :immediate-check="false">
        <!--数据列表-->
        <div class="item_body_warp">
          <div class="item_body"
               v-for="(item,index) in classification"
               :key="index">
            <div class="flex_box flex_align_center"
                 style="padding: 10px;">
              <div class="flex_placeholder"
                   :style="$general.loadConfiguration(3)+'font-weight: bold;color: #222;'"
                   v-html="item.name"></div>
              <div @click="openBookStoreDetails(item)"
                   class="flex_box flex_align_center">
                <div :style="$general.loadConfiguration(-4)+'font-weight: bold;color:#999'">{{'查看更多'}}</div>
                <van-icon style="margin-left: 3px;"
                          :size="((appFontSize-4)*0.01)+'rem'"
                          :color="'#999'"
                          :name="'arrow'"></van-icon>
              </div>
            </div>
            <div class="itemSex_box flex_box T-flex-flow-row-wrap">
              <div @click="openBookDetails(nItem)"
                   v-for="(nItem,nIndex) in item.data"
                   :key="nIndex"
                   class="itemSex_item">
                <div :style="$general.loadConfigurationSize([80,115])+'position: relative;'">
                  <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                       :src="nItem.img.url" />
                </div>
                <div v-if="nItem.name"
                     class="itemSex_name text_two"
                     :style="$general.loadConfiguration(-1)"
                     v-html="nItem.name"></div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'CPPCCJournalList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      classification: []
    })
    if (data.title) {
      document.title = data.title
    }
    const search = () => {
      data.pageNo = 1
      data.classification = []
      data.loading = true
      data.finished = false
      typeSexList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.classification = []
        data.loading = true
        data.finished = false
        typeSexList()
      }, 520)
    }
    onMounted(() => {
      typeSexList()
    })
    const onLoad = () => {
      // data.pageNo = data.pageNo + 1
      // typeSexList()
    }
    // 列表请求
    const typeSexList = async () => {
      const res = await $api.CPPCCJournal.typeSexList()
      var { data: list, total } = res
      list.forEach(function (_nItem, _nIndex, _nArr) { // item index 原数组对象
        var nItem = { id: _nItem.firstTypeId || '', name: _nItem.firstTypeName || '', data: [] }
        var books = _nItem.books || []
        books.forEach(function (_nItem, _nIndex, _nArr) {
          var item = { img: { url: _nItem.coverImgUrl || '' }, txt: { url: _nItem.bookContentUrl || '', state: 0, schedule: -1, name: _nItem.bookName || '', bookType: _nItem.bookType || '' } }
          item.id = _nItem.id || '' // 书本id
          item.name = _nItem.bookName || '' // 书名
          item.author = _nItem.authorName || '' // 书作者
          item.summary = _nItem.bookDescription || '' // 书简单
          nItem.data.push(item)
        })
        if (nItem.name.indexOf('政协期刊') !== -1) {
          data.classification = data.classification.concat(nItem)
        }
      })
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.classification.length >= total) {
        data.finished = true
      }
    }
    const onClickLeft = () => history.back()
    // 详情
    const openBookDetails = (row) => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    // 更多
    const openBookStoreDetails = (row) => {
      router.push({ name: 'libraryDetails', query: { id: row.id, title: row.name } })
    }
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    return { ...toRefs(data), dayjs, search, onClickLeft, openSearch, onRefresh, onLoad, openBookDetails, openBookStoreDetails, $general }
  }
}
</script>
<style lang="less">
.CPPCCJournalList {
  width: 100%;
  min-height: 100%;
  background: #f8f8f8;
  .itemSex_box {
    margin: 0 -5px;
  }
  .itemSex_item {
    width: 33.33%;
    padding: 0 5px 4px 5px;
  }
  .itemSex_name {
    color: #222;
    font-weight: 500;
    margin-top: 0rem;
    padding-left: 4px;
  }
}
</style>
