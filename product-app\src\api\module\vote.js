import { HTTP } from '../http.js'
class vote extends HTTP {
  // 投票列表
  voteList (params) {
    return this.request({ url: '/qdvote/appList?', data: params })
  }

  // 投票详情
  voteInfo (params) {
    return this.request({ url: `/qdvote/appInfo/${params}` })
  }

  // 详情里的投票列表
  voteoptionsList (params) {
    return this.request({ url: '/qdvoteoptions/appList', data: params })
  }

  // 投票
  voterecordAdd (params) {
    return this.request({ url: '/voterecord/add', data: params })
  }
}
export {
  vote
}
