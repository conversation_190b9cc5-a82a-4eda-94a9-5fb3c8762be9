<template>
  <div class="activityNew">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <!--  标题 -->
        <van-field v-model="form.meetName"
                   required
                   name="标题"
                   show-word-limit
                   label="标题"
                   placeholder="请输入社情民意标题"
                   :rules="rules.meetName" />
        <!-- 稿件类型 -->
        <van-field @click="pickerOpen()"
                   required
                   size="large"
                   label="稿件类别"
                   v-model="form.Category"
                   clearable
                   placeholder="点击选择稿件类别"
                   :readonly="true"></van-field>
        <van-popup v-model:show="showtype"
                   :style="{height:'40%'}"
                   round
                   position="bottom">
          <van-picker ref="picker"
                      show-toolbar
                      @cancel="showtype = false;"
                      :default-index="defaultIndex"
                      @confirm="pickerConfirm"
                      title="稿件类别"
                      @change="onChange"
                      :columns="columns"
                      item-height="44px">
          </van-picker>
        </van-popup>
        <!-- 反映人身份 -->
        <van-cell style="margin: 20px 0;">
          <van-radio-group v-model="form.submiterType"
                           class="flex_box flex_align_center">
            <span class="text_one2 van-field__label">反映人身份</span>
            <div class="flex_placeholder flex_box T-flex-flow-row-wrap inherit"
                 v-for="(nItem,nIndex) in identity"
                 :key="nIndex">
              <van-radio v-if="showThis?form.submiterType==nItem.value:true"
                         :disabled="disabled"
                         :checked-color="appTheme"
                         :name="nItem.value">
                <template>
                  <!-- #icon="props" -->
                  <!-- {{ props.checked }}{{ !disabled }} -->
                  <!-- :color="props.checked&&!disabled?appTheme:'#C8C8C8'" -->
                  <van-icon class-prefix="iconfont"
                            color="#C8C8C8"
                            :size="((appFontSize+1)*0.01)+'rem'"
                            class="icon iconfont icon-xuanzhong"
                            style="font-size: 16px;position: absolute;top: 0px;left: 105px;">
                  </van-icon>
                </template>
                <span style="font-size: 16px;position: absolute;top: 0px;left: 125px;">{{nItem.text}}</span>
              </van-radio>
            </div>
          </van-radio-group>
        </van-cell>
        <!-- 第一反应人 -->
        <van-field v-model="form.spokesman"
                   name="第一反映人"
                   label="第一反映人"
                   disabled
                   placeholder="请输入第一反映人" />
        <!-- 层次 -->
        <!-- <van-cell center
                  style="margin-left: 10px;"
                  required>
          <div class="flex_box flex_align_center inherit">
            <span class="text_one2 inherit van-field__label">层次</span>
            <div class="flex_placeholder flex_box inherit">
              <van-dropdown-menu class="add_select"
                                 :active-color="appTheme">
                <van-dropdown-item style="width:100%;"
                                   @change="selChange()"
                                   v-model="form.level"
                                   :options="levelData"></van-dropdown-item>
              </van-dropdown-menu>
            </div>
          </div>
        </van-cell> -->
        <van-cell center
                  title="层次"
                  :required="true"
                  style="margin-left: 4px;">
          <!-- 使用 right-icon 插槽来自定义右侧图标 -->
          <template #right-icon>
            <van-dropdown-menu :active-color="appTheme">
              <van-dropdown-item v-model="form.level"
                                 :options="levelData"
                                 @change="levelChange" />
            </van-dropdown-menu>
          </template>
        </van-cell>
        <!-- 正文 -->
        <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="正文"
                   rows="6"
                   maxlength="2000"
                   show-word-limit
                   type="textarea"
                   placeholder="添加正文"
                   :rules="rules.content" />
      </van-cell-group>
      <div class="newButton">
        <div v-if="showDrafts==='true'"
             class="T-flexbox-vertical flex_align_center flex_justify_content click"
             style="padding: 0 13px 0 8px;"
             @click="oneReset(0)"
             native-type="submit">
          <img :style="$general.loadConfigurationSize(14)"
               src="../../assets/img/socialimg/icon_draft.png" />
          <div :style="$general.loadConfiguration(-6)+'color: #888;'">存草稿</div>
        </div>
        <div class="flex_placeholder">
          <van-button round
                      size="large"
                      color="#3088FE"
                      native-type="submit">
            提交</van-button>
        </div>
      </div>
    </van-form>
  </div>
</template>
<script>
import { onMounted, reactive, ref, toRefs, inject } from 'vue'
import { DatetimePicker, DropdownMenu, DropdownItem, Toast, Dialog, Popup } from 'vant'
import { useRouter, useRoute } from 'vue-router'
export default {
  name: 'activityNew',
  components: {
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem

  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $general = inject('$general')
    const $api = inject('$api')
    const user = ref(null)
    const $appTheme = inject('$appTheme')
    const picker = ref(null)
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      appTheme: $appTheme,
      form: {
        meetName: '', // 标题
        Category: '', // 稿件类别
        listdata1: '',
        listdata2: '',
        submiterType: '', // 反映人身份
        spokesman: '', // 第一反映人
        content: '', // 内容
        firstCategory: '',
        secondCategory: '',
        level: ''
      },
      levelData: [{ text: '请选择', value: '' }],
      array: [],
      showThis: true,
      disabled: true,
      id: route.query.id,
      defaultIndex: 0,
      showtype: false,
      type: route.query.type,
      showDrafts: route.query.showDrafts,
      sectioncategory: {},
      rules: {
        meetName: [{ required: true, message: '请输入社情民意标题' }],
        signBeginTime: [{ required: true, message: '请选择稿件类别' }],
        content: [{ required: true, message: '请输入内容' }]
      },
      first: '',
      identity: [ // 反映人身份
        { text: '委员', value: '1' },
        { text: '党派成员', value: '2' },
        { text: '特邀信息员', value: '3' },
        { text: '区县政协', value: '4' },
        { text: '其他', value: '5' }
      ],
      processedCities: {}
    })
    onMounted(() => {
      if (data.user.otherInfo.userOtherInfo.isRepresenter || data.user.otherInfo.userOtherInfo.isMember) { // 是委员
        data.form.submiterType = '1'
      } else if (data.user.otherInfo.userOtherInfo.isSocailSpecialWorker) {
        data.form.submiterType = '3'
      }
      data.form.spokesman = data.user.userName
      getSectionCategory()
      pubkvs()
      if (data.id) {
        getInfo()
      }
    })
    const pubkvs = async () => {
      var { data: info } = await $api.general.pubkvs({ types: 'opinion_level_type' })
      data.levelData = []
      const newData = [{ text: '请选择', value: '' }]
      info.opinion_level_type.forEach(element => {
        newData.push({ text: element.value, value: element.id })
      })
      data.levelData = data.levelData.concat(newData)
    }
    const levelChange = (e) => {
      console.log('e====>', e)
    }
    // 详情回显并编辑提交
    const getInfo = async () => {
      const res = await $api.social.socialinfoInfo(data.id)
      data.form.meetName = res.data.titile
      data.form.level = res.data.level
      data.form.listdata1 = res.data.firstCategory
      data.form.listdata2 = res.data.secondCategory
      const first1 = data.array.find(v => v.id === data.form.listdata1)
      if (first1) {
        const smallId = first1.children.find(v => v.id === data.form.listdata2)
        data.form.Category = (`${first1.name} ${smallId.name}`)
      }
      data.form.submiterType = res.data.submiterType
      data.form.spokesman = res.data.firstReflecterName
      var content = res.data.content.replaceAll(/<[^>]+>/g, '')
      data.form.content = content
    }
    // 提交
    const onSubmit = async (values) => {
      if (data.id && data.type === 'edit') {
        const res = await $api.social.editSocial({
          empty: '1',
          id: data.id,
          reflecterType: 1,
          phoneNumber: data.user.mobile,
          firstReflecterId: data.user.id,
          firstReflecterName: data.form.spokesman,
          processStatus: 39,
          titile: data.form.meetName,
          firstCategory: data.form.firstCategory,
          secondCategory: data.form.secondCategory,
          submiterType: data.form.submiterType,
          content: data.form.content,
          level: data.form.level
        })
        var { errcode, errmsg } = res
        if (errcode === 200) {
          Toast.success(errmsg)
          router.go(-1)
        }
      } else {
        const res = await $api.social.addSocial({
          empty: '1',
          reflecterType: 1,
          phoneNumber: data.user.mobile,
          firstReflecterId: data.user.id,
          firstReflecterName: data.form.spokesman,
          processStatus: 39,
          titile: data.form.meetName,
          firstCategory: data.form.firstCategory,
          secondCategory: data.form.secondCategory,
          submiterType: data.form.submiterType,
          content: data.form.content,
          level: data.form.level
        })
        console.log(res)
        var { errcode: errcodes, errmsg: errmsgs } = res
        if (errcodes === 200) {
          Toast.success(errmsgs)
          router.go(-1)
        }
      }
    }
    // 草稿箱
    const oneReset = (value) => {
      if (value === 0) {
        Dialog.confirm({
          title: '',
          message: '确定要存草稿吗?'
        }).then(async () => {
          if (data.id && data.type === 'edit') {
            const res = await $api.social.editSocial({
              empty: '1',
              id: data.id,
              reflecterType: 1,
              phoneNumber: data.user.mobile,
              firstReflecterId: data.user.id,
              firstReflecterName: data.form.spokesman,
              processStatus: 20,
              titile: data.form.meetName,
              firstCategory: data.form.firstCategory,
              secondCategory: data.form.secondCategory,
              submiterType: data.form.submiterType,
              content: data.form.content
            })
            var { errcode, errmsg } = res
            if (errcode === 200) {
              Toast.success(errmsg)
              router.go(-1)
            }
          } else {
            const res = await $api.social.addSocial({
              empty: '1',
              reflecterType: 1,
              phoneNumber: data.user.mobile,
              firstReflecterId: data.user.id,
              firstReflecterName: data.form.spokesman,
              processStatus: 20,
              titile: data.form.meetName,
              ficrstCategory: data.form.firstCategory,
              seondCategory: data.form.secondCategory,
              submiterType: data.form.submiterType,
              content: data.form.content
            })
            var { errcode: errcodes, errmsg: errmsgs } = res
            if (errcodes === 200) {
              Toast.success(errmsgs)
              router.go(-1)
            }
          }
        }).catch(() => {
          // on cancel
        })
      }
    }
    // 多级选择 打开
    const pickerOpen = async () => {
      data.showtype = true
    }
    const columns = []
    let oldDatas = null
    const processedCities = {}
    // 获取稿件类别
    const getSectionCategory = async () => {
      const res = await $api.social.treelist({ treeType: 6 })
      console.log('获取稿件类别', res)
      var treeList = res.data || []
      data.array = treeList
      oldDatas = treeList
      treeList.forEach(city => {
        const categoryName = city.label
        const subCategories = city.children.map(subCategory => subCategory.label)
        processedCities[categoryName] = subCategories
      })
      const categoryNames = Object.keys(processedCities)
      columns.push({ values: categoryNames }, { values: processedCities[categoryNames[0]] })
    }
    const onChange = (values) => {
      const subCategories = processedCities[values[0]]
      picker.value.setColumnValues(1, subCategories)
    }
    const pickerConfirm = (value) => {
      let bigId = ''
      let smallId = ''
      const selectedCategory = oldDatas.find(category => category.label === value[0])
      console.log('selectedCategory==>', selectedCategory)
      if (selectedCategory) {
        bigId = selectedCategory.id
        const selectedSubCategory = selectedCategory.children.find(subCategory => subCategory.label === value[1])
        smallId = selectedSubCategory ? selectedSubCategory.id : ''
      }
      console.log(bigId, smallId)
      data.form.Category = (`${value[0]} ${value[1]}`)
      data.form.firstCategory = bigId
      data.form.secondCategory = smallId
      data.showtype = false
    }
    return { ...toRefs(data), $general, user, onSubmit, oneReset, getSectionCategory, pickerOpen, onChange, columns, picker, pickerConfirm, levelChange }
  }
}
</script>
<style lang="less" >
.activityNew {
  background: #fff;
  .van-form {
    width: 100%;
    .van-cell-group--inset {
      margin: 0;
      .van-field {
        margin: 20px 0;
      }
    }
    .newContent {
      flex-wrap: wrap;
      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }
      .van-field__body {
        background-color: #f5f5f5;
        padding: 6px 12px;
      }
    }
    .van-field__control {
      font-size: 14px;
    }
    .time-box {
      .van-cell__title {
        width: 120px;
      }
    }
    .activity-box {
      .van-cell__title {
        width: 220px !important;
      }
    }
  }
  .titleStyle {
    background-color: #fff;
    height: 48px;
    // width: 100%;
    width: 50%;
  }
  .van-dropdown-menu__title {
    margin-right: 15px;
  }
  --van-dropdown-menu-box-shadow: 0;
  .newButton {
    display: flex;
    justify-content: space-around;
    padding: 36px 18px;
    // padding-bottom: 88px;
    .van-button {
      // width: 128px;
      // height: 36px;
    }
  }
  .van-radio__icon .van-icon {
    width: 1em;
    height: 1em;
    line-height: 1;
  }
}
</style>
