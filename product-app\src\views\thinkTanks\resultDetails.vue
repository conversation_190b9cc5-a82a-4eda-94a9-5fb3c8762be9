<template>
  <div class="resultDetails">
    <div class="vue_newslist_item ">
      <div class="flex_box">
        <div class="flex_placeholder vue_newslist_warp">
          <div class="vue_newslist_title"
               :style="$general.loadConfiguration(6)+'font-weight:bold;line-height:1.5;'">
            {{title}}
          </div>
          <div class="flex_box flex_align_center flex_align_box">
            <div class="vue_newslist_source"
                 :style="$general.loadConfiguration(-2)">{{org}}</div>
            <div class="flex_placeholder"></div>
            <div class="
                 vue_newslist_time"
                 :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">
              {{time}}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 展示内容content -->
    <div class="n_details_content"
         :style="$general.loadConfiguration(1)"
         v-html="content"></div>
    <!--展示附件-->
    <!-- <template v-if="attachInfo.data.length != 0">
      <van-cell class="list_item"
                center
                :title="attachInfo.name + '('+attachInfo.data.length+')'"></van-cell>
      <div class="add_warp attach_warp">
        <van-swipe-cell v-for="(nItem,nIndex) in attachInfo.data"
                        :key="nIndex">
          <van-cell :border="false"
                    :class="nItem.state==2?'cache':'cache'"
                    @click="annexClick(nItem)"
                    :title="nItem.name"></van-cell>
        </van-swipe-cell>
      </div>
    </template> -->
    <template v-if="attachInfo.data&&attachInfo.data.length != 0">
      <div class="general_attach"
           style="background-color: #fff;">
        <!-- @click="annexClick(item,false)" -->
        <div v-for="(item,index) in attachInfo.data"
             :key="index"
             @click="annexClick(item)"
             class="general_attach_item flex_box flex_align_center click">
          <img class="general_attach_icon"
               :style="$general.loadConfigurationSize([5,7])"
               :src="require(`../../assets/fileicon/${item.iconInfo.name}`)" />
          <div class="flex_placeholder flex_box flex_align_center">
            <div class="general_attach_name text_one2"
                 style="font-size: 14px;display: -webkit-box;">{{item.name}}</div>
            <div class="general_attach_size"
                 style="font-size: 12px;">{{$general.getFileSize(item.size)}}</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
/* eslint-disable */
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'resultDetails',
  components: {
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      id: route.query.id,
      title: '',
      time: '',
      content: '',
      attachInfo: { name: '附件', data: [] }
    })

    onMounted(() => {
      getInfo()
    })

    const getInfo = async () => {
      const res = await $api.thinkTanks.assemblyInfo(data.id)
      var { data: info } = res
      console.log('info===>', info)
      data.id = info.id || ''
      data.title = info.name
      data.time = info.pubDate
      data.content = info.content
      var attachmentList = info.attachmentList || []
      if (attachmentList.length != 0) {
        attachmentList.forEach(function (item) {
          item.url = item.filePath || ''
          item.name = item.fileName || ''
          item.size = item.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          data.attachInfo.data.push(item)
        })
      }
    }

    const annexClick = (item) => {
      // if (item.iconInfo.type === 'pdf') {
      //   if (window.location.origin === 'http://**************:81') {
      //     window.open('http://**************/pdf/web/viewer.html?file=' + item.url)
      //   } else {
      //     window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.url)
      //   }
      // } else {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
      // }
    }
    return { ...toRefs(data), $general, annexClick }
  }
}
</script>

<style lang="less" >
.resultDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;
  .info_title {
    font-weight: bold;
    font-size: 18px;
    margin: 20px 15px;
  }
  .info_time {
    font-size: 14px;
    color: #8a8a8a;
  }
  .info_content {
    text-indent: 2em;
    margin: 10px 20px;
    line-height: 28px;
  }
}
</style>

