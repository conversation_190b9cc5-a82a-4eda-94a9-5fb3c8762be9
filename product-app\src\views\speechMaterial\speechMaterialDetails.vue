<template>
  <div class="speechMaterialDetails">
    <div class="vue_newslist_item ">
      <div class="flex_box">
        <div class="flex_placeholder vue_newslist_warp">
          <div class="vue_newslist_title"
               :style="general.loadConfiguration(6)+'font-weight:bold;line-height:1.5;'">
            {{title}}
          </div>
          <div class="flex_box flex_align_center flex_align_box">
            <div class="vue_newslist_source"
                 :style="general.loadConfiguration(-2)">{{org}}</div>
            <div class="flex_placeholder"></div>
            <div class="
                 vue_newslist_time"
                 :style="general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">
              {{dataTime}}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 展示内容content -->
    <div class="n_details_content"
         :style="general.loadConfiguration(1)"
         v-html="content"></div>
    <!--展示附件-->
    <template v-if="attachInfo.data&&attachInfo.data.length != 0">
      <div class="general_attach"
           style="background-color: #fff;">
        <!-- @click="annexClick(item,false)" -->
        <div v-for="(item,index) in attachInfo.data"
             :key="index"
             @click="annexClick(item)"
             class="general_attach_item flex_box flex_align_center click">
          <img class="general_attach_icon"
               :style="general.loadConfigurationSize([5,7])"
               :src="require(`../../assets/fileicon/${item.iconInfo.name}`)" />
          <div class="flex_placeholder flex_box flex_align_center">
            <div class="general_attach_name text_one2"
                 style="font-size: 14px;display: -webkit-box;">{{item.name}}</div>
            <div class="general_attach_size"
                 style="font-size: 12px;">{{general.getFileSize(item.size)}}</div>
          </div>
        </div>
      </div>
    </template>
    <footer class="footer"
            :style="general.loadConfiguration()">
      <div class="flex_box">
        <div v-for="(item,index) in footerBigBtns"
             :key="index"
             :style="general.loadConfiguration(-2)+'width:100%;padding: 0 0.12rem;'">
          <van-button :icon="item.icon"
                      :icon-prefix="item.prefix"
                      round
                      size="large"
                      :color="item.color||appTheme"
                      @click="footerBtnClick(item)">{{item.name}}</van-button>
        </div>
      </div>
    </footer>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, SwipeCell } from 'vant'
export default {
  name: 'speechMaterialDetails',
  components: {
    [VanImage.name]: VanImage,
    [SwipeCell.name]: SwipeCell,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      id: route.query.id,
      type: route.query.type,
      title: '', // 标题
      dataTime: '', // 时间
      org: '', // 发布部门
      status: '',
      content: '', // 正文内容
      footerBigBtns: [],
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    onMounted(() => {
      if (data.type === '1') {
        getNoticeInfo()
      }
      if (data.type === '2') {
        getPublicInfo()
      }
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataInfo = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      if (data.type === '1') {
        getNoticeInfo()
      }
      if (data.type === '2') {
        getPublicInfo()
      }
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取征稿通知列表
    const getNoticeInfo = async () => {
      const res = await $api.speechMaterial.speechnoticeInfo(data.id)
      var { data: info } = res
      data.title = info.name
      data.org = info.orgName
      data.dataTime = dayjs(info.startTime).format('YYYY-MM-DD') + '至' + dayjs(info.endTime).format('YYYY-MM-DD')
      data.content = info.content
      data.status = dayjs().isBefore(dayjs(info.startTime)) ? '未开始' : dayjs().isAfter(dayjs(info.endTime)) ? '已结束' : '征稿中'
      if (data.status === '征稿中') {
        data.footerBigBtns.push({ name: '提交大会发言', click: 'speechMaterial', color: '' })
      }
      var attachmentList = info.attachmentList || []
      data.attachInfo.data = []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          data.attachInfo.data.push(item)
        })
      }
      console.log('附件信息======', data.attachInfo)
    }
    // 获取公开发言列表
    const getPublicInfo = async () => {
      const res = await $api.speechMaterial.conferencespeechInfo(data.id)
      var { data: info } = res
      data.title = info.name
      data.org = info.userName
      data.dataTime = info.speechDate
      data.content = info.content
      var attachmentList = info.attachmentList || []
      data.attachInfo.data = []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          data.attachInfo.data.push(item)
        })
      }
    }
    const annexClick = (item) => {
      // if (item.iconInfo.type === 'pdf') {
      //   if (window.location.origin === 'http://59.224.134.152:81') {
      //     window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + item.url)
      //   } else {
      //     window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.url)
      //   }
      // } else {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
      // }
    }
    // footer底部提交按钮
    const footerBtnClick = async (_item) => {
      console.log('_item', _item)
      switch (_item.click) {
        case 'speechMaterial':
          router.push({ name: 'speechMaterialNew' })
          break
      }
    }
    return { ...toRefs(data), onRefresh, onLoad, general, footerBtnClick, annexClick }
  }
}
</script>
<style lang="less" scoped>
.speechMaterialDetails {
  background: #fff;
  .n_details_content {
    text-indent: 2em;
    margin: 10px 15px 30px 15px;
    line-height: 26px;
  }
  .footer {
    padding: 10px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }
  .footer .van-button {
    padding: 10px 21px;
    border-color: #e2e2e2;
    font-weight: 600;
  }
}
</style>
