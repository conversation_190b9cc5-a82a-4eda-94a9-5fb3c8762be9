import { HTTP } from '../http.js'
class ImportantWork extends HTTP {
  // 栏目列表
  getColumnList (params) {
    return this.request({ url: '/focuscolumnmodule/list', data: params })
  }

  // 列表
  list (params) {
    return this.request({ url: '/focuscontentmodule/list', data: params })
  }

  // 详情
  info (params) {
    return this.request({ url: `/focuscontentmodule/info/${params}` })
  }
}
export {
  ImportantWork
}
