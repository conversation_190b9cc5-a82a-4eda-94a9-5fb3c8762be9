import { HTTP } from '../http.js'
class questionnaire extends HTTP {
  // 调查问卷列表
  questionnaireList (params) {
    return this.request({ url: '/questionnaire/appList?', data: params })
  }

  // 调查问卷详情
  questionnaireDetails (params) {
    return this.request({ url: `/questionnaire/info/${params}` })
  }

  // 是否可以答题
  questionnaireIsAnswerApp (params) {
    return this.request({ url: '/questionnairerespondents/isAnswerApp', data: params })
  }

  // 提交问卷调查
  questionnaireCommit (params) {
    return this.request({
      url: '/questionnaire/commit',
      data: params,
      method: 'POST',
      header: { 'Content-Type': 'application/json;charset=UTF-8' }
    })
  }
}
export {
  questionnaire
}
