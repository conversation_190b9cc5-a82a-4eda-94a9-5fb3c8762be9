import { HTTP } from '../http.js'
class proposal extends HTTP {
  // 所有提案
  proposalList (params) {
    return this.request({ url: '/proposal/list', data: params })
  }

  // 最全提案详情
  transactProposalDetail (params) {
    return this.request({ url: 'proposal/transactProposalDetail', data: params })
  }

  // 获取提案进程
  nodeInfo (params) {
    return this.request({ url: 'proposal/nodeInfo?', data: params })
  }

  // 沟通情况
  flowContactList (params) {
    return this.request({ url: '/proposal/flowContactList', data: params })
  }

  // 答复件列表
  answerList (params) {
    return this.request({ url: '/proposal/answerList', data: params })
  }

  // 答复件详情
  proposalAnswerDetail (params) {
    return this.request({ url: '/proposal/proposalAnswerDetail?', data: params })
  }

  // 满意度测评详情
  flowEvaluateDetail (params) {
    return this.request({ url: '/proposal/flowEvaluateDetail', data: params })
  }

  // 我领衔的提案
  myProposalList (params) {
    return this.request({ url: '/proposal/myProposalList', data: params })
  }

  // 我联名的提案
  myJoinProposalList (params) {
    return this.request({ url: '/proposal/myJoinProposalList', data: params })
  }

  // 我的草稿提案
  myDraftsProposalList (params) {
    return this.request({ url: '/proposal/myDraftsProposalList', data: params })
  }

  // 获取主题词
  chooseList (params) {
    return this.request({ url: '/submission/proposal/topic/chooseList', data: params })
  }

  // 审查详情接口
  auditDetail (params) {
    return this.request({ url: '/proposal/auditDetail', data: params })
  }
}
export {
  proposal
}
