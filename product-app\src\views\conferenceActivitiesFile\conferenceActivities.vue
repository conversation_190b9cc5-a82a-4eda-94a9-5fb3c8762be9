<template>
  <div class="conferenceActivities ">
    <van-tabs v-model:active="active"
              @change="onRefresh"
              color="#fff"
              swipeable
              title-active-color="#fff"
              title-inactive-color="#fff"
              sticky>
      <van-tab v-for="item in activeData"
               :key="item.id"
               :name="item.id"
               :title="item.value"
               :badge="item.badge>0?item.badge:''">
        <!-- 搜索框 -->
        <div id="search"
             style="border-radius: 10px;"
             class="search_box"
             :style="$general.loadConfiguration() ">
          <div class="search_warp flex_box">
            <div @click="search();"
                 class="search_btn flex_box flex_align_center flex_justify_content">
            </div>
            <form class="flex_placeholder flex_box flex_align_center search_input"
                  action="javascript:return true;">
              <input id="searchInput"
                     class="flex_placeholder"
                     :style="$general.loadConfiguration(-1)"
                     placeholder="请输入搜索内容"
                     maxlength="100"
                     type="search"
                     ref="btnSearch"
                     @keyup.enter="search()"
                     v-model="seachText" />
              <div v-if="seachText"
                   @click="seachText='';search();"
                   class="search_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                          :color="'#ccc'"
                          :name="'clear'"></van-icon>
              </div>
            </form>
          </div>
        </div>
        <van-pull-refresh v-model="refreshing"
                          style="min-height: 80vh;"
                          success-text="刷新成功"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <div class="myProposalListBox"
                 v-show="active==='0'">
              <!--全会-->
              <div v-if="allMeeting.length != 0"
                   class="allmeeting_box">
                <div v-for="(item,index) in allMeeting"
                     :key="index"
                     class="allmeeting_warp">
                  <!--顶上的 总标题-->
                  <div class="allmeeting_top_box flex_box flex_align_center flex_justify_content">
                    <div class="allmeeting_top"
                         :style="$general.loadConfiguration(-1)">全体会议</div>
                  </div>
                  <!--没有展开的时候 只显示第一个大会的名字-->
                  <div v-if="!item.more"
                       @click="item.more = !item.more;window['a'+item.id] = item.more;">
                    <div class="allmeeting_title text_two"
                         :style="$general.loadConfiguration(1)"
                         v-html="item.title"></div>
                    <div class="allmeeting_time text_one2"
                         :style="$general.loadConfiguration(-2)">会期:{{item.startTime.substr(0,10)}} 至 {{item.endTime.substr(5,5)}}</div>
                  </div>
                  <!--展开后显示所有的大会和议程-->
                  <template v-else>
                    <div class="allmeeting_title text_two"
                         :style="$general.loadConfiguration(2)+'text-align: center;'"
                         v-html="item.title"></div>
                    <div :style="$general.loadConfiguration(-1)+'color:#B6B6B6;font-weight: 500;margin-bottom:6px;text-align: center;'"
                         class="text_one2">会期:{{item.startTime.substr(0,10)}} 至 {{item.endTime.substr(5,5)}}</div>
                    <div v-for="(nItem,nIndex) in item.child"
                         :key="nIndex"
                         @click="openDetails(nItem,index)">
                      <div class="allmeeting_title text_two"
                           :style="$general.loadConfiguration()+'font-weight: 400;'"
                           v-html="nItem.title"></div>
                      <div class="flex_box flex_align_center"
                           style="margin-bottom:6px;">
                        <div :style="$general.loadConfiguration(-2)+'color:#B6B6B6;font-weight: 500;margin-right:10px;'"
                             class="flex_placeholder text_one2">{{dayjs(nItem.startTime).format('YYYY')!=dayjs(nItem.endTime).format('YYYY')?dayjs(nItem.startTime).format('YYYY-'):''}}{{dayjs(nItem.startTime).format('MM-DD HH:mm')}} 至 {{dayjs(nItem.startTime).format('YYYY')!=dayjs(nItem.endTime).format('YYYY')?dayjs(nItem.endTime).format('YYYY-'):''}}{{dayjs(nItem.startTime).format('YYYY-MM-DD')!=dayjs(nItem.endTime).format('YYYY-MM-DD')?dayjs(nItem.endTime).format('MM-DD '):''}}{{dayjs(nItem.endTime).format('HH:mm')}}</div>
                        <div class="flex_box flex_align_center"
                             :style="'color:'+(nItem.state=='会议中'?'#E30202':'#B6B6B6')+';'">
                          <div :style="$general.loadConfiguration(-2)+'color:inherit;font-weight: 500;margin-right:3px;line-height: 1.4'">{{nItem.state}}</div>
                          <van-icon :size="(($general.appFontSize-7)*0.01)+'px'"
                                    color="inherit"
                                    name="arrow"></van-icon>
                        </div>
                      </div>
                    </div>
                    <div v-if="allmeetingHasLeave(item) || allmeetingHasSignUp(item) || item.hasManagement"
                         class="allmeeting_btn_box flex_box flex_align_center">
                      <div v-if="allmeetingHasLeave(item)"
                           @click.stop="clickOption(item,'leave')"
                           :style="$general.loadConfiguration(-2)"
                           class="allmeeting_btn click">请 假</div>
                      <div v-if="allmeetingHasSignUp(item)"
                           @click.stop="clickOption(item,'signUp')"
                           :style="$general.loadConfiguration(-2)+(allmeetingHasLeave(item)?('margin-left:'+(item.hasManagement?'8':'27')+'px;'):'')"
                           class="allmeeting_btn click">报 名</div>
                      <div v-if="item.hasManagement"
                           @click.stop="openManagement(item,true)"
                           :style="$general.loadConfiguration(-2)+'margin-left:'+((allmeetingHasLeave(item)&&allmeetingHasSignUp(item))?'8':(allmeetingHasLeave(item)||allmeetingHasSignUp(item))?'27':'0')+'px;'"
                           class="allmeeting_btn click">会务管理</div>
                    </div>
                  </template>
                  <!--底下的查看更多-->
                  <div @click="item.more = !item.more;window['a'+item.id] = item.more;"
                       class="allmeeting_bottom_box flex_box flex_align_center flex_justify_content">
                    <div class="allmeeting_bottom_text"
                         :style="$general.loadConfiguration(-2)">{{item.more?'点击收起':'查看更多'}}</div>
                    <div class="allmeeting_bottom_icon_box flex_box flex_align_center flex_justify_content">
                      <van-icon size="14"
                                color="#E30202"
                                :name="item.more?'arrow-up':'arrow-down'"></van-icon>
                    </div>
                  </div>
                </div>
              </div>
              <!--数据列表-->
              <ul v-if="listData.length != 0"
                  class="vue_newslist3_box">
                <div v-for="(item,index) in listData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <van-cell clickable
                            class="vue_newslist3_item"
                            @click="openDetails(item,index)">
                    <div v-if="meetredIds.includes(item.id)"
                         style="right:8px;"
                         class="notRead"></div>
                    <div class="flex_box flex_align_center">
                      <div class="vue_newslist3_status"
                           :style="$general.loadConfigurationSize(-9)+$general.getColorStatus(item.state,'bg')"></div>
                      <div class="vue_newslist3_status_text flex_placeholder"
                           :style="$general.loadConfiguration(-2)+$general.getColorStatus(item.state,'text')">{{item.state}}<span v-if="item.stateType">({{item.stateType}})</span></div>
                      <div class="vue_newslist3_time"
                           :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}</div>
                    </div>
                    <div class="vue_newslist3_title"
                         :style="$general.loadConfiguration()"
                         v-html="item.title"></div>
                    <div class="flex_box "
                         :class="!item.hasManagement?'flex_align_center':'flex_align_end'"
                         :style="'margin-top:'+(item.hasManagement?'7':'20')+'px;'">
                      <div class="flex_placeholder"
                           :style="$general.loadConfiguration(-3)+'color:#999;'">查看详情</div>
                      <div v-if="item.hasManagement"
                           :style="$general.loadConfiguration(-3)">
                        <van-button @click.stop="openManagement(item)"
                                    round
                                    type="info"
                                    size="large"
                                    color="#3088fe">会务管理</van-button>
                      </div>
                      <van-icon v-else
                                :size="(($general.appFontSize-5)*0.01)+'px'"
                                color="#999"
                                name="arrow"></van-icon>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </div>
            <!-- 活动通知 -->
            <div class="myProposalListBox"
                 v-show="active==='1'">
              <!--数据列表-->
              <ul v-if="listData.length != 0"
                  class="vue_newslist3_box">
                <div v-for="(item,index) in listData"
                     :key="index"
                     class="vue_newslist3_warp">
                  <div class="red-dot"
                       v-if="!item.read"></div>
                  <van-cell clickable
                            class="vue_newslist3_item"
                            @click="openDetails(item)">
                    <div v-if="activityredIds.includes(item.id)"
                         style="right:8px;"
                         class="notRead"></div>
                    <div class="flex_box flex_align_center">
                      <div class="vue_newslist3_status"
                           :style="$general.loadConfigurationSize(-9)+$general.getColorStatus(item.state,'bg')"></div>
                      <div class="vue_newslist3_status_text flex_placeholder"
                           :style="$general.loadConfiguration(-2)+$general.getColorStatus(item.state,'text')">{{item.state}}</div>
                      <div class="vue_newslist3_time"
                           :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}</div>
                    </div>
                    <div class="vue_newslist3_title"
                         :style="$general.loadConfiguration()"
                         v-html="item.title"></div>
                    <div class="flex_box "
                         :class="!item.hasManagement?'flex_align_center':'flex_align_end'"
                         :style="'margin-top:'+(item.hasManagement?'7':'20')+'px;'">
                      <div class="flex_placeholder"
                           :style="$general.loadConfiguration(-3)+'color:#999;'">查看详情</div>
                      <div v-if="item.hasManagement"
                           :style="$general.loadConfiguration(-3)">
                        <van-button @click.stop="activityManagement(item)"
                                    round
                                    type="info"
                                    size="large"
                                    color="#3088fe">活动管理</van-button>
                      </div>
                      <van-icon v-else
                                :size="((appFontSize-5)*0.01)+'rem'"
                                color="#999"
                                name="arrow"></van-icon>
                    </div>
                  </van-cell>
                </div>
              </ul>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
/* eslint-disable */
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'conferenceActivities',
  components: {
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      activeData: [
        { id: '0', value: '会议', badge: 0 },
        { id: '1', value: '活动', badge: 0 }
      ],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      listData: [],
      allMeeting: [],//全会数据
      seachText: '',
      meetredIds: [],
      activityredIds: []
    })

    onMounted(() => {
      onRefresh()
    })
    const onChange = (e) => {

    }
    // 获取列表未读红点
    const getMeetRedIds = async () => {
      const res = await $api.Networkpolitics.getRedIds({ type: 'meet' })
      data.meetredIds = res ? res.data || [] : []
    }
    const getActivityRedIds = async () => {
      const res = await $api.Networkpolitics.getRedIds({ type: 'activity' })
      data.activityredIds = res ? res.data || [] : []
    }
    // 获取会议和活动红点数量
    const getRedPointNum = async () => {
      const ret = await $api.Networkpolitics.getRedPointNumByModule({ module: 'app' })
      const list = ret ? ret.data || [] : []
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          if (_eItem.code === 'meet') {
            data.activeData[0].badge = Number(_eItem.count)
          } else if (_eItem.code === 'activity') {
            data.activeData[1].badge = Number(_eItem.count)
          }
        })
      }
    }

    const onCancel = () => {
      data.pageNo = 1
      myProposalList()
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      myProposalList()
      getRedPointNum()
      getMeetRedIds()
      getActivityRedIds()
    }
    const onLoad = () => {
      data.loading = true
      myProposalList()
    }
    //本次大会是否可以请假
    const allmeetingHasLeave = (_item) => {
      var leave = false;
      (_item.child || []).forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.leave) {
          leave = true;
        }
      });
      return leave;
    }
    //本次大会是否可以报名
    const allmeetingHasSignUp = (_item) => {
      var signUp = false;
      (_item.child || []).forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.signUp) {
          signUp = true;
        }
      });
      return signUp;
    }
    //全会数据
    const findAppConferenceParents = async () => {
      var datas = {
        pageNo: 1,
        pageSize: 999,
        general: 1,
        year: new Date().getFullYear()
      }
      var res = []
      res = await $api.conferenceActivitiesFile.findAppConferenceParents(datas) //全会
      var { data: list, total } = res
      data.allMeeting = []
      if (list && list.length > 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {//item index 原数组对象
          var item = {};
          item.id = _eItem.id || "";//id
          item.title = (_eItem.name || "");
          item.startTime = _eItem.startTime || "";
          item.endTime = _eItem.endTime || "";
          item.hasManagement = _eItem.meetingManagement;//是否有会务 管理
          var conferenceChildFormPos = _eItem.conferenceChildFormPos || [];
          item.more = window["a" + item.id];
          item.child = [];
          conferenceChildFormPos.forEach(function (_uItem, _uIndex, _uArr) {
            var nItem = {};
            nItem.id = _uItem.id || "";//id
            nItem.title = (_uItem.name || "");
            nItem.startTime = _uItem.startTime || "";
            nItem.endTime = _uItem.endTime || "";
            nItem.state = _uItem.state || "";
            nItem.signInCommand = _uItem.signInCommand || "";
            nItem.leave = _uItem.leave;//是否可以请假
            nItem.signUp = _uItem.signUp;//是否可以报名
            item.child.push(nItem);
          });
          data.allMeeting.push(item);
        });

      }
    }
    // 所有普通会议数据
    const myProposalList = async () => {
      var res = []
      if (data.active === '0') {
        var datas = {
          pageNo: data.pageNo,
          pageSize: 10,
          keyword: data.seachText,
          publishSet: "1",
          isTemplate: "0"
        }
        res = await $api.conferenceActivitiesFile.findAppConferences(datas) //全会
        var { data: list, total } = res
        if (list && list.length > 0) {
          list.forEach(function (_eItem, _eIndex, _eArr) {//item index 原数组对象
            _eItem.id = _eItem.id || "";//id
            _eItem.title = _eItem.name || "";
            _eItem.state = _eItem.state || "";
            _eItem.stateType = _eItem.stateType || "";
            _eItem.time = _eItem.startTime || "";
            _eItem.hasManagement = _eItem.meetingManagement;
            _eItem.signInCommand = _eItem.signInCommand || "";
            _eItem.isTop = _eItem.isTop || "0";//置顶 1是0否
            _eItem.read = _eItem.read;
          });
        }
        findAppConferenceParents()
      } else if (data.active === '1') {
        var datas = {
          pageNo: data.pageNo,
          pageSize: 10,
          keyword: data.seachText,
          publishSet: "1"
        }
        res = await $api.conferenceActivitiesFile.findAppActivitys(datas) //活动通知
        var { data: list, total } = res
        list.forEach(function (_eItem, _eIndex, _eArr) {//item index 原数组对象
          _eItem.id = _eItem.id || "";//id
          _eItem.title = _eItem.meetName || "";
          _eItem.state = _eItem.state || "";
          _eItem.stateType = _eItem.stateType || "";
          _eItem.time = _eItem.meetStartTime || "";
          _eItem.hasManagement = _eItem.activityManagement;
          _eItem.signInCommand = _eItem.signInCommand || "";
          _eItem.isTop = _eItem.isTop || "0";//置顶 1是0否
          _eItem.read = _eItem.read;
        })
      }
      if (data.pageNo === 1) {
        data.listData = list
      } else {
        data.listData = data.listData.concat(list)
      }
      data.loading = false
      data.refreshing = false
      data.pageNo = data.pageNo + 1

      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      if (data.active === '0') {
        router.push({ name: 'meetingDetailFile', query: { id: row.id } })
      } else {
        router.push({ name: 'activitesDetailFile', query: { id: row.id } })
      }
    }
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      myProposalList()
    }
    // 会务管理
    const openManagement = (_item, allMeeting) => {
      console.log('_item====>', _item)
      console.log('allMeeting====>', allMeeting)
      var myParam = {};
      myParam.pageType = "page";//打开页面方式	home 首页一级模块	 page 作为页面被打开
      // myParam.pageOnly = T.getNum();//表示页面唯一参数	必传
      myParam.footerH = 0;//底部按钮高度 传home时会有
      myParam.title = "会务管理";
      myParam.allMeeting = allMeeting;//是否全体会议
      myParam.allMeetingChild = _item.child || [];//议程集合
      myParam.meetId = _item.id;
      myParam.signInCommand = _item.signInCommand || "";
      router.push({ name: 'ConferenceAffairsManagement', query: { myParam: JSON.stringify(myParam) } })
    }
    // 活动管理
    const activityManagement = (_item, allMeeting) => {
      var myParam = {};
      myParam.pageType = 'page' //打开页面方式	home 首页一级模块	 page 作为页面被打开
      // myParam.pageOnly = T.getNum(); //表示页面唯一参数	必传
      myParam.footerH = 0 //底部按钮高度 传home时会有
      myParam.title = '活动管理'
      myParam.allMeeting = allMeeting //是否全体会议
      myParam.allMeetingChild = _item.child || [] //议程集合
      myParam.meetId = _item.id
      myParam.signInCommand = _item.signInCommand || ''
      router.push({ name: 'activityManagement', query: { myParam: JSON.stringify(myParam) } })
    }
    return { ...toRefs(data), onChange, onRefresh, dayjs, onLoad, openDetails, onCancel, allmeetingHasSignUp, allmeetingHasLeave, openManagement, activityManagement, $general, search }
  }
}
</script>

<style lang="less" >
.conferenceActivities {
  width: 100%;
  // min-height: 100vh;
  .van-tab__text {
  }
  .van-field__control {
  }
  .van-tabs {
    .van-tabs__nav {
      background: rgb(48, 136, 254);
    }
    // padding-bottom: 50px;
    .van-tabs__content--animated {
      .van-swipe__track {
        min-height: calc(100vh - 200px);
      }
    }
  }
  .myProposalListBox {
    margin-top: 10px;
    width: 100%;
    height: 100%;
    //全会
    .allmeeting_box {
      padding: 14px 14px 0 14px;
      .allmeeting_warp {
        position: relative;
        background: #fff7f7;
        border: 1px solid #e30202;
        border-radius: 10px;
        padding: 28px 10px 0px 10px;
        margin-bottom: 10px;
      }
      .allmeeting_warp + .allmeeting_warp {
        margin-top: 14px;
      }
      .allmeeting_top_box {
        position: absolute;
        top: -1px;
        left: 0;
        right: 0;
      }
      .allmeeting_top {
        background: linear-gradient(72deg, #ff2929 0%, #ff853b 100%);
        border-radius: 0px 0px 6px 6px;
        padding: 5px 29px;
        font-weight: 500;
        color: #ffffff;
      }
      .allmeeting_bottom_box {
        width: 100%;
        padding: 10px;
      }
      .allmeeting_bottom_text {
        font-weight: 500;
        color: #e30202;
        margin-right: 6px;
      }
      .allmeeting_bottom_icon_box {
        background: #ffd8d8;
        border-radius: 2px;
        padding: 1px;
      }
      .allmeeting_title {
        font-weight: bold;
        color: #333333;
        margin: 17px 0 11px 0;
      }
      .allmeeting_time {
        color: #b6b6b6;
        font-weight: 500;
        margin-bottom: 6px;
      }

      .allmeeting_btn_box {
        margin-top: 20px;
        padding: 0 11px;
      }
      .allmeeting_btn {
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 0px 5px 0px #ffcece;
        border-radius: 19px;
        padding: 10px 0;
        text-align: center;
        font-weight: 500;
        color: #333333;
      }
      .red-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #e30202;
        position: absolute;
        top: 5px;
        z-index: 1;
        right: 5px;
      }
      .vue_newslist3_warp,
      .allmeeting_warp {
        position: relative;
      }
    }
  }
}
.hiddenVideo {
  .van-tabs__wrap {
    height: 0;
  }
}
</style>

