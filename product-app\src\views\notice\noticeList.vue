<template>
  <div class="noticeList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <div v-for="(item,index) in dataList"
               :key="index"
               class="van-hairline--bottom">
            <van-cell clickable
                      style=" padding: 10px 15px;"
                      @click="openDetails(item)">
              <!-- <div v-if="getItemForKey(item.id,redIds,'id')"
                   class="notRead"></div> -->
              <div class="flex_box">
                <img class="vue_newslist_img"
                     v-if="item.url"
                     :src="item.url"
                     :alt="cacheImg(item,true)" />
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                       :style="$general.loadConfiguration()">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="$general.loadConfiguration(-4)">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    <span class="inherit"
                          style="font-size: 18px;margin-left: 5px;"
                          v-html="item.title"></span>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div class="vue_newslist_time"
                         style="font-size: 13px;">{{dayjs(item.time).format('YYYY-M-D HH:mm')}}</div>
                    <div class="vue_newslist_source text_one2 flex_placeholder"
                         style="font-size: 13px;">{{item.source}}</div>
                  </div>
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Tag } from 'vant'
export default {
  name: 'noticeList',
  components: {
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.notice.noticeList({
        pageNo: data.pageNo,
        pageSize: 10,
        noticeTitle: data.keyword,
        type: data.type,
        isAppShow: 1,
        isCanSeeAll: 0
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.id = item.id || ''
        item.title = (item.noticeTitle || '').replace(new RegExp(data.keyword, 'g'), data.keyword ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.keyword + '</span>' : '')
        item.content = item.content ? DeleteHtmlFromStartToEnd(item.content, '<!--', '-->').replace(/<.*?>/g, '').replace(/&nbsp;/ig, '') : ''
        item.source = item.org || item.createName || '' // 部门
        item.time = item.publishDate || '' // 时间
        item.isTop = item.isTop || '0' // 置顶 1是0否
        item.meetingId = item.meetingId || '' // 活动id
        item.noticeType = item.noticeType || '' // 活动还是会议
      })
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const DeleteHtmlFromStartToEnd = (str, begin, end) => {
      str = str.replace(begin + end, '')
      if (str.indexOf(begin) === -1) {
        return str
      }
      var substr = str.substring(str.indexOf(begin) + begin.length, str.indexOf(end))
      str = str.replace(substr, '')
      return DeleteHtmlFromStartToEnd(str, begin, end)
    }
    const onClickLeft = () => history.back()
    const openDetails = (row) => {
      router.push({ name: 'noticeDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), dayjs, search, onClickLeft, onRefresh, onLoad, openDetails, $general }
  }
}
</script>
<style lang="less">
.noticeList {
  width: 100%;
  min-height: 100vh;
  background: #f8f8f8;
}
</style>
