<template>
  <div class="bookNotice">
    <!--搜索-->
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="onClickRight">
        <template #right>
          <van-icon name="shop-o"
                    size="20" />&nbsp;个人书库
        </template>
      </van-nav-bar>
      <div id="search"
           class="search_box"
           style="background:#f8f8f8;">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                   :placeholder="'搜索笔记内容'"
                   maxlength="100"
                   type="search"
                   @keyup.enter="btnSearch"
                   v-model="keyword" /></form>
          <div v-if="!isShowHead"
               class="right_btn"
               @click="onClickRight">
            <van-icon name="shop-o"
                      size="20" />&nbsp;个人书库
          </div>
        </div>
      </div>
    </van-sticky>

    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul v-if="dataList.length != 0">
          <van-swipe-cell v-for="(item,index) in dataList"
                          :key="index"
                          class="van-hairline--bottom">
            <li @click="openBookNotes(item)"
                class="item_body_warp">
              <div class="item_body click">
                <div class="itemThree_item flex_box ">
                  <div v-if="item.url"
                       :style="'width:64px;height:81px;'+'margin:auto;position: relative;overflow:hidden;'">
                    <img v-if="item.txt.bookType == '2'"
                         class="item_Sound"
                         :style="'width:18px;height:18px;'"
                         :src="icon_hasSound" />
                    <div v-if="item.isAvailable == '0'"
                         class="item_takeDown"
                         :style="'font-size:12px;'">已下架</div>
                    <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                         :src="item.url" />
                  </div>
                  <div class="flex_placeholder"
                       style="padding-top: 3px;padding-left: 10px;">
                    <div v-if="item.name"
                         class="itemThree_name text_one2"
                         :style="'font-size:15px;'"
                         v-html="item.name"></div>
                    <div v-if="item.author"
                         class="itemThree_author text_one2"
                         :style="'font-size:12px;'"
                         v-html="item.author"></div>
                    <div v-if="item.summary"
                         class="itemThree_summary text_two"
                         :style="'font-size:12px;'"
                         v-html="'共'+item.num+'条笔记'"></div>
                  </div>
                </div>
              </div>
            </li>
            <!--不能换行 会有多的空间-->
            <template v-slot:right>
              <div :style="'height:100%;padding: 9px 0;'">
                <van-button style="border-radius:14px 0 0 14px;"
                            @click="delNote(item)"
                            color="#fa5051"
                            square
                            type="danger"
                            text="删除"></van-button>
              </div>
            </template>
          </van-swipe-cell>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, NavBar, Sticky, Circle, Progress } from 'vant'
// import moment from 'moment'
export default {
  name: 'bookNotice',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Circle.name]: Circle,
    [Progress.name]: Progress,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    watch(() => data.listSelect, (newName, oldName) => {
      console.log(data.listSelect)
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      onRefresh()
    })
    if (data.title) {
      document.title = data.title
    }
    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword

      }
      const { data: list, total } = await $api.bookAcademy.getNotesList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {
          txt: { bookType: _eItem.bookType || '' },
          onlyPage: '1',
          id: _eItem.id,
          url: _eItem.coverImgUrl,
          name: _eItem.bookName,
          author: _eItem.authorName,
          isAvailable: _eItem.isAvailable,
          summary: _eItem.bookDescription,
          num: _eItem.noteAmount
        }
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const delNote = async (_item) => {
      const res = await $api.bookAcademy.clearNotes({ bookId: _item.id })
      if (res) {
        // eslint-disable-next-line eqeqeq
        Toast('删除' + (res.errcode == 200 ? '成功' : '失败'))
        $general.delItemForKey(_item.id, data.dataList, 'id')
        if (data.dataList.length === 0) {
          onRefresh()
        }
      }
    }
    const openBookNotes = (row) => {
      console.log(row)
      router.push({ name: 'notesDetails', query: { id: row.id } })
    }
    // 点击 书本
    const clickBook = (_item, _index) => {
      if (data.isSetting) {
        return
      }
      openBookDetails(_item)
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    const onClickLeft = () => history.back()
    const onClickRight = () => {
      router.push({ name: 'books', query: {} })
    }
    return { ...toRefs(data), onRefresh, onLoad, onClickLeft, onClickRight, delNote, openBookNotes, btnSearch, goBookReader, openBookDetails, clickBook }
  }
}
</script>
<style lang="less" scoped>
.bookNotice {
  width: 100%;
  background: #f9f9f9;
  position: relative;
  .right_btn {
    margin: 6px 10px 0 0;
  }
  .itemThree_name {
    color: #222;
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 3px;
  }
  .itemThree_summary {
    font-weight: 400;
    margin-top: 10px;
    color: #8b8a8a;
  }
  .notes_box {
    margin-top: 10px;
  }
  .notes_item {
    padding: 7px;
  }
  .notes_context {
    font-weight: 400;
    color: #222222;
    padding: 2px 1px 2px 11px;
  }
}
</style>
