.libraryDetails {
  width: 100%;

  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }

  .store_hint_img {
    position: relative;
    width: 100%;
    height: 215px;
    object-fit: cover;
  }

  .store_details_box {
    background: #FFF;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    position: absolute;
    width: 100%;
    min-height: 16px;
    margin-top: -16px;
  }

  .itemSex_box {
    margin: 0 0px;
  }

  .itemSex_item {
    width: 33.333%;
    padding: 10px 10px 15px 10px;
  }

  .itemSex_name {
    color: #5E646D;
    font-weight: 500;
    margin-top: 0.0px;
    text-align: center;
  }

  .itemSex_author {
    color: #A5A5A5;
    font-weight: 400;
    margin-top: 3px;
    text-align: center;
  }

  .van-tab {
    max-width: 360px;
  }
}