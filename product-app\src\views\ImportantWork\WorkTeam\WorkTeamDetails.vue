<template>
  <div class="WorkTeamDetails">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="WorkTeamDetailsBox">
        <div class="WorkTeamDetailsTitle">{{ details.title }}</div>
        <div class="WorkTeamDetailsInfo">
          <div>{{ details.publishDate }}</div>
          <div>{{ details.type == '1' ? '青岛国际航运中心建设' : details.type == '2' ? '青港合作及港澳招商' : '' }}</div>
        </div>
        <div class="WorkTeamDetailsContent" @click="setImgBigger" v-html="details.content"></div>
        <template v-if="attachInfo.data && attachInfo.data.length != 0">
          <div class="general_attach" style="background-color: #fff;padding: 0;">
            <div v-for="(item, index) in attachInfo.data" :key="index"
              class="general_attach_item flex_box flex_align_center click" @click="annexClick(item, false)">
              <img class="general_attach_icon" :style="$general.loadConfigurationSize([5, 7])"
                :src="require(`../../../assets/fileicon/${item.iconInfo.name}`)" />
              <div class="flex_placeholder flex_box flex_align_center">
                <div class="general_attach_name text_one2" style="font-size: 14px;display: -webkit-box;">{{ item.name }}
                </div>
                <div class="general_attach_size" style="font-size: 12px;">{{ $general.getFileSize(item.size) }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'WorkTeamDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false,
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        noticeInfo()
      }, 520)
    }
    const annexClick = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.host === '*************') {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://*************/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        } else {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://www.cszysoft.com/appShare/qdzx/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        }
      } else {
        var param = {
          id: item.id,
          url: item.url,
          name: item.name
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    // 详情请求
    const noticeInfo = async () => {
      const res = await $api.ImportantWork.info(data.id)
      var { data: details } = res
      data.details = details
      data.attachInfo.data = []
      var attachmentList = data.details.fileListVo || []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.id = _eItem.id || ''
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          data.attachInfo.data.push(item)
        })
      }
      if (!details.content && data.attachInfo.data.length > 0) {
        annexClick(data.attachInfo.data[0])
      }
      data.refreshing = false
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.WorkTeamDetailsContent img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview({ images: img, startPosition: nowIndex, closeable: true })
      }
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), $general, onRefresh, setImgBigger, annexClick, onClickLeft }
  }
}
</script>
<style lang="less">
.WorkTeamDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .WorkTeamDetailsBox {
    padding: 16px;
    background: #fff;

    .WorkTeamDetailsTitle {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }

    .WorkTeamDetailsInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;

      div {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }

    .WorkTeamDetailsContent {
      width: 100%;
      line-height: 30px;
      margin: 10px 0;

      img {
        width: 100%;
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
