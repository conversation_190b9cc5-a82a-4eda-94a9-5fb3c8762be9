<template>
  <div class="attractInvestmentColumnList">
    <van-overlay :show="loading" z-index="2000">
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh;">
        <van-loading type="spinner" size="32px" color="#1989fa">加载中...</van-loading>
      </div>
    </van-overlay>
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="招商引资情况统计" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <div v-for="(col, colIdx) in filteredColumnList" :key="colIdx" class="column-section">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">{{ col.label }}</span>
        </div>
        <div class="header_right" v-if="col.data.length > 3" @click="openMore(col.id)">
          <span class="header_right_more">更多</span>
          <img class="header_right_arrow" src="../../../assets/img/icon_word_right_arrow.png" alt="">
        </div>
      </div>
      <ul class="vue_newslist_box">
        <template v-if="col.data.length === 0">
          <div class="empty-tip">暂无数据</div>
        </template>
        <template v-else>
          <div class="investment_card" v-for="item in col.data.slice(0, 3)" :key="item.id" @click="openDetails(item)">
            <div class="investment_title">{{ item.title }}</div>
            <div class="investment_bottom">
              <span class="investment_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</span>
              <span class="investment_type_btn">{{ item.type == '1' ? '外出拜访' : item.type == '2' ? '在青接待' : item.type ==
                '3' ? '自主举办' : '' }}</span>
            </div>
          </div>
        </template>
      </ul>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, computed } from 'vue'
import { NavBar, Sticky, Tag, Loading, Overlay } from 'vant'
export default {
  name: 'attractInvestmentColumnList',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      columnListData: [
        { id: '12', label: '2025年12月招商引资情况', data: [] },
        { id: '11', label: '2025年11月招商引资情况', data: [] },
        { id: '10', label: '2025年10月招商引资情况', data: [] },
        { id: '9', label: '2025年9月招商引资情况', data: [] },
        { id: '8', label: '2025年8月招商引资情况', data: [] },
        { id: '7', label: '2025年7月招商引资情况', data: [] },
        { id: '6', label: '2025年6月招商引资情况', data: [] },
        { id: '5', label: '2025年5月招商引资情况', data: [] },
        { id: '4', label: '2025年4月招商引资情况', data: [] },
        { id: '3', label: '2025年3月招商引资情况', data: [] },
        { id: '2', label: '2025年2月招商引资情况', data: [] },
        { id: '1', label: '2025年1月招商引资情况', data: [] }
      ]
    })
    // 添加计算属性来过滤有数据的栏目
    const filteredColumnList = computed(() => {
      return data.columnListData.filter(col => col.data.length > 0)
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeList()
    })
    // 列表请求
    const noticeList = async () => {
      data.loading = true
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 66,
        columnId: route.query.columnId
      })
      var { data: list } = res
      data.columnListData.forEach(item => { item.data = [] })
      list.forEach(item => {
        const col = data.columnListData.find(col => col.id === item.backupTwo)
        if (col) {
          col.data.push(item)
        }
      })
      data.loading = false
    }
    const openDetails = (row) => {
      router.push({ name: 'attractInvestmentDetails', query: { id: row.id } })
    }
    const onClickLeft = () => {
      history.back()
    }
    const openMore = (_id) => {
      router.push({ name: 'attractInvestmentList', query: { columnId: route.query.columnId, secondaryColumn: _id } })
    }
    return { ...toRefs(data), dayjs, openDetails, $general, onClickLeft, openMore, filteredColumnList }
  }
}
</script>
<style lang="less">
.attractInvestmentColumnList {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 15px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 17px;
        color: #222222;
        margin-left: 6px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_more {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_arrow {
        width: 23px;
        height: 23px;
      }
    }
  }

  .empty-tip {
    text-align: center;
    color: #999;
    padding: 32px 0;
    font-size: 15px;
  }

  .investment_card {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;

    .investment_title {
      font-family: Source Han Serif SC, Source Han Serif SC;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 12px;
    }

    .investment_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .investment_date {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-size: 12px;
        color: #666666;
      }

      .investment_type_btn {
        background: rgba(0, 122, 255, 0.12);
        border: 1px solid #007AFF;
        border-radius: 2px;
        font-family: Source Han Serif SC, Source Han Serif SC;
        font-weight: 500;
        font-size: 12px;
        color: #007AFF;
        padding: 3px;
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
