<template>
  <div class="bookNotice">
    <van-nav-bar v-if="isShowHead" title="活动通知"
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <!--搜索-->
    <van-sticky>
      <div id="search"
           class="search_box">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                   :placeholder="'搜索'"
                   maxlength="100"
                   type="search"
                   @keyup.enter="btnSearch"
                   v-model="keyword" /></form>
        </div>
      </div>
    </van-sticky>

    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <ul v-if="dataList.length != 0"
            class="vue_newslist_box">
          <van-swipe-cell v-for="(item,index) in dataList"
                          :key="index"
                          class="van-hairline--bottom">
            <van-cell clickable
                      class="vue_newslist_item "
                      @click="openDetails(item)">
              <div class="flex_box"
                   style="position: relative;">
                <p v-if="!item.isRead"
                   class="flex_box flex_align_center flex_justify_content text_one"
                   :class="'footer_item_hot'"
                   :style="'width12px;height:12px;'"
                   v-html="''"></p>
                <div class="vue_newslist_img"
                     v-if="item.url"
                     v-lazy:background-image="item.url"
                     :alt="cacheImg(item)"></div>
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="'font-size:12px;'">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    {{item.title}}
                  </div>
                  <div class="flex_box flex_align_center">
                    <div v-if="item.time"
                         class="vue_newslist_time"
                         :style="'font-size:13px;'">{{item.time}}</div>
                    <div class="vue_newslist_source"
                         :style="'font-size:13px;'">{{item.source || item.createBy}}</div>
                    <div class="flex_placeholder"></div>
                  </div>
                </div>
              </div>
            </van-cell>
          </van-swipe-cell>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Sticky, NavBar } from 'vant'
import moment from 'moment'
export default {
  name: 'bookNotice',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [NavBar.name]: NavBar
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      appReadStatus: [],
      title: route.query.title
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      onRefresh()
    })
    if (data.title) {
      document.title = data.title
    }
    const getHasnew = async () => {
      const { data: news } = await $api.bookAcademy.getHasnew({})
      data.appReadStatus = news
      getData()
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        isPublish: 1,
        seachPlaceholder: '搜索'

      }
      const { data: list, total } = await $api.bookAcademy.getNoticeList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {}; var itemData = _eItem
        item.id = itemData.id || ''// id
        item.title = itemData.title || ''// 标题
        item.source = itemData.officeName || ''// 部门
        // 去除HTML中的注释、去除HTML标签、去除HTML标签中的属性、去除所有空白字符即回车换行
        item.createBy = itemData.createBy || ''// 部门
        item.time = itemData.publishDate ? moment(itemData.publishDate).format('YYYY-M-D HH:mm') : ''// 时间
        item.isTop = itemData.isTop || '0'// 置顶 1是0否
        item.isRead = true
        for (var i = 0; i < data.appReadStatus.length; i++) {
          if (data.appReadStatus[i] === item.id) {
            item.isRead = false
            break
          }
        }
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      console.log(row)
      router.push({ name: 'bookNoticeDetails', query: { id: row.id } })
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getHasnew()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, onLoad, openDetails, btnSearch }
  }
}
</script>
<style lang="less" scoped>
.bookNotice {
  width: 100%;
  background: #f9f9f9;
  .search_warp .search_input {
    padding-left: 10px;
  }
  .footer_item_hot {
    position: absolute;
    top: 2px;
    right: 0;
    background: #f92323;
    border-radius: 50%;
    z-index: 1;
  }
}
</style>
