<template>
  <div class="contactCompanyDetails">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="WorkTeamDetailsBox">
        <div class="WorkTeamDetailsTitle">{{ details.title }}</div>
        <div class="WorkTeamDetailsInfo" v-if="details.businessName">{{ details.businessName }} </div>
        <div style="margin-top: 15px;" v-if="details.content">
          <span style="color: #666;font-size: 16px;">领导活动：</span>
          <span class="WorkTeamDetailsContent" @click="setImgBigger" v-html="details.content"></span>
        </div>
        <div style="margin-top: 15px;" v-if="details.briefIntroduction">
          <span style="color: #666;font-size: 16px;">企业简介：</span>
          <span class="WorkTeamDetailsContent" @click="setImgBigger" v-html="details.briefIntroduction"></span>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'contactCompanyDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        noticeInfo()
      }, 520)
    }
    // 详情请求
    const noticeInfo = async () => {
      const res = await $api.ImportantWork.info(data.id)
      var { data: details } = res
      data.details = details
      data.refreshing = false
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.WorkTeamDetailsContent img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview({ images: img, startPosition: nowIndex, closeable: true })
      }
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), $general, onRefresh, setImgBigger, onClickLeft }
  }
}
</script>
<style lang="less">
.contactCompanyDetails {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .WorkTeamDetailsBox {
    width: 100%;
    padding: 16px;

    .WorkTeamDetailsTitle {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }

    .WorkTeamDetailsInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;

      div {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }

    .WorkTeamDetailsContent {
      width: 100%;
      line-height: 30px;
      margin: 10px 0;

      img {
        width: 100%;
      }
    }
  }

}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
