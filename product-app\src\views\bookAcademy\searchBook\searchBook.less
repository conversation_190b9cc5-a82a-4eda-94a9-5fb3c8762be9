html,
body {
  background: #FFF;
}

.searchBook {
  width: 100%;
  background: #FFF;

  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }

  .history_item {
    min-height: 40px;
  }

  .popular_box {
    margin-top: 30px;
    padding: 0 16px;
  }

  .popular_item {
    margin: 19px 14px 0 0;
    padding: 7px 15px 7px 7px;
    background: #F3f3f3;
    color: #5E646D;
    border-radius: 20px;
  }

  .search_box {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
  }

  .search_list_box {
    padding: 11px 15px;
  }

  .search_item {
    padding: 13px 10px;
    box-shadow: 0px 0px 26px -11px rgba(0, 0, 0, 0.4);
    border-radius: 16px;
  }

  .search_item+.search_item {
    margin-top: 20px;
  }
}