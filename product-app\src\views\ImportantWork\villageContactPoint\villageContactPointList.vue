<template>
  <div class="villageContactPointList">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="乡村振兴联系点" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <ul class="vue_newslist_box">
          <div class="village_contact_point_item" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
            <div class="village_contact_point_icon_title">
              <img class="village_contact_point_icon" src="../../../assets/img/icon_folder.png" alt="icon" />
              <span class="village_contact_point_title">{{ item.title }}</span>
            </div>
            <!-- <div class="village_contact_point_desc"
              v-html="item.content ? item.content.replace(/<[^>]+>/g, '').replace(/&[a-z]+;/g, '') : ''"></div> -->
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Tag } from 'vant'
export default {
  name: 'villageContactPointList',
  components: {
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: data.pageNo,
        pageSize: 10,
        columnId: route.query.columnId
      })
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      router.push({ name: 'villageContactPointDetails', query: { id: row.id } })
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), dayjs, search, onRefresh, onLoad, openDetails, $general, onClickLeft }
  }
}
</script>
<style lang="less">
.villageContactPointList {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .vue_newslist_box {
    padding: 14px 15px;

    .village_contact_point_item {
      padding: 10px 0 15px 0;
      border-bottom: 1px solid #e5eaf3;

      &:last-child {
        border-bottom: none;
      }

      .village_contact_point_icon_title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .village_contact_point_icon {
          width: 17px;
          height: 17px;
          margin-right: 8px;
        }

        .village_contact_point_title {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
      }

      .village_contact_point_desc {
        font-family: Source Han Serif SC, Source Han Serif SC;
        font-weight: 500;
        font-size: 14px;
        color: #666666;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
