<template>
  <div class="CommentDetails">
    <!-- 留言详情 -->
    <div class="comment_info_box">
      <div class="comment_user flex_box flex_align_center">
        <img :src="headImg"
             class="comment_img" />
        <div :style="$general.loadConfiguration(1)"
             class="comment_name">{{userName}}</div>
        <div :style="$general.loadConfiguration(-1)"
             class="comment_date">{{pubDate}}</div>
      </div>
      <div :style="$general.loadConfiguration(2)"
           class="comment_content"
           v-html="content"></div>
    </div>
    <div>
      <div style="padding: 12px 12px;border-top: 15px solid #f4f4f4;"
           class="flex_box flex_align_center">
        <div :style="$general.loadConfiguration(1)+'font-weight: bold;'"
             class="text_one ul_wrapper_bigtxt">{{commentInfo.name}}</div>
      </div>
      <div v-for="(item,index) in listData"
           :key="index"
           class="comment_list flex_box">
        <img :src="item.userHeadImg"
             class="comment_img" />
        <div class="comment_list_box flex_placeholder van-hairline--bottom">
          <div class="flex_box flex_align_center">
            <div :style="$general.loadConfiguration()"
                 class="comment_name flex_placeholder">{{item.userName}}</div>
            <!-- <div v-if="item.isCheck"
                 class="comment_tag"
                 :style="$general.loadConfiguration(-6)+'color:#F00;background:'+T.colorRgba('#F00',0.15)">
              {{item.isCheck}}</div> -->
          </div>
          <div class="flex_box flex_align_center">
            <div :style="$general.loadConfiguration(-2)"
                 class="comment_time flex_placeholder"> {{item.createDate}}
            </div>
            <div @click.stop="fabulousInfo(item)"
                 class="flex_box flex_align_center"
                 style="padding: 8px 0;">
              <van-icon :color="item.isFabulou?appTheme:'#ABABAB'"
                        :size="((appFontSize+2)*1)+'px'"
                        :name="item.isFabulou?'good-job':'good-job-o'"></van-icon>
              <div v-if="item.fabulousCount > 0"
                   :style="$general.loadConfiguration(-2)"
                   class="comment_like_txt">
                {{item.fabulousCount}}</div>
            </div>
          </div>
          <div :style="$general.loadConfiguration()"
               class="">{{item.content}}</div>
        </div>
      </div>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Toast } from 'vant'
export default {
  name: 'CommentDetails',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      id: route.query.id,
      content: route.query.content,
      userName: route.query.name,
      pubDate: route.query.time,
      headImg: route.query.url,
      listData: [],
      commentInfo: { name: '评论' }
    })
    onMounted(() => {
      getCommentList()
    })
    const search = () => {
      data.pageNo = 1
      data.userListData = []
      data.loading = true
      data.finished = false
      getCommentList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.userListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getCommentList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取评论列表数据
    const getCommentList = async () => {
      const res = await $api.general.getCommentList({
        pageNo: 1,
        pageSize: 999,
        keyId: data.id,
        type: 160,
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      var { data: list } = res
      console.log('list===', list)
      list.forEach((item) => {
        item.isFabulou = $general.trimAll(item.isFabulous) === '1' // 是否点赞
      })
      data.listData = data.listData.concat(list)
    }
    // 点赞与取消点赞
    const fabulousInfo = async (item) => {
      console.log('item===========', item)
      item.isFabulou = !item.isFabulou
      if (item.isFabulou) {
        item.fabulousCount++
        var saveres = await $api.general.saveFabulous({
          keyId: item.id,
          type: '101',
          areaId: data.user.areaId
        })
        if (saveres) {
          Toast('点赞成功')
        }
      } else {
        item.fabulousCount--
        var delres = await $api.general.delFabulous({
          keyId: item.id,
          type: '101',
          areaId: data.user.areaId
        })
        if (delres) {
          Toast('取消点赞成功')
        }
      }
    }
    return { ...toRefs(data), search, onRefresh, onLoad, $general, fabulousInfo }
  }
}
</script>
<style lang="less" scoped>
.CommentDetails {
  .comment_info_box {
    padding-bottom: 20px;
    padding: 0 15px;
    .comment_title {
      font-weight: bold;
      padding: 13px 0;
      line-height: 1.41;
      color: #333333;
    }
    .comment_user {
      padding: 20px 0;
      .comment_img {
        width: 38px !important;
        height: 40px !important;
        margin-right: 7px;
      }
      .comment_name {
        color: #666;
        font-weight: 600;
        flex: 2;
      }
      .comment_date {
        color: #999999;
        flex: 2;
      }
    }
    .comment_content {
      padding: 8px 0;
      text-indent: 2em;
      font-weight: 600;
      line-height: 1.81;
      color: #333333;
    }
  }
  .ul_wrapper_bigtxt {
    padding-left: 20px;
    height: 20px;
    color: #333333;
    font-weight: bold;
    // background: url(../../assets/img/jining/lanmuImg.png) no-repeat;
    background: url(../../assets/img/lanmuImg.png) no-repeat;
    background-size: 51px 19px;
    display: flex;
    align-items: center;
  }
  .comment_list {
    padding: 14px;
    .comment_img {
      height: 40px !important;
      margin-right: 7px;
    }
    .comment_list_box {
      padding-bottom: 20px;
      .comment_name {
        color: #666;
        font-weight: 600;
      }
      .comment_time {
        color: #999;
      }
    }
  }
}
</style>
