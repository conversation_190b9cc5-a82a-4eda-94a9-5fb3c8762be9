.commissarRecom {
    width: 100%;

    .commissarRecom_type {
        text-align: right;
        padding: 15px 20px 0 0;
        color: #BABEBF;
        width: 100%;
        font-size: 14px;
    }
    .van-pull-refresh {
      z-index: 99;
    }
  
    .item_title {
      color: #222;
      font-weight: bold;
      padding: 6px 0;
      text-align: center;
      font-size: 19px;
    }
  
    .typeOne .item_title {
      padding: 13px 0;
    }
  
    .item_smallTitle {
      color: #8b8a8a;
      text-align: center;
      font-size: 13px;
    }
  
    .item_img {
      margin: 10px 0 4px 0;
      position: relative;
      width: 129px;
      height: 165px;
    }
  
    .item_img img {
      width: 100%;
      height: 100%;
    }
  
    .item_Sound {
      width: 28px !important;
      height: 28px !important;
    }
  
    .item_entity {
      position: absolute;
      right: 0;
      top: 0;
      background: #4cd964;
      color: #fff;
      padding: 2px 10px;
      border-bottom-left-radius: 5px;
      border-top-right-radius: 5px;
      font-size: 10px;
    }
  
    .item_name {
      color: #222;
      font-weight: bold;
      margin-top: 20px;
      text-align: center;
      font-size: 16px;
    }
  
    .item_hint {
      color: #8b8a8a;
      margin-top: 7px;
      text-align: center;
      font-size: 13px;
    }
    
    .item_people {
        width: 100%;
        text-align: center;
        margin-top: 10px;
        > img {
            vertical-align: middle;
            width: 22px;
            height: 22px;
            margin-right: 5px;
        }
        > span {
            color: #92A0AC;
            vertical-align: middle;
            font-size: 12px;
        }
    }
    .item_btn_box {
      width: 252px;
      margin: 20px 0 10px 0;
      font-size: 15px;
    }
  
    .itemSex_box {
      margin: 0 -6px;
    }
  
    .itemSex_item {
      width: 33.33%;
      padding: 0 6px 5px 6px;
    }
  
    .itemSex_name {
      color: #222;
      font-weight: 500;
      margin-top: 0;
      padding-left: 5px;
      font-size: 15px;
    }
  
    .itemThree_item {
      padding: 6px 0;
    }
  
    .itemThree_name {
      color: #222;
      font-weight: 400;
      margin-top: 0;
      margin-bottom: 3px;
      font-size: 15px;
    }
  
    .itemThree_summary {
      font-weight: 400;
      margin-top: 8px;
      color: #8b8a8a;
      font-size: 12px;
    }
  
    .itemNotes_item {
      padding-top: 12px;
    }
  
    .itemNotes_item_box {
      padding: 0 0 15px 10px;
    }
  
    .itemNotes_name {
      font-weight: 500;
      color: #222222;
      font-size: 12px;
    }
  
    .itemNotes_content {
      color: #222222;
      padding-bottom: 6px;
      font-size: 13px;
    }
  
    .itemNotes_from {
      padding: 5px;
      border-radius: 6px;
    }
  
    .itemNotes_author {
      margin-top: 3px;
    }
  
    .itemWords_content_box {
      margin: 10px 0;
      padding: 16px 16px;
    }
  
    .itemWords_content {
      position: relative;
    }
  
    .itemWords_content_hint {
      position: absolute;
      background: #fff;
      font-size: 56px;
      width: 22px;
      height: 26px;
      font-family: Helvetica;
    }
  
    .itemWords_author {
      text-align: right;
      margin-top: 15px;
    }
  }