<template>
  <div class="performanceFilesTypeList">
    <div @click.stop="selYear()"
         style="padding: 10px;background: #FFF;">
      <div class="flex_placeholder flex_box flex_align_center flex_justify_content">
        <div class="flex_box flex_align_center"
             style="padding: 5px 8px;">
          <span :style="$general.loadConfiguration(-3)"
                style="color:#333;margin-right:5px;"
                v-html="year+'年度'"></span>
          <van-icon :color="'#333'"
                    :size="((appFontSize-8)*0.02)+'rem'"
                    name="arrow-down"></van-icon>
        </div>
      </div>
    </div>
    <template v-if="listData&&listData.length !== 0">
      <!--数据列表-->
      <ul class="vue_newslist_box"
          style="background: #FFF;">
        <li v-for="(item,index) in listData"
            :key="index"
            @click="openDetails(item)"
            class="flex_box flex_align_center van-hairline--bottom click"
            style="padding: 15px 24px 15px 15px;">
          <div style="min-width: 32px;margin-right:10px;"
               class="flex_box flex_justify_content">
            <div :style="$general.loadConfiguration(-2)+'font-weight: bold;color:'+(index<3?['#FF5E5E','#FFC05E','#2FC994'][index]:'#ccc')"
                 v-if="index<9">{{ 0+(index+1) }}</div>
            <div :style="$general.loadConfiguration(-2)+'font-weight: bold;color:'+(index<3?['#FF5E5E','#FFC05E','#2FC994'][index]:'#ccc')"
                 v-else>
              {{index+1}}
            </div>
          </div>
          <div class="text_one2 flex_placeholder"
               :style="$general.loadConfiguration()+'color:#333;margin-right:24px;'">{{item.name}}</div>
          <div :style="$general.loadConfiguration(-1)+'color:#333;'">{{item.values.length}}</div>
        </li>
      </ul>
    </template>
    <template v-else>
      <div style="text-align: center;color: #878787;padding-top: 30px;">暂无数据</div>
    </template>
  </div>
  <van-action-sheet v-model:show="showAction"
                    :actions="years"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, ActionSheet } from 'vant'
export default {
  name: 'performanceFilesTypeList',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [ActionSheet.name]: ActionSheet,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      correspond: {
        allMeet: '全体会议',
        committeeMeet: '常委会议',
        specialCommitteeMeet: '专门委员全体会议',
        negotiationMeet: '双月协商座谈会',
        negotiationAct: '协商监督，议政建言',
        researchAct: '调研活动',
        fiveAct: '五进五送',
        deleAct: '界别活动',
        specialAct: '专题协商议政',
        memberBBSAct: '委员论坛',
        memberTrainingAct: '委员培训',
        legislationAct: '立法协商',
        inspectAct: '视察活动',
        memberOnlineAct: '委员在线',
        otherAct: '其他活动',
        threeAct: '三双活动',
        contributionAct: '特别贡献',
        meetSpeechOral: '入选全体会议大会发言材料并口头发言',
        meetSpeechRXQTHY: '入选全体会议大会发言材料',
        meetSpeechZXQTHY: '撰写全体会议大会发言材料',
        meetSpeechRXZTXSOral: '入选专题协商议政发言材料并口头发言',
        meetSpeechRXZTXS: '入选专题协商议政发言材料',
        meetSpeechZXZTXS: '撰写专题协商议政发言材料',
        prop: '提案个人',
        joinprop: '提案联名',
        feedbackprop: '提案反馈意见',
        learningMaterials: '委员必读',
        dutyReport: '述职报告',
        dutyFillReport: '参加调研并撰写调研报告',
        dutyFillMaterials: '撰写文史资料',
        social: '提交社情民意',
        goodSocial: '优秀社情民意',
        tadbAct: '提案督办',
        tablxsnAct: '提案办理协商'
      },
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      listData: [],
      years: [],
      year: route.query.year,
      userId: route.query.id,
      showAction: false,
      description: '选择年份'
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      var nowYear = new Date().getFullYear()
      for (let i = 2018; i <= nowYear; i++) {
        data.years.push({ name: i })
      }
      data.year = nowYear
      data.years.value = nowYear
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      getList()
    }
    // 列表
    const getList = async () => {
      const param = {
        userId: data.userId,
        year: data.year
      }
      const res = await $api.performanceFiles.findDutyDetail(param)
      var code = res ? res.errcode : ''
      var info = res ? res.data : {} || {}
      data.listData = []
      if (code === 200) {
        for (var _eItem in data.correspond) {
          data.listData.push({ type: _eItem, name: data.correspond[_eItem], values: info[_eItem] || [] })
        }
        data.listData.sort(function (a, b) {
          return b.values.length - a.values.length
        })
      }
    }
    // 弹窗年份
    const selYear = () => {
      data.showAction = true
    }
    // 详情
    const openDetails = (row) => {
      console.log('row==>>', row)
      router.push({ name: 'performanceDutiesList', query: { id: data.userId, title: row.name, year: data.year, correspond: row.type } })
    }
    // 选择年份
    const onSelect = (_item) => {
      data.year = _item.name
      onRefresh()
    }
    return { ...toRefs(data), $general, dayjs, openDetails, selYear, onSelect }
  }
}
</script>
<style lang="less" scoped>
.performanceFilesTypeList {
  width: 100%;
  .historName {
    font-size: 0.42rem;
  }
  .historTime {
    font-size: 0.35rem;
    color: #8d8c8c;
    margin-top: 0.2rem;
  }
}
</style>
