<template>
  <div v-if="!isShowLogin"
       style="text-align:center;padding-top:150px;">
    <div>
      <img src="../../assets/img/loading_more.gif" />
    </div>
  </div>
  <div v-else
       class="login">
    <div class="loginText">欢迎登录，</div>
    <div class="loginName">青岛政协</div>
    <div class="loginTab">
      <div class="loginTabItem"
           :class="{ loginTabItemA: active }"
           @click="activeClick(true)">登录</div>
      <!-- <span></span>
      <div class="loginTabItem"
           :class="{loginTabItemA:!active}"
           @click="activeClick(false)">验证码登录</div> -->
    </div>
    <!-- 账号密码登录 -->
    <template v-if="active">
      <van-cell-group>
        <div class="loginLabel">
          <div class="loginLabelIcon account"></div>
          <div class="loginLabelText">账号</div>
        </div>
        <van-field v-model="account"
                   placeholder="请输入用户名" />
      </van-cell-group>
      <van-cell-group>
        <div class="loginLabel">
          <div class="loginLabelIcon password"></div>
          <div class="loginLabelText">密码</div>
        </div>
        <van-field v-model="password"
                   type="password"
                   @keyup.enter="loginUc"
                   placeholder="请输入密码" />
      </van-cell-group>
    </template>
    <!-- 验证码登录 -->
    <template v-if="!active">
      <van-cell-group>
        <div class="loginLabel">
          <div class="loginLabelIcon phone"></div>
          <div class="loginLabelText">手机号</div>
        </div>
        <van-field v-model="phone"
                   placeholder="请输入手机号" />
      </van-cell-group>
      <van-cell-group>
        <div class="loginLabel">
          <div class="loginLabelIcon validation"></div>
          <div class="loginLabelText">验证码</div>
        </div>
        <van-field v-model="validation"
                   placeholder="请输入验证码">
          <template #button>
            <div class="validationButton">获取验证码</div>
          </template>
        </van-field>
      </van-cell-group>
    </template>
    <div class="loginButton">
      <van-button round
                  block
                  type="primary"
                  @click="loginUc">登录</van-button>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Toast } from 'vant'
export default {
  name: 'login',
  setup () {
    const $api = inject('$api')
    const $general = inject('$general')
    const $utils = inject('$utils')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      active: true,
      account: '',
      password: '',
      phone: '',
      validation: '',
      isShowLogin: true
    })
    onMounted(() => {
      getCode()
      const token = route.query.userToken
      if (token) {
        data.isShowLogin = false
        sessionStorage.setItem('otherToken', 'Bearer ' + token)
        getPersonal()
      }
    })
    const getCode = async () => {
      setTimeout(() => {
        data.isShowLogin = true // 10秒还没有登录成功就手机登录
      }, 10000)
      data.isShowLogin = false
      var codes = location.search.split('&')[0].split('=')[1]
      console.log(codes)
      console.log(location.search)
      if (location.search.indexOf('code') !== -1) {
        getSDToken(codes)
      } else {
        Toast('您未授权！')
        data.isShowLogin = true
      }
    }
    const getSDToken = async (codes) => {
      const res = await $api.general.authenticationLogin({ code: codes, verifyCode: 'app' })
      console.log('res============', res)
      var { data } = res
      var token = data.data
      var desEncryptPhone = data.desEncryptPhone
      sessionStorage.setItem('token', JSON.stringify(token))
      sessionStorage.setItem('Sys_token', JSON.stringify(token))
      sessionStorage.setItem('desEncryptPhone', JSON.stringify(desEncryptPhone))
      changearea()
    }
    const getPersonal = async () => {
      setTimeout(() => {
        data.isShowLogin = true // 10秒还没有登录成功就手机登录
      }, 10000)
      const { custom: user } = await $api.general.getPersonal({ params: {} })
      console.log(user)
      const useridN = user.userguid
      // const useridN = '45f0c5f9-cad2-49e6-887d-b38dfcbc23de'
      getOtherToken(useridN)
    }
    const activeClick = (active) => {
      data.active = active
    }
    const loginUc = async () => {
      const res = await $api.general.loginUc({
        username: data.account,
        password: $utils.encrypt(data.password, 'zysofthnzx202002'),
        verifyCode: ''
      }, { isApp: 'app' })
      var { data: token, projects } = res
      sessionStorage.setItem('token', JSON.stringify(token))
      sessionStorage.setItem('Sys_token', JSON.stringify(token))
      sessionStorage.setItem('projects', JSON.stringify(projects))
      changearea()
    }
    const getOtherToken = async (useridN) => {
      sessionStorage.setItem('useridN', useridN)
      const { data: token } = await $api.general.calogin({ certId: useridN })
      sessionStorage.setItem('token', JSON.stringify(token))
      sessionStorage.setItem('Sys_token', JSON.stringify(token))
      changearea()
    }
    // 切换系统登录
    const changearea = async () => {
      const res = await $api.general.changearea()
      var { data: { token, user, menus, areas } } = res
      sessionStorage.setItem('menus', JSON.stringify(menus))
      sessionStorage.setItem('token', JSON.stringify(token))
      sessionStorage.setItem('Sys_token', JSON.stringify(token))
      sessionStorage.setItem('user', JSON.stringify(user))
      sessionStorage.setItem('areas', JSON.stringify(areas))
      sessionStorage.setItem('areaId', user.areaId)
      areas.forEach(item => {
        if (item.id === user.areaId) {
          sessionStorage.setItem('areaName', JSON.stringify(item.value))
        }
      })
      nologin()
      // router.push({ name: 'home' })
    }
    // 切换系统登录
    const nologin = async () => {
      const res = await $api.general.nologin({ codes: 'logo,AcademyAppId,AcademyRentId,rongCloudIdPrefix,appGrayscale,setWaterMark,fileStoreUrl,fileVisitUrl,BigDataUser,qrCode,appHomeBg,appTheme,memberRequiredId,appMyBg' })
      var { data: { logo, AcademyAppId, AcademyRentId, rongCloudIdPrefix, appGrayscale, setWaterMark, fileStoreUrl, fileVisitUrl, BigDataUser, qrCode, appHomeBg, appTheme, memberRequiredId, appMyBg } } = res
      sessionStorage.setItem('qrCode', qrCode)
      sessionStorage.setItem('tomcatAddress', 'https://www.cszysoft.com:9091/')
      sessionStorage.setItem('logo', logo)
      sessionStorage.setItem('AcademyAppId', AcademyAppId)
      sessionStorage.setItem('AcademyRentId', AcademyRentId)
      sessionStorage.setItem('rongCloudIdPrefix', rongCloudIdPrefix)
      sessionStorage.setItem('appGrayscale', appGrayscale)
      sessionStorage.setItem('setWaterMark', setWaterMark)
      sessionStorage.setItem('fileStoreUrl', fileStoreUrl)
      sessionStorage.setItem('fileVisitUrl', fileVisitUrl)
      sessionStorage.setItem('BigDataUser', BigDataUser)
      sessionStorage.setItem('appHomeBg', appHomeBg)
      sessionStorage.setItem('appTheme', appTheme)
      sessionStorage.setItem('memberRequiredId', memberRequiredId)
      sessionStorage.setItem('appMyBg', appMyBg)
      $general.setWaterMark()
      $general.appGrayscale()
      router.push({ name: 'home' })
    }
    return { ...toRefs(data), activeClick, loginUc }
  }
}
</script>
<style lang='less'>
.login {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  padding: 40px;
  padding-top: 60px;
  background-color: #fff;

  .loginText,
  .loginName {
    font-size: 22px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 32px;
    color: #333333;
  }

  .loginText {
    color: #3088fe;
  }

  .loginTab {
    padding-top: 58px;
    display: flex;
    align-items: center;

    .loginTabItem {
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 40px;
      color: #999999;
    }

    .loginTabItemA {
      font-weight: 600;
      color: #333333;
    }

    span {
      display: inline-block;
      width: 0.6px;
      height: 15px;
      margin: 0 16px;
      background-color: #999999;
    }
  }

  .van-cell-group {
    padding-top: 28px;

    &::after {
      bottom: 0;
      top: auto;
      border-color: #e8e8e8;
    }

    .van-cell {
      padding: 6px 0;

      .van-field__control {
        font-size: 14px;
      }

      .validationButton {
        font-size: 14px;
        font-family: PingFang SC;
        color: #3088fe;
      }
    }
  }

  .loginLabel {
    width: 100%;
    display: flex;
    align-items: center;
    padding-bottom: 2px;

    .loginLabelIcon {
      width: 15px;
      height: 17px;
      margin-right: 10px;
    }

    .account {
      background: url("../../assets/img/accountIcon.png") no-repeat;
      background-size: 100% 100%;
    }

    .password {
      background: url("../../assets/img/passwordIcon.png") no-repeat;
      background-size: 100% 100%;
    }

    .phone {
      background: url("../../assets/img/phoneIcon.png") no-repeat;
      background-size: 100% 100%;
    }

    .validation {
      background: url("../../assets/img/validationIcon.png") no-repeat;
      background-size: 100% 100%;
    }

    .loginLabelText {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 22px;
      color: #333333;
    }
  }

  .loginButton {
    padding-top: 88px;
  }
}
</style>
