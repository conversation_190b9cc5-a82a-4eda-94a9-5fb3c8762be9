<template>
  <div class="progress-bar-chart">
    <div class="progress-bar-header">
      <span class="progress-bar-title">{{ title }}</span>
    </div>
    <div class="progress-bar-wrapper">
      <div class="progress-bar-track">
        <span class="progress-bar-desc" :style="{ left: descLeft }">{{ desc }}</span>
        <div class="progress-bar-bg">
          <div class="progress-bar-fg" :style="{
            width: percent + '%',
            background: color
          }"></div>
        </div>
      </div>
      <span class="progress-bar-value">{{ value }}</span>
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'

export default {
  name: 'ProgressBar<PERSON><PERSON>',
  props: {
    title: { type: String, required: true },
    desc: { type: String, default: '' },
    percent: { type: Number, required: true },
    value: { type: [Number, String], required: true },
    color: { type: String, default: '' }
  },
  setup (props) {
    const descLeft = computed(() => {
      if (props.percent >= 100) {
        return 'calc(100% - 40px)'
      }
      if (props.percent <= 0) {
        return '0px'
      }
      return `calc(${props.percent}% - 20px)`
    })
    onMounted(() => {
    })
    return {
      descLeft
    }
  }
}
</script>

<style scoped lang="less">
.progress-bar-chart {
  margin-top: 12px;

  .progress-bar-header {
    display: flex;
    align-items: center;
    font-size: 15px;
    color: #666;
    margin-bottom: 4px;

    .progress-bar-title {
      font-size: 14px;
      color: #666;
    }
  }

  .progress-bar-wrapper {
    display: flex;
    align-items: center;

    .progress-bar-track {
      position: relative;
      flex: 1;
      margin-right: 10px;

      .progress-bar-desc {
        position: absolute;
        top: -22px; // 进度条上方，可根据实际调整
        white-space: nowrap;
        font-size: 12px;
        color: #999;
        transition: left 0.4s;
        pointer-events: none;
      }

      .progress-bar-bg {
        width: 100%;
        height: 8px;
        background: #f5f6fa;
        border-radius: 4px;
        overflow: hidden;

        .progress-bar-fg {
          height: 100%;
          border-radius: 4px;
          transition: width 0.4s;
        }
      }
    }

    .progress-bar-value {
      font-size: 14px;
      color: #999;
      min-width: 32px;
      text-align: right;
    }
  }
}
</style>
