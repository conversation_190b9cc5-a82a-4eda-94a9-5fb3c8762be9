* {
  -webkit-touch-callout: none;
  /**系统默认菜单被禁⽤*/
  -webkit-user-select: none;
  /**webkit浏览器*/
  -moz-user-select: none;
  /**⽕狐*/
  -ms-user-select: none;
  /**IE10*/
  user-select: none;
}



.chatRoom {
  width: 100%;
  -webkit-touch-callout: none;
  /**系统默认菜单被禁⽤*/
  -webkit-user-select: none;
  /**webkit浏览器*/
  -moz-user-select: none;
  /**⽕狐*/
  -ms-user-select: none;
  /**IE10*/
  user-select: none;


  .contert-box {
    width: 100%;
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .chat_hint {
    text-align: center;
    padding: 15px;
  }

  .chat_hint .van-tag {
    font-size: inherit;
    font-family: inherit;
    padding: 2px 15px;
    color: #999999;
    background: rgba(0, 0, 0, 0);
    font-weight: 600;
  }

  .chat_msg_box {
    width: 100%;
    height: auto;
    padding: 10px 15px;
    box-sizing: border-box;
  }

  .chat_right {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -moz-box-orient: reverse;
    -moz-box-direction: reverse;
    flex-direction: row-reverse;
    -webkit-flex-direction: row-reverse;
  }

  .chat_msg_name {
    color: #333;
    font-weight: 600;
  }

  .chat_msg_add_box {
    margin-left: 14px;
  }

  .chat_right .chat_msg_add_box {
    padding-right: 0;
    margin-left: 0;
    margin-right: 14px;
  }

  .chat_msg_content {
    margin-top: 8px;
    height: auto;
    min-height: 40px;
    position: relative;
  }

  .chat_msg_content_p {
    height: auto;
    padding: 12px 13px;
    border-radius: 5px;
    color: #333;
    max-width: 80%;
    background: #FFFFFF;
  }

  .chat_msg_content_p span,
  .chat_msg_content_p span p,
  .chat_msg_content_p p {
    font-size: inherit;
    font-family: inherit;
  }

  .chat_right .chat_msg_content p {
    color: #FFF;
  }

  .chat_msg_content_i {
    height: 0px;
    width: 0px;
    border-width: 6px;
    border-style: solid;
    position: relative;
    left: -4px;
    position: absolute;
  }

  .chat_right .chat_msg_content_i {
    left: auto;
    right: -4px;
  }

  .chat_status {
    padding: 1px 0.1px 0 10px;
  }

  .chat_voice {
    width: 28px;
    margin: 0 5px;
    float: left;
  }

  .record_warp {
    position: fixed;
    z-index: 99999;
    width: 100%;
    top: 30%;
  }

  .record_bg {
    background-color: rgba(0, 0, 0, 0.5);
    min-width: 50%;
    margin: 0 auto;
    border-radius: 8px;
    text-align: center;
    padding: 2%;
  }

  .record_img {
    min-height: 85px;
    margin: auto;
    margin-top: 8px;
  }

  .record_text {
    border-radius: 4px;
    color: #FFFFFF;
    padding: 8px 10px;
    margin-top: 14px;
    font-weight: 800;
  }

  .chat_location {
    padding: 6px 0;
  }

  .chat_location_img {
    width: 100%;
    height: 120px;
    border-radius: 5px;
    overflow: hidden;
  }

  .aiteBody,
  .unreadBody {
    background: rgba(0, 0, 0, 0.35);
    position: fixed;
    z-index: 1;
    right: 0;
    top: 50px;
    padding: 5px;
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
  }

  .aiteContent {
    margin: 0 10px;
    color: #FFF
  }

  .unreadBody {
    top: 100px;
    background: #FFF;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-right: 0px;
  }

  .aiteStyle {
    background: rgba(0, 0, 0, 0.1);
  }

  .link_box {
    background: #FFF;
    padding: 7px 10px;
    border-radius: 8px;
    margin: 5px 0;
  }

  .link_box_title {
    line-height: 1.6;
    color: #000000;
  }

  .link_addbox {
    margin-top: 7px;
  }

  .link_box_desc {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    color: #b2b2b2;
    line-height: 1.4;
  }

  .link_box_img {
    margin: 3px 0;
    margin-left: 5px;
    object-fit: cover;
  }

  .file_box {
    width: 100%;
  }

  .content_img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    flex-shrink: 0;
  }

  .app_tag .tag_text {
    margin-left: 5px;
  }

  .myPopup {
    background-color: rgba(0, 0, 0, .2);
  }

  .itemSex_item {
    width: 80px;
    padding: 10px 0;
  }

  .itemSex_name {
    color: #5E646D;
    text-align: center;
    margin-top: 5px;
  }

  .itemSex_box {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    margin: 10px;
  }

  .itemSex_item {
    display: inline-block;
    border-radius: 1px;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }

  .footer-box {
    position: fixed;
    bottom: 0;
    width: 100%;
  }

  .van-cell-group {
    margin: 0;
  }

  .van-cell-group--inset {
    border-radius: 0;
  }

  .popup-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
  }

  /*展开收起 **************************************************************/
  .hide_preCode_box {
    width: 100%;
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), to(#fff));
    background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
  }

  .arrowOpen {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .arrowClose {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .chatInputBtn {
    padding: 5px !important;
    border: 0;
    background: rgba(0, 0, 0, 0);

    img {
      width: 30px;
    }
  }

  .emoBoxBg {
    height: 130px;
    overflow: auto;
    margin: 10px 0 0 10px;

    .emoBox {
      margin: 5px;

      img {
        width: 30px;
      }
    }
  }

  .extrasBtnsBoxBg {
    height: 130px;
    overflow: auto;
    padding-top: 10px;
    background: #f9f9f9;

    .extrasBtnsBox {
      width: 100px;
      height: 90px;
      position: relative;

      img {
        position: absolute;
        width: 60px;
        left: 0;
        right: 0;
        margin: auto;
      }

      div {
        text-align: center;
        margin-top: 60px;
        font-size: 14px;
      }
    }
  }
}