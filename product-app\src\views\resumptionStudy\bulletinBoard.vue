<template>
  <div class="bulletinBoard">
    <van-pull-refresh v-model="refreshing"
                      success-text=""
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="onLoad"
                :immediate-check="false">
        <van-cell v-for="item in dataList"
                  :key="item.id">
          <!-- 数据列表 -->
          <div class="announcement"
               @click="skipDetails(item)">
            <div class="announcement_title">{{ item.content }}</div>
            <div class="announcement_text">{{ item.moduleView }}</div>
            <div class="isRead" v-if="item.isRead == 0"></div>
          </div>
        </van-cell>
      </van-list>
    </van-pull-refresh>
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog } from 'vant'
export default {
  name: 'bulletinBoard',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id || '',
      pageType: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 跳详情
    const skipDetails = async (_id) => {
      _id.isRead = '1'
      await $api.general.updateState({ id: _id.id })
      if (_id.module === 'survey') {
        router.push({ path: '/survey', query: { type: 'survey', id: _id.paramMap.id } })
      } else if (_id.module === 'notice') {
        router.push({ path: '/noticeDetails', query: { type: _id.module, id: _id.paramMap.id } })
      } else {
        Dialog.alert({
          title: _id.moduleView,
          message: _id.content,
          confirmButtonColor: '#39a9ed'
        }).then(() => {
          // on close
        })
      }
    }
    // 获取公告栏列表列表
    const getList = async () => {
      var { data: list, total } = await $api.general.msgboxList({ pageNo: data.pageNo, pageSize: data.pageSize })
      data.dataList = data.dataList.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    return { ...toRefs(data), $general, dayjs, skipDetails, onLoad, onRefresh }
  }
}
</script>

<style lang="less" scoped>
.bulletinBoard {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .announcement {
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    .isRead {
      width: 5px;
      height: 5px;
      background: red;
      border-radius: 20px;
      position: absolute;
      top: 5px;
      right: 5px;
    }
    .announcement_title {
      font-weight: 700;
    }
    .announcement_text {
      color: #666;
      text-align: right;
      font-size: 14px;
    }
  }
}
</style>
