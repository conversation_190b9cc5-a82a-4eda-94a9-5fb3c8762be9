<template>
  <div class="socials">
    <!-- 顶部默认背景-->
    <div class="bg_default T-flexbox-vertical flex_align_center flex_justify_content"
         :style="'padding-top:'+safeAreaTop+'px;background-image: url('+require('../../assets/img/bg_default_social.png') ">
    </div>
    <!--提交box-->
    <div class="submit_box flex_box flex_align_center"
         :style="'height: '+submitH+'px'">
      <div style="font-size: 12px;">
        <div :style="'font-weight: bold;color:#FFF;font-size:23px'"
             v-html="'提交社情民意'"></div>
        <van-button @click="submitSocial()"
                    plain
                    round
                    :color="appTheme"
                    text="立即提交"></van-button>
      </div>
    </div>
    <van-list v-model:loading="loading"
              :finished="finished"
              finished-text="没有更多了"
              offset="52"
              @load="onLoad">
      <div class="btn_box flex_box T-flex-flow-row-wrap">
        <div v-for="(item, index) in btns"
             :key="index"
             class="btn_warp"
             @click="openSocialList(item.onlyPage)">
          <div v-if="item.show">
            <div class="btn_item"
                 :style="'background-image: url(' + require('../../assets/img/bg_' + item.bg + '.png')">
              <p v-if="item.pointNumber > 0"
                 class="flex_box flex_align_center flex_justify_content text_one"
                 :class="item.pointType == 'big' ? 'item_hot_big' : 'item_hot'"
                 :style="item.pointType == 'big' ? $general.loadConfiguration(-6) + $general.loadConfigurationSize(2) : $general.loadConfigurationSize(-6)"
                 v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></p>
              <div :style="$general.loadConfiguration() + 'font-weight: bold;color:#FFF'"
                   v-html="item.text"></div>
              <div :style="$general.loadConfiguration(-1) + 'color:#FFF;margin-top:0.03rem;'"
                   v-html="'社情民意'"></div>
              <div class="flex_box flex_align_center"
                   style="margin-top: 16px;">
                <div :style="$general.loadConfiguration(-3) + 'color:#FFF;margin-right:0.04rem;'"
                     v-html="'点击查看'"></div>
                <van-icon :color="'#fff'"
                          :size="(($general.appFontSize - 4) * 0.01) + 'rem'"
                          name="arrow"></van-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item_warp"
           v-if="allBtn.show">
        <div class="all_btn_item"
             @click="openSocialList('3')">
          <p v-if="allBtn.pointNumber > 0"
             class="flex_box flex_align_center flex_justify_content text_one"
             :class="allBtn.pointType == 'big' ? 'item_hot_big' : 'item_hot'"
             :style="allBtn.pointType == 'big' ? $general.loadConfiguration(-6) + $general.loadConfigurationSize(2) : $general.loadConfigurationSize(-6)"
             v-html="allBtn.pointType == 'big' ? (allBtn.pointNumber > 99 ? '99+' : allBtn.pointNumber) : ''"></p>
          <div :style="$general.loadConfiguration(-2) + 'font-weight: bold;color:#FFF'"
               v-html="allBtn.text"></div>
          <div class="flex_box flex_align_center"
               style="margin-top: 8px;">
            <div :style="$general.loadConfiguration(-4) + 'color:#FFF;margin-right:4px;'"
                 v-html="'点击查看'"></div>
            <van-icon :color="'#fff'"
                      :size="(($general.appFontSize - 4) * 1) + 'px'"
                      name="arrow"></van-icon>
          </div>
        </div>
      </div>
      <!-- 接收统计图表 -->
      <div class="module_box">
        <div id="barBox"
             style="height: 230px;"></div>
      </div>
      <div class="module_box">
        <div id="pieBox"
             style="height: 200px;margin-left: 10px;"></div>
      </div>
    </van-list>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item, index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage } from 'vant'
import * as echarts from 'echarts'

export default {
  name: 'socials',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      module: '1',
      loading: false,
      finished: false,
      refreshing: false,
      btns: [
        { show: true, type: 'mine', text: '我的', bg: 'blue', pointType: 'big', pointNumber: 0, onlyPage: '1' },
        { show: true, type: 'excellent', text: '优秀', bg: 'yellow', pointType: 'big', pointNumber: 0, onlyPage: '2' }
      ],
      allBtn: { show: false, type: 'all', text: '所有社情民意信息', pointType: 'big', pointNumber: 0 },
      // 折线图
      option1: {
        title: { text: '年度来稿统计/月', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        color: ['#007BFF'], // 线条颜色
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        series: [{
          type: 'line',
          name: '数量',
          smooth: true,
          data: []
        }]
      },
      // 饼图
      option2: {
        title: { text: '来稿类别统计', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '20%',
          orient: 'vertical',
          left: '3%',
          data: []
        },
        color: ['#59F164', '#91FFE4', '#66D9FF', '#E27EF4', '#FF806B', '#F9EA3E'],
        series: [{
          type: 'pie',
          radius: ['35%', '55%'],
          center: ['70%', '50%'],
          avoidLabelOverlap: false,
          label: {
            normal: { show: false, position: 'center', formatter: '{b}\n{c} ({d}%)' },
            emphasis: { show: true, textStyle: { fontSize: '13', color: '#6670AB' } } // 文字至于中间时，这里需为true
          },
          data: []
        }]
      }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getRedPointNum()
      if (data.user.otherInfo.userOtherInfo.isSocailAdmin) {
        data.allBtn.show = true
      }
      getbar()
    })
    // 获取会议和活动红点数量
    const getRedPointNum = async () => {
      const ret = await $api.Networkpolitics.getRedPointNumByModule({ module: 'app' })
      const list = ret ? ret.data || [] : []
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          if (_eItem.code === 'social_check_red_point') {
            data.allBtn.pointNumber = Number(_eItem.count)
          }
        })
      }
    }
    const onRefresh = () => {
      data.option1.xAxis.data = []
      data.option1.series[0].data = []
      data.option2.legend.data = []
      data.option2.series[0].data = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getbar()
    }
    const onLoad = () => {
    }
    // 点击提交页面
    const submitSocial = () => {
      router.push({ name: 'socialsNews', query: { title: '提交社情民意', type: 'add', showDrafts: true } })
    }
    // 点击我的页面
    const openSocialList = (_onlyPage) => {
      switch (_onlyPage) {
        case '1':
          router.push({ name: 'socialsMyList', query: { title: '我的社情民意' } })
          break
        case '2':
          router.push({ name: 'socialsExcellentList', query: { title: '优秀社情民意' } })
          break
        case '3':
          router.push({ name: 'socialsAllList', query: { title: '所有社情民意' } })
          break
        default:
          break
      }
    }
    // 获取年度来搞统计/月
    const getbar = async () => {
      const res = await $api.social.getStatistics({
        years: new Date().getFullYear(),
        countType: 'qdmany'
      })
      console.log('获取年度来搞统计------', res)
      var { data: list } = res
      list.mounths.forEach(function (_eItem, _eIndex, _eArr) {
        data.option1.xAxis.data.push(_eItem.monthName || '')
        data.option1.series[0].data.push(_eItem.amount)
      })
      list.types.forEach(function (_eItem, _eIndex, _eArr) {
        data.option2.legend.data.push(_eItem.typeName || '')
        data.option2.series[0].data.push({ value: _eItem.amount, name: _eItem.typeName || '' })
      })
      setTimeout(() => {
        echarts.init(document.getElementById('barBox')).setOption(data.option1)
        echarts.init(document.getElementById('pieBox')).setOption(data.option2)
      }, 1000)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      data.finished = true
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, submitSocial, openSocialList }
  }
}
</script>
<style lang="less" scoped>
.socials {
  .bg_default {
    background-position: bottom;
    background-size: cover;
    height: 220px;
  }

  .submit_box {
    width: 100%;
    padding: 0 33px;
    position: absolute;
    top: 15%;

    .van-button {
      padding: 0px 100px;
      border: 0px;
      margin-top: 15px;
    }
  }

  .btn_box {
    padding: 14px 7px 0 7px;

    .btn_warp {
      width: 50%;
      padding: 0 7px;

      .btn_item {
        width: 100%;
        height: 100%;
        min-height: 106px;
        background-size: cover;
        -webkit-background-size: cover;
        background-position: 50%;
        padding: 19px;
        border-radius: 5px;
        position: relative;
      }
    }
  }

  .item_warp {
    padding: 14px 14px 0 14px;

    .all_btn_item {
      width: 100%;
      height: 100%;
      min-height: 77px;
      padding: 19px;
      border-radius: 5px;
      position: relative;
      border-radius: 5px;
      background: linear-gradient(
        -14deg,
        #fd3839 0%,
        #f96262 51%,
        #ff908f 100%
      );
    }
  }

  .module_box {
    background: #fff;
    border-radius: 10px;
    margin: 15px;
  }
  .item_hot_big {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
  }
  .item_hot {
    position: absolute;
    top: 5px;
    right: calc(50% - 2px);
    background: #f92323;
    border-radius: 50%;
  }
}
</style>
