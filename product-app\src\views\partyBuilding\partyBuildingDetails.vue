<template>
  <div class="partyBuildingDetails">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="partyInfoDetailsBox">
        <div class="partyTitle">{{details.name}}</div>
        <div class="partyInfo flex_box flex_align_center flex_justify-content_end">
          <div class="flex_placeholder"> </div>
          <div>
            <div style="font-size: 14px;color: #549dff;">{{details.typeName}}</div>
            <div style="margin-top: 10px;font-size: 15px;color: #b0b0b0;">{{details.publicDate}}</div>
          </div>
        </div>
        <div class="partyContent"
             @click="setImgBigger"
             v-html="details.content"></div>
      </div>
    </van-pull-refresh>
  </div>
  <commentList ref="commentList"
               @openInputBoxEvent="openInputBoxEvent"
               @freshState="freshState"
               :commentData="commentData"
               :type="type"
               :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox">
    <inputBox ref="inputBox"
              :inputData="inputData"
              @addCommentEvent="addCommentEvent"
              :type="type"
              :id="id" />
  </footer>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import inputBox from '../../components/inputBox/inputBox.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'partyBuildingDetails',
  components: {
    inputBox,
    commentList,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    // const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      refreshing: false,
      details: {},
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentList: null,
      inputBox: null,
      type: 31
    })
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      getInfo()
    }
    const addCommentEvent = (value) => {
      console.log(value)
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      console.log(value)
      data.inputBox.changeType(2, value)
    }
    // 获取详情
    const getInfo = async () => {
      const res = await $api.partyBuilding.partyInfo(data.id)
      data.details = res.data
      data.refreshing = false
    }
    return { ...toRefs(data), onRefresh, addCommentEvent, openInputBoxEvent }
  }
}
</script>
<style lang="less">
.partyBuildingDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .partyInfoDetailsBox {
    margin: 15px;
    .partyTitle {
      font-weight: bold;
      line-height: 1.5;
      font-size: 18px;
    }
    .partyInfo {
      margin-top: 15px;
    }
    .partyContent {
      margin-top: 20px;
      line-height: 28px;
    }
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>
