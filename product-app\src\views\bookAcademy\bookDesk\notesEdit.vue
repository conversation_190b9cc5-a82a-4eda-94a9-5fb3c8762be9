<template>
  <div class="noteEdit">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="submit">
        <!-- <template #right>
          <van-icon name="guide-o"
                    size="20" />&nbsp;确定
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <van-field id="message"
               v-model="message"
               rows="11"
               type="textarea"
               show-word-limit
               maxlength="1000"
               placeholder="请输入笔记内容"></van-field>
    <van-button @click="submit"
                type="parmary"
                :color="appTheme"
                block>确定</van-button>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, Sticky, NavBar } from 'vant'
// import moment from 'moment'
export default {
  name: 'noteEdit',
  components: {
    [Button.name]: Button,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      noteId: route.query.noteId,
      title: route.query.title || '编辑笔记',
      user: JSON.parse(sessionStorage.getItem('user')),
      message: ''
    })
    onMounted(() => {
      getData()
    })
    if (data.title) {
      document.title = data.title
    }
    const getData = async () => {
      var ret = await $api.bookAcademy.syReadingNotesInfo({ id: data.noteId })
      if (ret && ret.data) {
        data.message = ret.data.noteContent
      }
    }
    const submit = async () => {
      var ret = await $api.bookAcademy.editReadingNotes({ id: data.noteId, noteContent: data.message, bookId: data.id })
      if (ret && ret.data) {
        Toast('修改' + (ret.errcode === 200 ? '成功' : '失败'))
        setTimeout(() => {
          onClickLeft()
        }, 1000)
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), submit, onClickLeft }
  }
}
</script>
<style lang="less" scoped>
.noteEdit {
  width: 100%;
  background: #f9f9f9;
  position: relative;
  .van-button {
    width: calc(100% - 20px);
    margin: 10px;
  }
}
</style>
