<template>
  <div class="userFeedback">
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item, index) in 10"
                    :key="index"
                    title
                    :row="1"></van-skeleton>
    </div>
    <van-pull-refresh v-else
                      v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="已加载完"
                offset="52"
                @load="onLoad"
                class="userFeedback_list">
        <div class="userFeedback_list_item"
             v-for="(item) in dataList"
             :key="item.id"
             v-show="user.id == item.feedbackUserId">
          <div class="userFeedback_list_item_title">
            <van-icon name="notes-o"
                      class="icons" />
            我的反馈：
          </div>
          <div class="userFeedback_list_item_con">
            {{ item.feedbackContent }}
          </div>
          <div class="userFeedback_list_item_img">
            <van-image width="2rem"
                       class="img_item"
                       v-for="(it) in item.imageVo"
                       :key="it"
                       height="2rem"
                       fit="cover"
                       @click.stop="ImagePreview({
                        images: [it.fullUrl],
                        closeable: true
                      })"
                       :src="it.fullUrl" />
          </div>
          <div class="userFeedback_list_item_time">
            {{ item.feedbackDate }}
          </div>
          <div class="userFeedback_list_item_box"
               v-for="it in item.replyListVos"
               :key="it.id">
            <div class="userFeedback_list_item_title">
              <span></span>
              回复
            </div>
            <div class="userFeedback_list_item_con">
              <span>{{ it.alternateOme }}</span>
              <p>{{ it.replyContent }}</p>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <div class="add"
         @click="add">
      问题投诉
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
// import moment from 'moment'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Tab, Tabs, Empty, ImagePreview } from 'vant'
export default {
  name: 'userFeedback',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Sticky.name]: Sticky,
    [Empty.name]: Empty
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      year: route.query.year || '',
      type: route.query.type || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')) || JSON.parse(localStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [
      ]
    })
    onMounted(() => {
      setTimeout(() => {
        window.scrollTo({
          top: 0
        })
      }, 100)
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {
    })
    const onRefresh = () => {
      data.dataList = []
      data.pageNo = 1
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    const add = () => {
      router.back()
    }
    const getList = async (page = 3) => {
      const res = await $api.general.systemfeedbackList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      })
      res.data.forEach(item => {
        item.publishDate = dayjs(item.publishDate).format('YYYY-MM-DD HH:mm ')
        item.attachmentList = item.attachmentList || []
      })
      data.dataList = data.dataList.concat(res.data)
      // data.dataList.forEach(item => {
      //   item.img = item.imageVo.fullUrl || ''
      //   item.path = item.remarks
      // })
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    const onClickLeft = () => history.back()
    const details = async (row) => {
      router.push({ name: 'userFeedbackDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), details, onClickLeft, onRefresh, onLoad, $general, router, confirm, ImagePreview, add }
  }
}
</script>
<style lang="less" scoped>
.userFeedback {
  background-size: 100%;
  min-height: 100vh;

  .add {
    position: fixed;
    text-align: center;
    width: 60px;
    // left: 0;
    right: 30px;
    // margin: auto;
    padding: 10px;
    bottom: 130px;
    color: #fff;
    background: #286fff;
    border-radius: 30px;
  }

  .userFeedback_list {
    min-height: 100vh;
    background: #fff;

    .userFeedback_list_item {
      padding: 10px;
      border-bottom: 1px solid #dfdfdf;

      .userFeedback_list_item_title {
        .icons {
          margin: 0px 5px 0px 0;
          font-weight: 700;
        }

        >span {
          display: inline-block;
          width: 3px;
          height: 15px;
          background: #257bf2;
          margin: 3px 10px 0px 0;
        }

        display: flex;
        align-items: center;
        font-size: 16px;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .userFeedback_list_item_con {
        margin: 10px 0;

        >span {
          display: inline-block;
          margin: 0px 0 10px;
          font-size: 14px;
        }
      }

      .userFeedback_list_item_img {
        .img_item {
          margin: 5px 5px;
        }
      }

      .userFeedback_list_item_time {
        margin: 10px 0 10px;
        font-size: 14px;
        color: #a8a8a8;
      }
    }
  }
}
</style>
