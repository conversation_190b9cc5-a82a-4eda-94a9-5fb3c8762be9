export default new Promise((resolve, reject) => {
  const script = document.createElement('script')
  script.src = 'http://res.wx.qq.com/open/js/jweixin-1.2.0.js'
  script.onload = function () {
    // 微信 SDK 加载完成后执行的代码
    console.log('微信 SDK 加载完成')
    // 在这里可以调用 wx.scanQRCode() 方法
    // 例如：
    const wx = window.wx
    console.log('wx=======>', wx)
    resolve(wx)
  }
  script.onerror = function () {
    reject(new Error('无法加载微信 SDK'))
  }
  document.body.appendChild(script)
})
