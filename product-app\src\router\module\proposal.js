// 提案撰写
const proposalNew = () => import('@/views/proposal/proposalNew')
// 所有提案
const proposalList = () => import('@/views/proposal/proposalList')
// 我的提案
const myProposalList = () => import('@/views/proposal/myProposalList')
// 提案详情
const proposalDetails = () => import('@/views/proposal/proposalDetails/proposalDetails')
// 沟通情况
const memberCommunication = () => import('@/views/proposal/memberCommunication')
// 答复意见详情
const proposalReply = () => import('@/views/proposal/proposalReply')
// 满意度新增
// const proposalSatisfaction = () => import('@/views/proposal/proposalSatisfaction')
// 满意度详情
const proposalSatisfactionDetails = () => import('@/views/proposal/proposalSatisfactionDetails')

const notice = [
  {
    path: '/proposalNew',
    name: 'proposalNew',
    component: proposalNew,
    meta: {
      title: '提案撰写',
      keepAlive: true
    }
  },
  {
    path: '/proposalList',
    name: 'proposalList',
    component: proposalList,
    meta: {
      title: '所有提案',
      keepAlive: true
    }
  },
  {
    path: '/myProposalList',
    name: 'myProposalList',
    component: myProposalList,
    meta: {
      title: '我的提案',
      keepAlive: true
    }
  },
  {
    path: '/proposalDetails',
    name: 'proposalDetails',
    component: proposalDetails,
    meta: {
      title: '提案详情',
      keepAlive: false
    }
  },
  {
    path: '/memberCommunication',
    name: 'memberCommunication',
    component: memberCommunication,
    meta: {
      title: '沟通情况',
      keepAlive: false
    }
  },
  {
    path: '/proposalReply',
    name: 'proposalReply',
    component: proposalReply,
    meta: {
      title: '答复意见详情',
      keepAlive: false
    }
  },
  // {
  //   path: '/proposalSatisfaction',
  //   name: 'proposalSatisfaction',
  //   component: proposalSatisfaction,
  //   meta: {
  //     title: '满意度测评',
  //     keepAlive: false
  //   }
  // },
  {
    path: '/proposalSatisfactionDetails',
    name: 'proposalSatisfactionDetails',
    component: proposalSatisfactionDetails,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  }
]
export default notice
