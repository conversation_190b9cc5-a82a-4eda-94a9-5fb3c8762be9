const speechMaterialList = () => import('@/views/speechMaterial/speechMaterialList')
const speechMaterialDetails = () => import('@/views/speechMaterial/speechMaterialDetails')
const speechMaterialNew = () => import('@/views/speechMaterial/speechMaterialNew')
const myList = () => import('@/views/speechMaterial/myView/myList')
const speechMaterial = [{
  path: '/speechMaterialList',
  name: 'speechMaterialList',
  component: speechMaterialList,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/speechMaterialDetails',
  name: 'speechMaterialDetails',
  component: speechMaterialDetails,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/speechMaterialNew',
  name: 'speechMaterialNew',
  component: speechMaterialNew,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/myList',
  name: 'myList',
  component: myList,
  meta: {
    title: '列表',
    keepAlive: true
  }
}]
export default speechMaterial
