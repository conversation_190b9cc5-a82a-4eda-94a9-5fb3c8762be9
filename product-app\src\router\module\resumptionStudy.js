
const resumptionStudy = () => import('@/views/resumptionStudy/resumptionStudy')
const resumptionStudyColumnList = () => import('@/views/resumptionStudy/resumptionStudyColumnList')
const bulletinBoard = () => import('@/views/resumptionStudy/bulletinBoard')

const resumptionStudys = [{
  path: '/resumptionStudy',
  name: 'resumptionStudy',
  component: resumptionStudy,
  meta: {
    title: '琴岛学社',
    keepAlive: true
  }
}, {
  path: '/resumptionStudyColumnList',
  name: 'resumptionStudyColumnList',
  component: resumptionStudyColumnList,
  meta: {
    title: '栏目列表',
    keepAlive: true
  }
}, {
  path: '/bulletinBoard',
  name: 'bulletinBoard',
  component: bulletinBoard,
  meta: {
    title: '公告栏列表',
    keepAlive: true
  }
}, {
  path: '/libraryList',
  name: 'libraryList',
  component: () => import('@/views/bookAcademy/library/library.vue')
}, {
  path: '/bookDeskHomes',
  name: 'bookDeskHomes',
  component: () => import('@/views/bookAcademy/bookDesk/books.vue')
}]
export default resumptionStudys
