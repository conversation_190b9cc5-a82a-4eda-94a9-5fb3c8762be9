<template>
  <div class="attractInvestmentDetailsCopy">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="详情" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="details-container">
        <!-- 标题 -->
        <div class="title">{{ details.title || '孟庆斌主席在成都市拜访清华四川能源互联网研究院' }}</div>

        <!-- 日期和标签 -->
        <div class="header-info">
          <div class="date">{{ formatDate(details.publishDate) || '2025-04-02' }}</div>
          <div class="tag">{{ getTypeText(details.type) || '外出拜访' }}</div>
        </div>

        <!-- 详情信息 -->
        <div class="info-section">
          <div class="info-item" v-if="details.publishDept">
            <div class="label">地点：</div>
            <div class="value">{{ details.publishDept }}</div>
          </div>

          <div class="info-item" v-if="details.projectWay">
            <div class="label">带队领导姓名及职务：</div>
            <div class="value">{{ details.projectWay }}</div>
          </div>

          <div class="info-item" v-if="details.companyNumber">
            <div class="label">企业序号：</div>
            <div class="value">{{ details.companyNumber }}</div>
          </div>

          <div class="info-item" v-if="details.projectAreaName">
            <div class="label">拜访企业名称：</div>
            <div class="value">{{ details.projectAreaName }}</div>
          </div>

          <div class="info-item" v-if="details.projectSuggestion">
            <div class="label">洽谈项目名称：</div>
            <div class="value">{{ details.projectSuggestion }}</div>
          </div>

          <div class="info-item" v-if="details.projectAppeal">
            <div class="label">洽谈项目价值：</div>
            <div class="value">{{ details.projectAppeal }}</div>
          </div>

          <div class="info-item" v-if="details.businessName">
            <div class="label">洽谈情况：</div>
            <div class="value content-text" v-html="details.businessName"></div>
          </div>

          <!-- 如果没有数据，显示默认内容 -->
          <template v-if="!details.publishDept && !details.projectWay && !details.projectAreaName">
            <div class="info-item">
              <div class="label">地点：</div>
              <div class="value">北京</div>
            </div>

            <div class="info-item">
              <div class="label">带队领导姓名及职务：</div>
              <div class="value">其他，市政协副主席，市工商联主席</div>
            </div>

            <div class="info-item">
              <div class="label">企业序号：</div>
              <div class="value">1</div>
            </div>

            <div class="info-item">
              <div class="label">拜访企业名称：</div>
              <div class="value">清华四川能源互联网研究院</div>
            </div>

            <div class="info-item">
              <div class="label">洽谈项目名称：</div>
              <div class="value">建设一体飞行平台及发展游戏项目</div>
            </div>

            <div class="info-item">
              <div class="label">洽谈项目价值：</div>
              <div class="value">储备在谈</div>
            </div>

            <div class="info-item">
              <div class="label">洽谈情况：</div>
              <div class="value content-text">
                重点围绕低空经济生态打造，在青岛地区开展飞行平台及发展游戏项目等方面进行了深入交流。双方将持续飞行系统等部分产品，推动项目落地。最终，双方就青岛低空经济应用通道</div>
            </div>
          </template>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, PullRefresh } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'attractInvestmentDetailsCopy',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [PullRefresh.name]: PullRefresh
  },
  setup () {
    const route = useRoute()
    const $general = inject('$general')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')

    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false
    })

    if (data.title) {
      document.title = data.title
    }

    if (typeof (window.api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }

    onMounted(() => {
      getInfo()
    })

    const onRefresh = () => {
      setTimeout(() => {
        getInfo()
      }, 520)
    }

    // 详情请求
    const getInfo = async () => {
      try {
        const res = await $api.ImportantWork.info(data.id)
        var { data: details } = res
        data.details = details
        data.refreshing = false
      } catch (error) {
        console.error('获取详情失败:', error)
        data.refreshing = false
      }
    }

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0')
    }

    // 获取类型文本
    const getTypeText = (type) => {
      const typeMap = {
        1: '外出拜访',
        2: '在青接待',
        3: '自主举办'
      }
      return typeMap[type] || '外出拜访'
    }

    const onClickLeft = () => {
      history.back()
    }

    return {
      ...toRefs(data),
      $general,
      onRefresh,
      onClickLeft,
      formatDate,
      getTypeText
    }
  }
}
</script>
<style lang="less">
.attractInvestmentDetailsCopy {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .details-container {
    padding: 20px;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      line-height: 1.4;
      margin-bottom: 16px;
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    .header-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #F4F4F4;

      .date {
        font-size: 14px;
        color: #666666;
        font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      }

      .tag {
        background: rgba(0, 122, 255, 0.12);
        border: 1px solid #007AFF;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        color: #007AFF;
        font-weight: 500;
      }
    }

    .info-section {
      .info-item {
        margin-bottom: 20px;

        .label {
          font-size: 16px;
          color: #999999;
          font-weight: 600;
          margin-bottom: 8px;
          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }

        .value {
          font-size: 16px;
          color: #333333;
          font-weight: 600;
          line-height: 1.5;
          font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;

          &.content-text {
            line-height: 1.6;
            font-weight: normal;
          }
        }
      }
    }
  }
}

// 导航栏样式
.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  }
}
</style>
