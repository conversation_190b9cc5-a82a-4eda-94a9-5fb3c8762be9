import {
  HTTP
} from '../http.js'
class partyBuilding extends HTTP {
  partyList (params) {
    return this.request({ url: '/party/list?', data: params })
  }

  findPartyConfigTypeAndName (params) {
    return this.request({ url: '/partyconfig/findPartyConfigTypeAndName?', data: params })
  }

  partyInfo (params) {
    return this.request({ url: `/party/info/${params}` })
  }
}
export {
  partyBuilding
}
