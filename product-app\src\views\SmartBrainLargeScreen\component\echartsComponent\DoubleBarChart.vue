<template>
  <div :id="id" class="DoubleBarChart"></div>
</template>

<script>
import { onMounted, watch, onBeforeUnmount, computed } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'DoubleBarChart',
  props: {
    id: { type: String, required: true },
    data: { type: Array, required: true }, // [{ name, value1, value2 }]
    legend: { type: Array, default: () => ['报送篇数', '采用篇数'] },
    color: { type: Array, default: () => ['#56A0FF', '#FF7B93'] },
    title: { type: String, default: '' }
  },
  setup (props) {
    let chartInstance = null
    // 自动生成 xAxis, series1, series2
    const xAxis = computed(() => props.data.map(item => item.name))
    const series1 = computed(() => props.data.map(item => Number(item.value1)))
    const series2 = computed(() => props.data.map(item => Number(item.value2)))

    // 工具函数：支持色值和渐变对象
    const getBarColor = (color) => {
      if (Array.isArray(color) && color.length === 2) {
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: color[0] },
            { offset: 1, color: color[1] }
          ]
        }
      }
      // 兼容字符串和对象
      if (typeof color === 'string') {
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color },
            { offset: 1, color }
          ]
        }
      }
      return color
    }

    const setChart = () => {
      if (!chartInstance) {
        chartInstance = echarts.init(document.getElementById(props.id))
      }
      const option = {
        title: {
          text: props.title,
          subtext: '',
          left: 6,
          top: 30,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          subtextStyle: {
            color: '#aaa',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderRadius: 8,
          textStyle: { color: '#fff' }
        },
        legend: {
          data: props.legend,
          top: 0,
          right: 10,
          itemWidth: 16,
          itemHeight: 8,
          textStyle: { color: '#AEB8C2', fontSize: 12 }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '28%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxis.value,
          axisLine: { lineStyle: { color: '#E5EAF3' } },
          axisLabel: { color: '#AEB8C2', fontSize: 12, interval: 0, rotate: props.id === 'party_double_line' ? 0 : 30 },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { lineStyle: { color: '#F2F3F5' } },
          axisLabel: { color: '#AEB8C2', fontSize: 12 }
        },
        series: [
          {
            name: props.legend[0],
            type: 'bar',
            data: series1.value,
            barWidth: 10,
            itemStyle: {
              color: getBarColor(props.color[0]),
              borderColor: '#fff',
              borderRadius: 8,
              borderWidth: 2
            }
          },
          {
            name: props.legend[1],
            type: 'bar',
            data: series2.value,
            barWidth: 10,
            itemStyle: {
              color: getBarColor(props.color[1]),
              borderColor: '#fff',
              borderRadius: 8,
              borderWidth: 2
            }
          }
        ]
      }
      chartInstance.setOption(option)
    }

    const resizeChart = () => {
      if (chartInstance) chartInstance.resize()
    }

    onMounted(() => {
      setTimeout(setChart, 0)
      window.addEventListener('resize', resizeChart)
    })

    watch(() => props.data, setChart, { deep: true })

    onBeforeUnmount(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', resizeChart)
    })

    return {}
  }
}
</script>

<style scoped>
.DoubleBarChart {
  width: 100%;
  height: 100%;
}
</style>
