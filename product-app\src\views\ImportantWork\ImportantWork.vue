<template>
  <div class="ImportantWork">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="市委交办重点工作" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <!-- 顶部图 -->
    <img src="../../assets/img/icon_work_top_bg.png" alt="" style="width: 100%;height: 100%;">
    <template v-for="(section, index) in visibleSections" :key="section.id">
      <!-- 工作通知 -->
      <div class="work_notice" :class="{ 'first-section': index === 0 }" v-if="section.title === '工作通知'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">工作通知</span>
          </div>
          <div class="header_right" @click="openMore('notice')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="work_notice_content" v-for="item in workNotice" :key="item.id" @click="openDetails(item, 'notice')">
          {{ item.title }} </div>
      </div>
      <!-- 工作专班 -->
      <div class="work_team" :class="{ 'first-section': index === 0 }" v-else-if="section.title === '工作专班'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">工作专班</span>
          </div>
          <div class="header_right" @click="openMore('team')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="work_team_content">
          <div class="work_team_tabs">
            <div class="work_team_tab" :class="{ active: workTeamTab === 0 }" @click="changeWorkTeamTab(0)">青岛国际航运中心建设
            </div>
            <div class="work_team_tab" :class="{ active: workTeamTab === 1 }" @click="changeWorkTeamTab(1)">青港合作及港澳招商
            </div>
          </div>
          <div class="work_team_tab_content" v-for="item in workTeamList" :key="item.id"
            @click="openDetails(item, 'team')">
            <div class="work_team_item_title">{{ item.title }}</div>
            <div class="work_team_item_date_icon">
              <img class="work_team_item_icon" src="../../assets/img/icon_work_team_time.png" alt="">
              <span class="work_team_item_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</span>
            </div>
            <div class="work_team_item_line"></div>
          </div>
        </div>
      </div>
      <!-- 顶格推进重大项目 -->
      <div class="major_projects" :class="{ 'first-section': index === 0 }" v-else-if="section.title === '顶格推进重大项目'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">顶格推进重大项目</span>
          </div>
          <div class="header_right" @click="openMore('majorProjects')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="major_projects_content">
          <div class="major_project_item" v-for="item in majorProjects" :key="item.id"
            @click="openDetails(item, 'majorProjects')">
            <div class="major_project_left">
              <div class="major_project_title">{{ item.title }}</div>
              <div class="major_project_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</div>
            </div>
            <img class="major_project_img" v-if="item.imageListVo[0]" :src="item.imageListVo[0].filePath" alt="" />
          </div>
        </div>
      </div>
      <!-- 联系企业 -->
      <div class="contact_company" :class="{ 'first-section': index === 0 }" v-else-if="section.title === '联系企业'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">联系企业</span>
          </div>
          <div class="header_right" @click="openMore('contactCompany')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="contact_company_content">
          <div class="contact_company_item" v-for="(expert) in contactCompany" :key="expert.name"
            @click="openDetails(expert, 'contactCompany')">
            <div class="contact_company_title">{{ expert.title }}</div>
            <!-- <div class="contact_company_enterprise_name">{{ expert.businessName }}</div>
            <div class="contact_company_enterprise_desc"
              v-html="expert.briefIntroduction ? expert.briefIntroduction.replace(/<[^>]+>/g, '').replace(/&[a-z]+;/g, '') : ''">
            </div> -->
          </div>
        </div>
      </div>
      <!-- 乡村振兴联系点 -->
      <div class="village_contact_point" :class="{ 'first-section': index === 0 }"
        v-else-if="section.title === '乡村振兴联系点'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">乡村振兴联系点</span>
          </div>
          <div class="header_right" @click="openMore('villageContactPoint')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="village_contact_point_content">
          <div class="village_contact_point_item" v-for="item in villageContactPoints" :key="item.id"
            @click="openDetails(item, 'villageContactPoint')">
            <div class="village_contact_point_icon_title">
              <img class="village_contact_point_icon" src="../../assets/img/icon_folder.png" alt="icon" />
              <span class="village_contact_point_title">{{ item.title }}</span>
            </div>
            <!-- <div class="village_contact_point_desc"
              v-html="item.content ? item.content.replace(/<[^>]+>/g, '').replace(/&[a-z]+;/g, '') : ''"></div> -->
          </div>
        </div>
      </div>
      <!-- 联系服务专家 -->
      <div class="contact_service_experts" :class="{ 'first-section': index === 0 }"
        v-else-if="section.title === '联系服务专家'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">联系服务专家</span>
          </div>
          <div class="header_right" @click="openMore('contactServiceExperts')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="contact_service_experts_content">
          <div class="expert_item" v-for="(expert) in serviceExperts" :key="expert.name"
            @click="openDetails(expert, 'contactServiceExperts')">
            <img class="expert_avatar" src="@/assets/img/icon_user_experts_headimg.png" alt="avatar" />
            <div class="expert_info">
              <div class="expert_name">{{ expert.title }}</div>
              <div class="expert_position">{{ expert.businessName }}</div>
              <!-- <div class="expert_title" v-html="expert.content"></div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 招商引资情况统计 -->
      <div class="attract_investment" :class="{ 'first-section': index === 0 }"
        v-else-if="section.title === '招商引资情况统计'">
        <div class="header_box">
          <div class="header_left">
            <span class="header_left_line"></span>
            <span class="header_left_title">招商引资情况统计</span>
          </div>
          <div class="header_right" @click="openMore('attractInvestment')">
            <span class="header_right_more">更多</span>
            <img class="header_right_arrow" src="../../assets/img/icon_word_right_arrow.png" alt="">
          </div>
        </div>
        <div class="attract_investment_content">
          <div class="investment_card" v-for="item in attractInvestmentList" :key="item.id"
            @click="openDetails(item, 'attractInvestment')">
            <div class="investment_title">{{ item.title }}</div>
            <div class="investment_bottom">
              <span class="investment_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</span>
              <span class="investment_type_btn">{{ item.type == '1' ? '外出拜访' : item.type == '2' ? '在青接待' : item.type ==
                '3' ? '自主举办' : '' }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { inject, reactive, toRefs, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'ImportantWork',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '重点工作',
      user: JSON.parse(sessionStorage.getItem('user')),
      workNotice: [],
      workTeamTab: 0, // 当前tab
      workTeamList: [],
      majorProjects: [],
      contactCompany: [],
      villageContactPoints: [],
      serviceExperts: [],
      attractInvestmentList: [],
      visibleSections: [] // 新增
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = true
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      console.log('打开了重点工作。')
      getColumnList()
    })
    // 获取栏目
    const getColumnList = async () => {
      const res = await $api.ImportantWork.getColumnList({ pageNo: 1, pageSize: 10 })
      if (res && res.data) {
        data.visibleSections = res.data.filter(item => item.isApp === '1').sort((a, b) => a.sort - b.sort)
        nologin()
      }
    }
    // 获取配置
    const nologin = async () => {
      const res = await $api.general.nologin({ codes: 'workId' })
      data.workId = res.data.workId.split(',')
      await getNoticeList()
      await getTeamList()
      await getMajorProjectsList()
      await getContactCompanyList()
      await getVillageContactPointList()
      await getContactExpertsList()
      await getAttractInvestmentList()
    }
    // 获取工作通知
    const getNoticeList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[0]
      })
      if (res && res.data) {
        data.workNotice = res.data.slice(0, 4)
      }
    }
    // 获取工作专报
    const getTeamList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[1],
        type: data.workTeamTab + 1
      })
      if (res && res.data) {
        data.workTeamList = res.data.slice(0, 2)
      }
    }
    // 获取顶格推进重大项目
    const getMajorProjectsList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[2]
      })
      if (res && res.data) {
        data.majorProjects = res.data.slice(0, 2)
      }
    }
    // 获取联系企业
    const getContactCompanyList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[3]
      })
      if (res && res.data) {
        data.contactCompany = res.data.slice(0, 2)
      }
    }
    // 获取乡村振兴联系点
    const getVillageContactPointList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[4]
      })
      if (res && res.data) {
        data.villageContactPoints = res.data.slice(0, 2)
      }
    }
    // 获取联系服务专家
    const getContactExpertsList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[5]
      })
      if (res && res.data) {
        data.serviceExperts = res.data.slice(0, 2)
      }
    }
    // 获取招商引资情况统计
    const getAttractInvestmentList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 10,
        columnId: data.workId[6]
      })
      if (res && res.data) {
        data.attractInvestmentList = res.data.slice(0, 3)
      }
    }
    // 打开更多页面
    const openMore = (_type) => {
      switch (_type) {
        case 'notice':
          router.push({ name: 'WorkNoticeList', query: { columnId: data.workId[0] } })
          break
        case 'team':
          router.push({ name: 'WorkTeamColumnList', query: { columnId: data.workId[1], type: data.workTeamTab + 1 } })
          break
        case 'majorProjects':
          router.push({ name: 'MajorProjectsColumnList', query: { columnId: data.workId[2] } })
          break
        case 'contactCompany':
          router.push({ name: 'contactCompanyColumnList', query: { columnId: data.workId[3] } })
          break
        case 'villageContactPoint':
          router.push({ name: 'villageContactPointList', query: { columnId: data.workId[4] } })
          break
        case 'contactServiceExperts':
          router.push({ name: 'contactServiceExpertsList', query: { columnId: data.workId[5] } })
          break
        case 'attractInvestment':
          router.push({ name: 'attractInvestmentColumnList', query: { columnId: data.workId[6] } })
          break

        default:
          break
      }
    }
    // 打开详情页面
    const openDetails = (_item, _type) => {
      var attachmentList = _item.fileListVo || []
      var attachInfo = []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          item.id = _eItem.id || ''
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.size = _eItem.fileSize || ''
          item.iconInfo = $general.getFileTypeAttr(item.url.split('.')[item.url.split('.').length - 1])
          attachInfo.push(item)
        })
      }
      switch (_type) {
        case 'notice':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'WorkNoticeDetails', query: { id: _item.id } })
          }
          break
        case 'team':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'WorkTeamDetails', query: { id: _item.id } })
          }
          break
        case 'majorProjects':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'MajorProjectsDetails', query: { id: _item.id } })
          }
          break
        case 'contactCompany':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'contactCompanyDetails', query: { id: _item.id } })
          }
          break
        case 'villageContactPoint':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'villageContactPointDetails', query: { id: _item.id } })
          }
          break
        case 'contactServiceExperts':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'contactServiceExpertsDetails', query: { id: _item.id } })
          }
          break
        case 'attractInvestment':
          if (attachmentList.length > 0) {
            annexClick(attachInfo[0])
          } else {
            router.push({ name: 'attractInvestmentDetails', query: { id: _item.id } })
          }
          break
        default:
          break
      }
    }
    const annexClick = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.host === '*************') {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://*************/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        } else {
          router.push({ name: 'pdfFilePreview', query: { url: 'http://www.cszysoft.com/appShare/qdzx/pdf/web/viewer.html?file=' + item.url, title: item.name } })
        }
      } else {
        var param = {
          id: item.id,
          url: item.url,
          name: item.name
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    // 工作专班tab切换
    const changeWorkTeamTab = (idx) => {
      data.workTeamTab = idx
      getTeamList()
    }

    const onClickLeft = () => {
      if (typeof (api) === 'undefined') return history.back()
      // eslint-disable-next-line no-undef
      api.closeWin()
    }
    return { ...toRefs(data), $api, openMore, openDetails, changeWorkTeamTab, onClickLeft, $general, dayjs }
  }
}
</script>
<style lang="less">
.ImportantWork {
  width: 100%;
  min-height: 100%;
  background: #F4F4F4;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 4px;
        height: 18px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_more {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_arrow {
        width: 23px;
        height: 23px;
      }
    }
  }

  .work_notice {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    height: 197px;
    margin: 12px 12px 12px 12px;

    .work_notice_content {
      font-size: 15px;
      color: #333333;
      font-family: Source Han Serif SC, Source Han Serif SC;
      margin: 12px 15px;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .work_team {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;
    height: 274px;

    .work_team_content {
      height: calc(100% - 60px);
      margin: 14px 15px;

      .work_team_tabs {
        display: flex;
        margin-bottom: 16px;

        .work_team_tab {
          flex: 1;
          text-align: center;
          padding: 8px 0;
          font-weight: bold;
          font-size: 14px;
          color: #999999;
          border-radius: 18px;
          background: #f0f2f5;
          margin-right: 8px;
          font-family: Source Han Serif SC, Source Han Serif SC;

          &:last-child {
            margin-right: 0;
          }
        }

        .work_team_tab.active {
          background: #007AFF;
          color: #fff;
          font-weight: bold;
        }
      }

      .work_team_tab_content {
        .work_team_item_title {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          font-family: Source Han Serif SC, Source Han Serif SC;
          margin-bottom: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .work_team_item_date_icon {
          display: flex;
          align-items: center;

          .work_team_item_icon {
            width: 12px;
            height: 12px;
          }

          .work_team_item_date {
            font-size: 12px;
            color: #999;
            margin-left: 5px;
          }
        }

        .work_team_item_line {
          border-bottom: 1px solid #EBEBEB;
          margin: 15px 0;
        }
      }
    }
  }

  .major_projects {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;

    .major_projects_content {
      padding: 14px 15px;

      .major_project_item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .major_project_left {
          flex: 1;
          margin-right: 12px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 103px;

          .major_project_title {
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .major_project_date {
            font-size: 14px;
            color: #999;
          }
        }

        .major_project_img {
          width: 121px;
          height: 103px;
          border-radius: 10px;
          object-fit: cover;
        }
      }
    }
  }

  .contact_company {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;

    .contact_company_content {
      padding: 0 15px;

      .contact_company_item {
        padding: 15px 0 15px 0;
        border-bottom: 1px solid #f0f0f0;

        .contact_company_title {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }

        .contact_company_enterprise_name {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
          margin-top: 6px;
        }

        .contact_company_enterprise_desc {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
          margin-top: 6px;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .village_contact_point {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;
    // height: 289px;

    .village_contact_point_content {
      padding: 15px 15px 0 15px;

      .village_contact_point_item {
        padding: 10px 0;
        border-bottom: 1px solid #e5eaf3;

        &:last-child {
          border-bottom: none;
        }

        .village_contact_point_icon_title {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .village_contact_point_icon {
            width: 17px;
            height: 17px;
            margin-right: 8px;
          }

          .village_contact_point_title {
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .village_contact_point_desc {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .contact_service_experts {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;

    .contact_service_experts_content {
      padding: 5px 15px;

      .expert_item {
        display: flex;
        align-items: flex-start;
        position: relative;
        padding: 15px 0 15px 0;
        border-bottom: 1px solid #f0f0f0;

        .expert_avatar {
          width: 19px;
          height: 17px;
          margin-right: 12px;
          object-fit: cover;
          margin-top: 5px;
        }

        .expert_info {
          flex: 1;
          display: flex;
          align-items: center;

          .expert_name {
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
          }

          .expert_position {
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
            margin-left: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 70%;
          }

          .expert_title {
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: 500;
            font-size: 14px;
            color: #666666;
            margin-top: 6px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .attract_investment {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin: 0 12px 12px 12px;

    .attract_investment_content {
      padding: 0px 15px;

      .investment_card {
        padding: 18px 0 14px 0;
        border-bottom: 1px solid #f0f0f0;

        .investment_title {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          margin-bottom: 12px;
        }

        .investment_bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .investment_date {
            font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
            font-size: 12px;
            color: #666666;
          }

          .investment_type_btn {
            background: rgba(0, 122, 255, 0.12);
            border: 1px solid #007AFF;
            border-radius: 2px;
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: 500;
            font-size: 12px;
            color: #007AFF;
            padding: 3px;
          }
        }
      }
    }
  }

  // 新增：第一个板块的margin和定位
  .first-section {
    margin: -30px 12px 12px 12px;
    position: relative;
    z-index: 2;
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
