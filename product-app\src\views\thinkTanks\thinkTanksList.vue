<template>
  <div class="thinkTanksList">
    <van-tabs v-model:active="active"
              @change="onRefresh"
              :color="appTheme"
              :title-active-color="appTheme"
              sticky>
      <van-tab v-for="item in tabsData"
               :key="item.id"
               :name="item.id"
               :title="item.value">
        <van-pull-refresh v-model="refreshing"
                          style="min-height: 80vh;"
                          success-text="刷新成功"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <div v-show="active==='0'">
              <!-- 搜索框 -->
              <div id="search"
                   style="border-radius: 10px;"
                   class="search_box"
                   :style="$general.loadConfiguration() ">
                <div class="search_warp flex_box">
                  <div @click="search();"
                       class="search_btn flex_box flex_align_center flex_justify_content">
                  </div>
                  <form class="flex_placeholder flex_box flex_align_center search_input"
                        action="javascript:return true;">
                    <input id="searchInput"
                           class="flex_placeholder"
                           :style="$general.loadConfiguration(-1)"
                           placeholder="请输入搜索内容"
                           maxlength="100"
                           type="search"
                           ref="btnSearch"
                           @keyup.enter="search()"
                           v-model="seachText" />
                    <div v-if="seachText"
                         @click="seachText='';search();"
                         class="search_btn flex_box flex_align_center flex_justify_content">
                      <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                                :color="'#ccc'"
                                :name="'clear'"></van-icon>
                    </div>
                  </form>
                </div>
              </div>
              <div v-if="territoryData.length || listData.length"
                   class="flex_box"
                   style="min-height: 100%;"
                   :style="$general.loadConfiguration(-1)">

                <van-sidebar v-if="territoryData.length"
                             v-model="territoryActive"
                             @change="onSidebarChange">
                  <van-sidebar-item v-for="(item,index) in territoryData"
                                    :style="(index==territoryActive?'color:'+appTheme:'')"
                                    :key="index"
                                    :title="item.name"></van-sidebar-item>
                </van-sidebar>
                <!--数据列表-->
                <ul class="vue_newslist_box flex_placeholder">
                  <li v-for="(item,index) in listData"
                      :key="index"
                      class="expert_item flex_box click"
                      @click="openDetails(item)">
                    <img class="expert_img"
                         :style="$general.loadConfigurationSize([42,59])"
                         :src="item.url" />
                    <div class="flex_placeholder">
                      <div class="expert_name text_one2"
                           :style="$general.loadConfiguration()"
                           v-html="item.name"></div>
                      <div class="expert_position text_two"
                           :style="$general.loadConfiguration(-2)"
                           v-html="item.position"></div>
                      <div class="flex_box flex_align_end"
                           style="margin-top:0.07rem;">
                        <div class="flex_placeholder expert_partisan text_one2"
                             :style="$general.loadConfiguration(-3)"
                             v-html="item.partisan"></div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>

            <div v-show="active==='1'">
              <!--搜索-->
              <div id="search"
                   class="search_box flex_box flex_align_center"
                   :style="$general.loadConfiguration()">
                <div class="flex_placeholder search_warp flex_box">
                  <div @click="btnSearch()"
                       class="search_btn img_btn flex_box flex_align_center flex_justify_content"><van-icon :size="((appFontSize)*0.01)+'rem'"
                              :color="'#999'"
                              name="search"></van-icon></div>
                  <form class="flex_placeholder flex_box flex_align_center search_input"
                        action="javascript:return true;"><input :style="$general.loadConfiguration(-3)"
                           placeholder="请输入搜索内容"
                           maxlength="100"
                           type="search"
                           ref="btnSearch"
                           @keyup.enter="btnSearch()"
                           v-model="seachText" /></form>
                </div>
                <van-dropdown-menu style="height:24px;"
                                   :active-color="appTheme"
                                   :style="$general.loadConfiguration(-3)">
                  <van-dropdown-item v-model="years.value"
                                     @change="changeData(0)"
                                     :options="years.data"></van-dropdown-item>
                </van-dropdown-menu>
              </div>
              <!--数据列表-->
              <ul v-if="resultListData.length"
                  class="vue_newslist_box resultList">
                <li v-for="(item,index) in resultListData"
                    :key="index"
                    class="expert_item flex_box click"
                    @click="details(item)">
                  <img class="expert_img"
                       :style="$general.loadConfigurationSize([64,85])"
                       :src="item.url" />
                  <div class="flex_placeholder">
                    <div class="expert_name"
                         :style="$general.loadConfiguration()"
                         v-html="item.name"></div>
                  </div>
                </li>
              </ul>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
/* eslint-disable */
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Tag, Toast, Sidebar, SidebarItem, DropdownMenu, DropdownItem } from 'vant'
export default {
  name: 'thinkTanksList',
  components: {
    [Tag.name]: Tag,
    [Toast.name]: Toast,
    [Sidebar.name]: Sidebar,
    [SidebarItem.name]: SidebarItem,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      tabsData: [
        { id: '0', value: '专家库' },
        { id: '1', value: '履职成功库' }
      ],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      territoryActive: 0,
      territoryData: [], // 分类
      listData: [], // 列表数据
      resultListData: [], // 履职成功库数据
      seachText: '',
      years: { value: '', data: [] }
    })

    onMounted(() => {
      var nowYear = new Date().getFullYear()
      data.years.data.push({ text: "选择年", value: '' })
      for (let i = nowYear; i >= 2018; i--) {
        data.years.data.push({ text: i, value: i })
      }
      data.years.value = ''
      getTerritoryData()
    })

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.resultListData = []
      data.loading = true
      data.finished = false
      getList()
      assemblyList()
    }
    const onLoad = () => {
      data.loading = true
      getList()
      assemblyList()
    }
    // 获取智库类型字典
    const getTerritoryData = async () => {
      const res = await $api.general.pubkvs({
        types: 'work_territory'
      })
      var { data: list } = res
      list.work_territory.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        data.territoryData.push({ name: _eItem.value, id: _eItem.id })
      })
      if (data.territoryActive >= data.territoryData.length) {
        data.territoryActive = 0
      }
      getList()
    }
    // 获取智库列表
    const getList = async () => {
      var res = []
      var datas = {
        pageNo: data.pageNo,
        pageSize: 10,
        keyword: data.seachText,
        isWiseman: 1,
        isUsing: 1,
        workTerritory: data.territoryData[data.territoryActive].id
      }
      res = await $api.thinkTanks.thinkTanksList(datas)
      var { data: list, total } = res
      list.forEach(item => {
        item.name = (item.userName || '').replace(new RegExp(data.seachText, 'g'), data.seachText ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>' : '')
        item.url = item.headImg || ''
        item.position = item.position || ''
        item.partisan = item.partisan || ''
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      data.pageNo = data.pageNo + 1
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    // 获取履职成功库列表
    const assemblyList = async () => {
      const res = await $api.thinkTanks.assemblyList({
        pageNo: data.pageNo,
        pageSize: 10,
        keyword: data.seachText,
        year: data.years.value
      })
      console.log('获取履职成功库列表==>', res)
      var { data: list } = res
      list.forEach(item => {
        item.name = (item.name || '').replace(new RegExp(data.seachText, 'g'), data.seachText ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>' : '')
        item.url = require('../../assets/img/icon_thinktank_results.png')
      })
      data.resultListData = data.resultListData.concat(list)
    }
    const onSidebarChange = (row) => {
      onRefresh()
    }
    const openDetails = (row) => {
      router.push({ name: 'thinkTanksDetails', query: { id: row.id } })
    }
    const details = (row) => {
      router.push({ name: 'resultDetails', query: { id: row.id } })
    }
    const changeData = () => {
      onRefresh()
    }
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
    }
    return { ...toRefs(data), onRefresh, dayjs, onLoad, openDetails, $general, search, onSidebarChange, changeData, details }
  }
}
</script>

<style lang="less" >
.thinkTanksList {
  width: 100%;
  background: #ffffff;
  .vue_newslist_box {
    padding: 12px 14px;
    .expert_item {
      background: #ffffff;
      border-radius: 8px;
      .expert_img {
        object-fit: cover;
        border-radius: 4px;
        margin-right: 14px;
      }
      .expert_name {
        font-weight: 600;
        color: #333333;
        line-height: 2;
      }
      .expert_position {
        font-weight: 500;
        color: #666666;
        line-height: 1.3;
        min-height: 2.572em;
      }
      .expert_partisan {
        font-weight: 500;
        color: #b2b9d9;
        margin-bottom: 2px;
      }
    }
    .expert_item + .expert_item {
      margin-top: 12px;
    }
  }
  .resultList {
    padding: 0 12px;
    .expert_item {
      padding: 12px;
      background: #ffffff;
      border-radius: 8px;
    }
    .expert_item + .expert_item {
      margin-top: 12px;
    }
    .expert_img {
      object-fit: cover;
      border-radius: 4px;
      margin-right: 12px;
    }
    .expert_name {
      font-weight: 600;
      color: #333333;
      line-height: 2;
    }
    .expert_position {
      font-weight: 500;
      color: #666666;
      line-height: 1.3;
      min-height: 2.572em;
    }
    .expert_partisan {
      font-weight: 500;
      color: #b2b9d9;
      margin-bottom: 2px;
    }
    .expert_btn {
      padding: 6px 15px;
      border-radius: 12px;
    }
  }
}
</style>

