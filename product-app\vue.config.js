const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const webpack = require('webpack')
const environment = require('./build')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const isProduction = process.env.NODE_ENV !== 'development'

module.exports = {
  /* 部署服务器的URL配置 */
  publicPath: './',
  /* 生产环境的 map 文件 */
  productionSourceMap: false,
  /* css相关配置 */
  css: {
    /* 生产环境的 css map 文件 */
    sourceMap: false
  },
  // eslint-disable-next-line 
  chainWebpack: config => {
    config.resolve.alias
      .set('@', resolve('src'))
  },
  configureWebpack: config => {
    config.plugins.push(
      new webpack.DefinePlugin({
        'process.env.STAGE': JSON.stringify(environment.stage),
        'process.env.LOCAL_URL': JSON.stringify(environment.localUrl)
      })
    )
    // 生产环境相关配置
    if (isProduction) {
      config.devtool = false
      const productionGzipExtensions = ['html', 'js', 'css']
      config.plugins.push(
        new CompressionWebpackPlugin({
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: new RegExp(
            '\\.(' + productionGzipExtensions.join('|') + ')$'
          ),
          threshold: 10240, // 只有大小大于该值的资源会被处理 10240
          minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
          deleteOriginalAssets: false // 删除原文件
        })
      )
      config.optimization = {
        splitChunks: {
          cacheGroups: {
            styles: {
              name: 'styles',
              test: /\.(sa|sc|c)ss$/,
              chunks: 'all',
              enforce: true
            }
          }
        }
      }
    }
    // },
    // devServer: {
    //   host: '***********'
  }
}
