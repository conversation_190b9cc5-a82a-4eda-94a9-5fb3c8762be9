.mainModuleDetails {
  width: 100%;
  min-height: 100%;
  background: #eee;
  .mainModuleText {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 20px;
    color: #333333;
    position: relative;
    padding-left: 6px;
    margin-bottom: 6px;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 2px;
      height: 15px;
      background: #3088fe;
      opacity: 1;
      border-radius: 10px;
    }
  }
  .mainModuleDetailsBox {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 10px;
    .mainModuleTitle {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .mainModuleInfoBox {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 0;
      .mainModuleInfo {
        display: flex;
        div {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 22px;
          color: #999999;
        }
        div + div {
          margin-left: 16px;
        }
      }
      .mainModuleState {
        height: 18px;
        background: #ffe7dc;
        border-radius: 2px;
        padding: 0 9px;
        font-size: 10px;
        font-family: PingFang SC;
        line-height: 18px;
        color: #fe7530;
      }
    }
    .joinUserBox {
      padding-bottom: 16px;
      .joinUser {
        overflow: hidden;
        span {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 20px;
          color: #666666;
        }
      }
      .viewMore {
        font-size: 10px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 14px;
        color: #3088fe;
        text-align: right;
      }
    }
    .mainModuleContent {
      width: 100%;
      padding-bottom: 16px;
      img {
        width: 100%;
      }
    }
    .attachmentBox {
      width: 100%;
      padding-bottom: 16px;
      .attachmentItem {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 18px;
        color: #3088fe;
        padding: 3px 0;
      }
    }
    .situationBox {
      width: 100%;
      .situation {
        width: 50%;
        display: inline-block;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 28px;
        color: #333333;
      }
    }
  }
  .mainModuleProcessBox {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    .mainModuleProcess {
      width: 100%;
      margin-top: 22px;
      .mainModuleProcessItem {
        padding-left: 38px;
        padding-bottom: 24px;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 13px;
          width: 1px;
          height: 100%;
          background: #dcebff;
          opacity: 1;
          border-radius: 10px;
        }
        .processIcon {
          position: absolute;
          top: 0;
          left: 14px;
          width: 15px;
          height: 15px;
          transform: translateX(-50%);
          background: url('../../assets/img/processIcon.png');
          background-size: 100% 100%;
          z-index: 2;
        }
        .processIcone {
          background: url('../../assets/img/processIcone.png');
          background-size: 100% 100%;
        }
        .processName {
          font-size: 15px;
          font-family: PingFang SC;
          font-weight: 600;
          line-height: 15px;
          padding-bottom: 8px;
          color: #333333;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 17px;
            color: #999999;
          }
        }
        .processText {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 20px;
          color: #666666;
          display: flex;
          justify-content: space-between;
          span {
            padding: 0 12px;
            height: 20px;
            border: 1px solid #3088fe;
            border-radius: 2px;
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 20px;
            color: #3088fe;
          }
        }
      }
      .mainModuleProcessItem:last-child {
        &::after {
          background: transparent;
        }
      }
    }
    .mainModuleButton {
      padding-bottom: 22px;
      .van-button {
        height: 36px;
        font-size: 16px;
        border-radius: 4px;
        background: #3088fe;
        margin-top: 16px;
      }
    }
  }
  .review {
    width: 100%;
    padding: 16px;
    .reviewState {
      text-align: center;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 20px;
      color: #3088fe;
      padding-bottom: 12px;
      border-bottom: 1px dotted #ccc;
    }
    .reviewResults,
    .reviewOpinion {
      font-size: 15px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 22px;
      color: #333333;
      padding-top: 12px;
      span {
        font-weight: 600;
      }
    }
  }
  .mainModuleReview {
    margin-top: 10px;
    width: 100%;
    background-color: #fff;
    padding: 16px 0;
    .van-form {
      width: 100%;
      .van-cell-group {
        width: 100%;
        margin: 0;
        border-radius: 0;
      }
      .van-cell__title {
        label {
          font-size: 15px;
          font-weight: 600;
          color: #333333;
        }
      }
      .van-field__control {
        font-size: 14px;
      }
      .van-switch__node {
        font-size: 20px;
      }
      .van-radio-group {
        .van-radio {
          font-size: 14px;
          margin-right: 9px;
          margin-bottom: 9px;
        }
      }
      .newDivBox {
        .newDiv {
          display: inline-block;
          height: 21px;
          background: #dcebff;
          opacity: 1;
          border-radius: 2px;
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 21px;
          color: #3088fe;
          padding: 0 6px;
          margin-right: 6px;
        }
      }
      .newButton {
        display: flex;
        justify-content: space-around;
        padding: 36px 18px;
        padding-bottom: 88px;
        .van-button {
          width: 128px;
          height: 36px;
        }
      }
    }
  }
}
