<template>
  <div class="myUser">
    <div class="myUserHead">
      <div v-if="themeImg.url">
        <img :style="'width:100%;height:190px;object-fit: cover;'"
             :src="themeImg.url" />
      </div>
      <div class="myUserHeadUserBox">
        <div class="myUserHeadUser">
          <div class="myUserHeadUserImg">
            <img :src="userInfo.url"
                 alt=""
                 @click="divClick(userInfo)">
          </div>
          <div class="myUserHeadUserInfo">
            <div class="myUserHeadUserName">{{ userInfo.name }}</div>
            <div class="ellipsis">{{ user.position }}</div>
          </div>
        </div>
        <!-- <div class="flex_box flex_align_center wisDom"
             @click="wisDomClick">
          <img src="../../assets/img/zhihuidou/dou.png"
               alt=""
               style="width: 20px;height: 20px;">
          <span style="color: #FFAD2B;font-size: 20px;font-weight: bold;">{{scoreCount}}</span>
          <span style="font-size: 12px;color: #FFAD2B;">个</span>
        </div> -->
      </div>

    </div>
    <div v-for="(nItem, nIndex) in gridBtn"
         :key="nIndex"
         class="grid_bg">
      <div class="grid">
        <van-grid clickable
                  :border="false"
                  :column-num="3">
          <van-grid-item v-for="(item, index) in nItem"
                         :key="index"
                         :info="item.pointNumber > 0 ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"
                         :text="item.name"
                         @click="divClick(item)">
            <template v-slot:default>
              <van-image fit="cover"
                         :style="$general.loadConfigurationSize(18) + 'margin-bottom:8px;'"
                         :src="item.url"></van-image>
              <div class="text_name"
                   v-html="item.name">
              </div>
              <p v-if="item.pointNumber > 0"
                 class="flex_box flex_align_center flex_justify_content text_one"
                 :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
                 :style="item.pointType == 'big' ? 'font-size:12px;height:20px' : 'height:12px'"
                 v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '...' : item.pointNumber) : ''"></p>
            </template>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <van-cell-group>
      <van-cell :title="item.name"
                @click="divClick(item)"
                v-for="item in cellBtn"
                :key="item.id"
                :icon="item.icon"
                is-link />
    </van-cell-group>
    <van-action-sheet v-model:show="editshow"
                      :actions="actions"
                      :description="description"
                      cancel-text="取消"
                      @select="selectProfileAvatar"
                      close-on-click-action />
    <van-action-sheet v-model:show="modifyAvatar"
                      :actions="albumCamera"
                      :description="description"
                      cancel-text="取消"
                      @select="chooseAlbumCamera"
                      close-on-click-action />
    <van-uploader style="display: none;"
                  v-model="fileList"
                  :max-count='1'
                  :after-read="afterRead"
                  ref="chatImg">
    </van-uploader>
    <van-uploader style="display: none;"
                  v-model="fileList"
                  :max-count='1'
                  capture="camera"
                  :after-read="afterReadCamera"
                  ref="chatImgCamera">
    </van-uploader>
  </div>
</template>
<script>

import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, ActionSheet, Toast, Uploader, Grid, GridItem, Image as VanImage } from 'vant'
export default {
  name: 'myUser',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Uploader.name]: Uploader,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      themeImg: { url: '' },
      userInfo: {
        name: '胡烈那',
        url: '',
        position: 'changePhoto',
        click: 'changePhoto'
      },
      editshow: false,
      modifyAvatar: false,
      albumCamera: [
        { name: '相册', color: '#3088fe' },
        { name: '相机', color: '#3088fe' }
      ],
      actions: [
        { name: '查看个人资料', color: '#3088fe' },
        { name: '修改头像', color: '#3088fe' }
      ],
      description: '请选择',
      chatImg: null,
      chatImgCamera: null,
      fileList: [],
      // 几个按钮
      gridBtn: [
        [
          { show: true, name: '履职档案', url: require('../../assets/img/icon_performanceFiles.png'), click: 'performanceFiles', pointNumber: 0 },
          { show: true, name: '我的收藏', url: require('../../assets/img/icon_collect.png'), click: 'myCollection', pointNumber: 0 },
          { show: true, name: '意见反馈', url: require('../../assets/img/icon_collect.png'), click: 'userFeedbacks', pointNumber: 0 }
          // { show: true, name: '活跃度', url: require('../../assets/img/icon_activity.png'), click: 'activation', pointNumber: 0 }
        ]
      ],
      cellBtn: [
        { id: '1', show: true, name: '帮助手册', icon: require('../../assets/img/icon_useraudit.png'), click: 'manual', to: 'helpManual' },
        { id: '2', show: true, name: '用户反馈电话：0532-51915707', click: 'userFeedback', tel: '0532-51915707', icon: '', to: '' }
      ],
      scoreCount: 0
    })
    onMounted(() => {
      getNumber()
      data.themeImg.url = sessionStorage.getItem('appMyBg') || ''
      data.userInfo.name = data.user.userName
      data.userInfo.url = sessionStorage.getItem('userHeadImg') || data.user.headImg
    })
    // 获取智慧豆
    const getNumber = async () => {
      const res = await $api.general.wisdombeanCount({
        userId: data.user.id,
        pageNo: '1',
        pageSize: '10'
      })
      data.scoreCount = res.data
    }
    // 跳转智慧豆页面
    const wisDomClick = () => {
      router.push({ name: 'wisdomBeanList' })
    }
    // 点击事件
    const divClick = (_item) => {
      switch (_item.click) {
        case 'changePhoto':
          changePhoto()
          break
        case 'openWorkPermit':
          router.push({ name: 'openWorkPermit', query: { id: data.user.id } })
          break
        case 'collection':
          router.push({ name: 'favorite', query: { id: data.user.id } })
          break
        case 'useraudit':
          router.push({ name: 'informationModification', query: { id: data.user.id } })
          break
        case 'performanceFiles':
          router.push({ name: 'performanceFilesTypeList', query: { id: data.user.id } })
          break
        case 'myCollection':
          router.push({ name: 'myCollection', query: { id: data.user.id } })
          break
        case 'activation':
          router.push({ name: 'activation', query: { id: data.user.id } })
          break
        case 'userFeedback':
          window.location.href = 'tel:' + _item.tel
          break
        case 'userFeedbacks':
          router.push({ name: 'userFeedbackAdd', query: { id: data.user.id } })
          break
        case 'manual':
          router.push({ name: _item.to, query: { id: data.user.id } })
          break
        default:
          break
      }
    }
    // 选择查看资料还是修改头像
    const selectProfileAvatar = (item) => {
      data.editshow = false
      switch (item.name) {
        case '查看个人资料':
          router.push({ name: 'personalDataView', query: { id: data.user.id } })
          break
        case '修改头像':
          data.modifyAvatar = true
          break
      }
    }
    // 修改头像
    const changePhoto = () => {
      data.editshow = true
    }
    // 选择相册、相机
    const chooseAlbumCamera = (_item) => {
      data.modifyAvatar = false
      switch (_item.name) {
        case '相册':
          data.chatImg.chooseFile()
          break
        case '相机':
          data.chatImgCamera.chooseFile()
          break
      }
    }
    // 上传相册图片
    const afterRead = async (file) => {
      const formData = new FormData()
      formData.append('userId', data.user.id)
      formData.append('areaId', JSON.parse(sessionStorage.getItem('areaId')))
      formData.append('file', file.file)
      var res = await $api.general.fileMyHeadimg(formData)
      if (res && res.errcode === 200) {
        Toast('更换成功' || res.errmsg)
      }
      data.userInfo.url = res.data.fullUrl
      sessionStorage.setItem('userHeadImg', data.userInfo.url)
    }
    // 上传拍照图片
    const afterReadCamera = async (file) => {
      const formData = new FormData()
      formData.append('userId', data.user.id)
      formData.append('areaId', JSON.parse(sessionStorage.getItem('areaId')))
      formData.append('file', file.file)
      var res = await $api.general.fileMyHeadimg(formData)
      if (res && res.errcode === 200) {
        Toast('更换成功' || res.errmsg)
      }
      data.userInfo.url = res.data.fullUrl
      sessionStorage.setItem('userHeadImg', data.userInfo.url)
    }
    // 退出
    const loginOut = () => {
      Dialog.confirm({
        message:
          '此操作将退出当前系统, 是否继续?'
      })
        .then(() => {
          sessionStorage.clear()
          router.push({ name: 'login' })
        })
        .catch(() => {
          // on cancel
        })
    }
    return { ...toRefs(data), loginOut, divClick, selectProfileAvatar, chooseAlbumCamera, afterRead, afterReadCamera, $general, wisDomClick }
  }
}
</script>
<style lang="less">
.myUser {
  width: 100%;
  padding-bottom: 50px;

  .myUserHead {
    width: 100%;

    .myUserHeadUserBox {
      width: 100%;
      padding: 0px 0px 0 20px;
      display: flex;
      align-items: center;
      position: absolute;
      top: 100px;

      .myUserHeadUser {
        width: 100%;
        display: flex;
        padding-bottom: 8px;
        flex: 1;

        .myUserHeadUserImg {
          width: 52px;
          height: 52px;
          border-radius: 15px;
          overflow: hidden;
          background-color: #ccc;
          position: relative;

          img {
            position: absolute;
            height: 100%;
            left: 0;
            right: 0;
            margin: auto;
          }
        }

        .myUserHeadUserInfo {
          width: calc(100% - 82px);
          margin-left: 15px;

          .myUserHeadUserName {
            font-size: 18px;
            font-family: PingFang SC;
            font-weight: 600;
            line-height: 22px;
            padding-bottom: 6px;
            color: #ffffff;
          }

          div {
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 22px;
            color: #e1eeff;
          }
        }
      }

      .wisDom {
        justify-content: center;
        align-items: center;
        background-color: #fff;
        width: 100px;
        padding: 5px 2px;
        border-radius: 16px 0 0 16px;
        position: relative;
        left: 8px;
      }

      .Modify_big {
        background: #fff;
        height: 30px;
        border-radius: 24px;
        padding: 6px;
        width: 86px;
        margin-left: 40px;

        img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }

        .text_edit {
          font-size: 14px;
        }
      }
    }
  }

  .grid_bg {
    box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.08);
    opacity: 1;
    border-radius: 10px;
    background: #fff;
    box-sizing: border-box;

    .text_name {
      font-size: 14px;
      color: #222;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
    }
  }

  .van-cell-group {
    margin-top: 10px;

    .van-icon__image {
      transform: translateY(1px);
    }
  }
}
</style>
