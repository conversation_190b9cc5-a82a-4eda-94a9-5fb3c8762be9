<template>
  <div class="dataList">
    <div class="dataListTitle ellipsisTwo">{{title}}</div>
    <div class="dataListBox">
      <slot></slot>
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue'
export default {
  name: 'dataList',
  props: ['title'],
  setup () {
    const data = reactive({
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less">
.dataList {
  width: 343px;
  background: #ffffff;
  box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.08);
  border-radius: 4px;
  padding: 10px 12px;
  .dataListTitle {
    width: 100%;
    height: 42px;
    font-size: 15px;
    font-family: PingFang SC;
    line-height: 21px;
    color: #333333;
  }
  .dataListBox {
    display: flex;
    align-items: center;
    padding-top: 6px;
    div {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 18px;
      color: #777777;
      margin-left: 20px;
    }
    .state {
      height: 18px;
      background: #dcebff;
      border-radius: 2px;
      padding: 0 10px;
      margin-left: 0;
      color: #3088fe;
    }
  }
}
.dataList + .dataList {
  margin-top: 10px;
}
</style>
