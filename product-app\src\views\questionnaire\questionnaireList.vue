<template>
  <div class="questionnaireList">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>

    <van-pull-refresh v-model="refreshing"
                      style="min-height: 80vh;"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <van-cell v-for="(item,index) in listData"
                    :key="index"
                    clickable
                    class="vue_newslist_item "
                    @click="openDetails(item)">
            <!-- <div v-if="$general.getItemForKey(item.id,redIds,'id')"
                 class="notRead"></div> -->
            <div v-if="questionnaireRedIds.includes(item.id)"
                 style="right:8px;"
                 class="notRead"></div>
            <div class="flex_box flex_align_center">
              <img class="vue_newslist_img"
                   v-if="item.url"
                   :src="item.url" />
              <div class="flex_placeholder vue_newslist_warp">
                <div class="vue_newslist_title text_two"
                     :style="$general.loadConfiguration()">
                  <span v-if="item.isTop == '1'"
                        class="vue_newslist_top"
                        :style="$general.loadConfiguration(-3)">
                    <van-tag plain
                             :color="appTheme">置顶</van-tag>
                  </span>
                  <span class="inherit"
                        v-html="item.title"></span>
                </div>
                <div class="flex_box flex_align_center">
                  <div class="flex_placeholder vue_newslist_time"
                       :style="$general.loadConfiguration(-3)">{{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}</div>
                  <span v-if="item.status"
                        class="social_status"
                        :style="$general.loadConfiguration(-5)+'margin-left:24px;color:'+getStatusColor(item.status,1)"><van-tag :color="getStatusColor(item.status,0)">{{item.status}}</van-tag></span>
                </div>
              </div>
            </div>
          </van-cell>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
/* eslint-disable */
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Tag, Toast } from 'vant'
export default {
  name: 'questionnaireList',
  components: {
    [Tag.name]: Tag,
    [Toast.name]: Toast
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      active: '0',
      appTheme: $appTheme,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      listData: [],
      seachText: '',
      questionnaireRedIds: []
    })

    onMounted(() => {
      onRefresh()
    })

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
      getMeetRedIds()
    }
    // 获取列表未读红点
    const getMeetRedIds = async () => {
      const res = await $api.Networkpolitics.getRedIds({ type: 'questionnaire' })
      data.questionnaireRedIds = res ? res.data || [] : []
    }
    const onLoad = () => {
      data.loading = true
      getList()
    }
    // 获取调查问卷列表
    const getList = async () => {
      var res = []
      var datas = {
        pageNo: data.pageNo,
        pageSize: 10,
        keyword: data.seachText,
        isPublish: 1,
        publishPlatform: 1
      }
      res = await $api.questionnaire.questionnaireList(datas)
      var { data: list, total } = res
      list.forEach(item => {
        // item.id = _eItem.id || ''
        item.title = (item.title || '').replace(new RegExp(data.seachText, 'g'), data.seachText ? '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>' : '')
        item.time = item.startTime || ''
        item.isTop = item.isTop || '0'
        item.status = item.questionnaireStatusValue || ''
        var attachmentList = item.attachmentList || []
        var bigImageforQS = $general.getItemForKey('bigImageforQS', attachmentList, 'moduleType')
        item.url = bigImageforQS ? bigImageforQS.filePath || '' : ''
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      data.pageNo = data.pageNo + 1
      // 数据全部加载完成
      if (data.listData.length >= total) {
        data.finished = true
      }
    }
    //获取各状态颜色   0背景 1颜色 
    const getStatusColor = async (_status, _type) => {
      console.log('_type==>>', _type)
      var colors = {
        '未开始': ['#FFF6D0', '#F6AE31'],
        '进行中': ['#E8F7FF', '#3E9DFF'],
      }
      try {
        return colors[_status][_type]
      } catch (e) {
        return _type == 0 ? '#EBEBEB' : '#999999'
      }
    }
    const openDetails = (row) => {
      router.push({ name: 'questionnaireDetails', query: { id: row.id } })
    }
    const search = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
    }
    return { ...toRefs(data), onRefresh, dayjs, onLoad, openDetails, $general, search, getStatusColor }
  }
}
</script>

<style lang="less" >
.questionnaireList {
  width: 100%;
  #app .vue_newslist_item {
    padding: 12px 12px;
  }
  .vue_newslist_item + .vue_newslist_item {
    border-top: 10px solid #f4f4f4;
  }
  #app .social_status .van-tag {
    border-radius: 3px;
    padding: 5px 10px;
    color: inherit;
  }
}
</style>

