<template>
  <div class="helpManualDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           v-html="details.name"></div>
    </div>
    <div class="n_details_content"
         v-html="details.content"></div>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'helpManualDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    // const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      details: {}
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      genInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        genInfo()
      }, 520)
    }
    // 列表请求
    const genInfo = async () => {
      const res = await $api.general.operationmanualInfo(data.id)
      var { data: details } = res
      data.details = details
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft }
  }
}
</script>
<style lang="less">
.helpManualDetails {
  width: 100%;
  background: #fff;
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 14px 10px;
    box-sizing: border-box;
    position: relative;
    .n_details_title {
      line-height: 1.8;
      font-weight: bold;
      font-size: 10px;
      font-size: 28px;
    }
  }
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
  }
}
</style>
