<template>
  <div class="userFeedbackAdd">
    <van-sticky>
      <van-nav-bar :title="'意见反馈'"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <div class="userFeedbackAdd-box">
      <div class="type_box">
        <div class="type_title">
          反馈类型
        </div>
        <div class="type_list">
          <div :class="{ 'type_list_item': true, 'type_list_items': item.value == typeStatus }"
               v-for="(item, index) in typeList"
               :key="index"
               @click="typerClick(item)">
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>
    <div class="userFeedbackAdd-box">
      <van-cell-group inset>
        <van-field class="newContent"
                   v-model="content"
                   name="content"
                   label="反馈内容"
                   rows="10"
                   maxlength="500"
                   type="textarea"
                   placeholder="请输入反馈内容" />
      </van-cell-group>
    </div>
    <!-- <div class="userFeedbackAdd-box">
      <div class="picUploader">
        <div class="picUploader_title">
          上传图片(最多上传{{ Uploadmax }}张)
        </div>
        <div class="imgloager">
          <div class="img_box"
               v-for="(nItem, nIndex) in attachmentIds"
               :key="nIndex">
            <van-icon name="clear"
                      @click.stop="$general.delItemForKey(nIndex, attachmentIds)"
                      class="clear" />
            <van-image @click.stop="ImagePreview([nItem.url])"
                       width="2rem"
                       height="2rem"
                       fit="cover"
                       :src="nItem.url" />
          </div>
          <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        capture="camera"
                        multiple
                        :disabled="attachmentIds.length == Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
          </van-uploader>
          <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        multiple
                        :disabled="attachmentIds.length == Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
            <div class="photo">
              <van-icon name="photo" />
            </div>
          </van-uploader>
        </div>
      </div>
    </div> -->
    <div class="userFeedbackAdd-submit"
         @click="Submit">提交</div>

    <div class="userFeedbackAdd-toList"
         @click="router.push('/userFeedback')">
      反馈记录
    </div>

  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ImagePreview, Uploader, Image as VanImage, Toast, NavBar } from 'vant'
export default ({
  name: 'schedule',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [NavBar.name]: NavBar,
    [Uploader.name]: Uploader
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      title: '',
      content: '',
      address: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      username: '',
      mobile: '',
      longitude: '',
      dimensionality: '',
      Uploadmax: 6,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      typeList: [],
      typeStatus: '',
      selectHome: {}
    })
    onMounted(async () => {
      console.log('进入h5页面1')
      const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token') || route.query.token
      console.log(JSON.stringify(route.query))
      if (!token) {
        setTimeout(() => {
          sessionStorage.setItem('Sys_token', JSON.stringify(api.getPrefs({ sync: true, key: 'Sys_token' }))) // eslint-disable-line
          sessionStorage.setItem('areaId', JSON.stringify(api.getPrefs({ sync: true, key: 'SYS_SiteID' }))) // eslint-disable-line
          api.setInterfaceStyle({ style: 'light' }) // eslint-disable-line
          const user = {
            userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }), // eslint-disable-line
            position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
            headImg: window.localStorage.getItem('Sys_AppPhoto')
          }
          // data.userName = user.userName
          sessionStorage.setItem('user', JSON.stringify(user))
          console.log(data.user, 'user')
        }, 300)
      } else {
        const res = await $api.general.changearea()
        var {
          data: {
            token: tokens,
            user,
            menus,
            areas
          }
        } = res
        sessionStorage.setItem('menus', JSON.stringify(menus))
        sessionStorage.setItem('token', JSON.stringify(tokens))
        sessionStorage.setItem('Sys_token', JSON.stringify(tokens))
        sessionStorage.setItem('user', JSON.stringify(user))
        sessionStorage.setItem('areas', JSON.stringify(areas))
        sessionStorage.setItem('areaId', user.areaId)
        data.user = user
        // appList()
      }
      data.seachArea = JSON.parse(sessionStorage.getItem('seachArea')) || {}
      data.username = data.user.userName
      data.mobile = data.user.mobile
      selectTitle()
    })
    const selectTitle = async () => {
      const { data: list } = await $api.general.pubkvs({
        types: 'complaint_type'
      })
      data.typeList = list.complaint_type
    }
    const imgUploader = async (file) => {
      console.log(file, 'file')
      console.log(data.images, 'data.images')
      const item = { url: file.content, uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: 'lzbl' }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'lzbl')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      data.attachmentIds.push(item)
    }
    // 提交
    const Submit = async () => {
      if (data.content === '') {
        Toast('请输入反馈内容')
        return
      }
      if (data.typeStatus === '') {
        Toast('请选择反馈类型')
        return
      }
      var params = {
        feedbackContent: data.content,
        feedbackUserName: data.username,
        mobile: data.mobile,
        feedbackUserId: data.user.id,
        feedbackType: data.typeStatus,
        feedbackDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        image: data.attachmentIds.map(item => item.uploadId).join(',')
      }
      const { errcode } = await $api.general.systemfeedbackAdd(params)
      if (errcode === 200) {
        Toast('反馈成功')
        router.push('/userFeedback')
      }
      // console.log(params, 'params')
    }
    const typerClick = (item) => {
      data.typeStatus = item.value
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), dayjs, route, router, onClickLeft, $api, imgUploader, ImagePreview, $general, Submit, typerClick }
  }
})
</script>
<style lang='less'>
.userFeedbackAdd {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;
  margin-top: 30px;

  .userFeedbackAdd-toList {
    position: fixed;
    text-align: center;
    width: 60px;
    // left: 0;
    right: 30px;
    // margin: auto;
    padding: 10px;
    bottom: 130px;
    color: #fff;
    background: #286fff;
    border-radius: 30px;
  }

  .type_box {
    padding: 15px;

    .type_title {}

    .type_list {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .type_list_item {
        background: #f4f5f7;
        padding: 3px 5px;
        margin-right: 5px;
        border-radius: 5px;
      }

      .type_list_items {
        background: #286fff;
        color: #fff;
      }
    }
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
  }

  @font-face {
    font-family: "PingFangSC-Medium";
  }

  .userFeedbackAdd-box {
    background: #fff;
    // border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  .userFeedbackAdd-submit {
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    width: 90%;
    height: 56px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }
}
</style>
