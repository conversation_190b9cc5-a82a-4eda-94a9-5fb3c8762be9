<template>
  <div class="commentList">
    <transition name="van-slide-up">
      <div class="T-flexbox-vertical">
        <div style="height: 30px;"
             :style="{paddingTop:(safeAreaTop)+'px'}"></div>
        <div class="header_box flex_box flex_align_center van-hairline--bottom">
          <div :style="$general.loadConfigurationSize(0,'h')+'width:5px;background:'+appTheme+';margin-left:16px;'"></div>
          <div class="comment_title"
               :style="$general.loadConfiguration(1)"
               v-html="commentInfo.hint"></div>
          <div class="flex_placeholder"></div>
          <div class="header_btn flex_box flex_align_center flex_justify_content"
               @click="onRefresh"><img :style="$general.loadConfigurationSize(4,'w')"
                 src="../../assets/img/icon_refresh.png" /></div>
          <!-- <div class="header_btn flex_box flex_align_center flex_justify_content"
               @click="close()"
               :style="$general.loadConfiguration()">关闭</div> -->
        </div>
        <!--数据列表-->
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <div id="details_comment"
                 class="n_details_comment_box T-flex-item">
              <ul class="n_details_comment">
                <li v-for="(item,index) in dataList"
                    :key="index"
                    class="n_details_comment_li">
                  <div class="flex_box">
                    <van-image @click="msgImgTap(item)"
                               :style="$general.loadConfigurationSize(14)"
                               class="comment_userImg"
                               round
                               fit="cover"
                               :src="item.url"></van-image>
                    <div class="flex_placeholder">
                      <!--右边内容-->
                      <div class="flex_box flex_align_center">
                        <div class="comment_userName"
                             :style="$general.loadConfiguration(-1)"
                             v-html="item.name + (item.extend != 'APP'?'(网民)':'')"></div>
                        <div v-if="item.isCheck"
                             class=""
                             :style="$general.loadConfiguration(-5)">
                          <van-tag :color="item.isCheck=='待审核'?appTheme:'red'">{{item.isCheck}}</van-tag>
                        </div>
                        <div class="flex_placeholder"></div>
                        <div class="like_box flex_box flex_align_center"
                             @click="downLike(item)">
                          <div :style="$general.loadConfiguration(-2)+'color:'+(item.isFabulou?appTheme:'#000')"
                               v-html="item.fabulouCount"></div>
                          <van-icon :color="item.isFabulou?appTheme:'#000'"
                                    :size="(($general.appFontSize+1)*0.01)+'px'"
                                    :name="item.isFabulou?'good-job':'good-job-o'"></van-icon>
                        </div>
                      </div>
                      <!--评论内容-->
                      <div class="comment_content"
                           :style="$general.loadConfiguration(1)"
                           v-html="item.content"></div>
                      <div v-if='item.showFull'
                           :style="$general.loadConfiguration(-3)"
                           class="qunw"
                           @click.stop='fullText(item)'>{{item.isFull ? '收起' : '展开全文'}}</div>
                      <ul v-if="item.nAttach.length != 0"
                          class="comment_file_box flex_box T-flex-flow-row-wrap">

                        <li v-for="(item2,index2) in item.nAttach"
                            :key="index2">
                          <div :style="'background-image:url('+item2.url+')'"
                               @click="previewImg(item.nAttach,index2)"></div>
                        </li>
                      </ul>
                      <div class="flex_box flex_align_center"
                           style="margin-top: 1px;">
                        <div class="comment_time"
                             :style="$general.loadConfiguration(-3)"
                             v-html="item.createTime"></div>
                        <div :style="$general.loadConfiguration(-3)"
                             @click="openMoreComment(item)">·回复</div>
                        <div class="flex_placeholder"></div>
                        <van-icon v-if="item.hasDelete"
                                  :color="'#333'"
                                  class="delete_btn"
                                  :size="(($general.appFontSize+1)*0.01)+'px'"
                                  name="delete"
                                  @click="deleteItem(item,index,dataList)"></van-icon>
                      </div>
                      <!--评论中的评论-->
                      <ul v-if="item.commentList.length != 0"
                          class="comment_reply_box van-hairline--top">
                        <li v-for="(item2,index2) in item.commentList"
                            :key="index2"
                            class="comment_reply_li_box"
                            :class="index2 != item.commentList.length - 1?'van-hairline--bottom':''">
                          <div class="flex_box flex_align_center">
                            <div class="comment_userName"
                                 :style="$general.loadConfiguration(-1)"
                                 v-html="item2.name + (item2.extend != 'APP'?'(网民)':'')"></div>
                            <div v-if="item2.isCheck"
                                 class=""
                                 :style="$general.loadConfiguration(-5)">
                              <van-tag :color="item2.isCheck=='待审核'?appTheme:'red'">{{item2.isCheck}}</van-tag>
                            </div>
                            <div class="flex_placeholder"></div>
                            <div class="comment_time"
                                 :style="$general.loadConfiguration(-6)"
                                 v-html="item2.createTime"></div>
                            <van-icon v-if="item2.hasDelete"
                                      :color="'#333'"
                                      class="delete_btn"
                                      :size="(($general.appFontSize+1)*0.01)+'px'"
                                      name="delete"
                                      @click="deleteItem(item2,index2,item.commentList)"></van-icon>
                          </div>
                          <div class="comment_content"
                               :style="$general.loadConfiguration(-1)"
                               v-html="item2.content"></div>
                          <div v-if='item2.showFull'
                               :style="$general.loadConfiguration(-3)"
                               class="qunw"
                               @click.stop='fullText(item2)'>{{item2.isFull ? '收起' : '展开全文'}}</div>
                          <ul v-if="item2.nAttach.length != 0"
                              class="comment_file_box flex_box T-flex-flow-row-wrap">
                            <li v-for="(item3,index3) in item2.nAttach"
                                :key="index3">
                              <div :style="'background-image:url('+item3.url+')'"
                                   @click="previewImg(item2.nAttach,index3)"></div>
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </transition>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { Toast, NavBar, Sticky, ActionSheet, Image as VanImage, Overlay, Dialog, ImagePreview } from 'vant'
import moment from 'moment'
export default {
  name: 'commentList',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [NavBar.name]: NavBar,
    [VanImage.name]: VanImage,
    [Sticky.name]: Sticky
  },
  props: ['commentData', 'id', 'type'],
  setup (props, context) {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      send: route.query.send || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      pageTip: '',
      dataList: [],
      commentInfo: { name: '', hint: '评论' },
      commentList: [], // 评论数据
      showPage: false,
      moreLength: 140, // 超出多少字 显示展开
      id: '',
      type: ''

    })

    if (data.title) {
      document.title = data.title
    }
    watch(() => data.dataList, (newName, oldName) => {

    })
    const getData = async () => {
      const { data: list, total } = await $api.general.getCommentList({
        keyId: data.id,
        type: data.type || props.type,
        areaId: data.user.areaId,
        isApp: '1',
        isCheck: '1', // 先默认审核通过 后面去除
        pageNo: data.pageNo,
        pageSize: data.pageSize
      })
      var newData = []
      list.forEach(element => {
        var item = {}; var itemData = element
        item.hasDelete = itemData.createBy === data.user.id// id
        item.id = itemData.id || ''// id
        item.extend = itemData.extend || ''// 来源
        item.createBy = itemData.createBy || ''// id
        item.url = itemData.userHeadImg || '../../assets/img/icon_default_user.png'// 用户头像
        item.name = itemData.userName || '匿名用户'// 用户名
        var content = (itemData.content || '')// 评论内容
        item.showFull = content.length > data.moreLength
        item.isFull = false
        item.fullContent = content
        item.content = content
        if (item.showFull) {
          item.content = content.substring(0, data.moreLength) + '...'
        }
        item.createTime = itemData.dateDetail || ''// 评论时间
        // eslint-disable-next-line eqeqeq
        item.isFabulou = $general.trimAll(itemData.isFabulous) == '1'// 是否点赞
        item.fabulouCount = itemData.fabulousCount || 0// 点赞数
        // eslint-disable-next-line eqeqeq
        item.isCheck = itemData.isCheck == '0' ? '待审核' : itemData.isCheck == '2' ? '审核不通过' : ''// 是否审核,0待审核，1审核通过，2审核不通过
        item.nAttach = []
        // 评论中的图片
        var resultBatchAttach = itemData.filePathList || []
        resultBatchAttach.forEach(Attach => {
          var resultBatchAttachItem = { url: (Attach.fullUrl || '') }
          item.nAttach.push(resultBatchAttachItem)
        })
        item.commentList = []
        itemData.children.forEach(commentlist => {
          var item2 = {}; var itemData2 = commentlist
          // eslint-disable-next-line eqeqeq
          item2.hasDelete = itemData2.createBy == data.user.id// id
          item2.id = itemData2.id || ''// id
          item2.extend = itemData2.extend || ''// 来源
          item2.name = itemData2.userName || '匿名用户'// 用户名
          item2.createTime = itemData2.dateDetail || ''// 时间
          var nContent = $general.dealWithCon(itemData2.content || '')// 评论内容
          item2.showFull = nContent.length > data.moreLength
          item2.isFull = false
          item2.fullContent = nContent
          item2.content = nContent
          if (item2.showFull) {
            item2.content = nContent.substring(0, data.moreLength) + '...'
          }
          // eslint-disable-next-line eqeqeq
          item2.isCheck = itemData2.isCheck == '0' ? '待审核' : itemData2.isCheck == '2' ? '审核不通过' : ''// 是否审核,0待审核，1审核通过，2审核不通过
          item2.nAttach = []
          // 评论中的图片
          var resultBatchAttach = itemData2.filePathList || []
          resultBatchAttach.forEach(Attach => {
            var fullUrl = Attach.fullUrl || ''
            var resultBatchAttachItem = { url: fullUrl }
            item2.nAttach.push(resultBatchAttachItem)
          })
          item.commentList.push(item2)
        })
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      console.log(data.dataList)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const search = () => {

    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }

    const onClickLeft = () => history.back()
    const msgImgTap = (item) => {
      console.log(item)
      router.push({ name: 'personData', query: { id: item.createBy } })
    }
    const openMoreComment = (item) => {
      // 打开评论框
      context.emit('openInputBoxEvent', item)
    }
    const fullText = (item) => {
      if (item.isFull) {
        item.content = item.fullContent.substring(0, data.moreLength) + '...'
      } else {
        item.content = item.fullContent
      }
      item.isFull = !item.isFull
    }
    const downLike = async (item) => {
      if (item.isFabulou) { // 当前是已点赞状态  数量-1
        if (item.fabulouCount > 0) { item.fabulouCount-- }
        await $api.general.delFabulous({
          keyId: item.id,
          type: 101, // 评论点赞固定101
          areaId: data.user.areaId
        })
      } else {
        await $api.general.saveFabulous({
          keyId: item.id,
          type: 101, // 评论点赞固定101
          areaId: data.user.areaId
        })
        item.fabulouCount++
      }
      item.isFabulou = !item.isFabulou
    }
    const deleteItem = (item, index, list) => {
      Dialog.confirm({
        title: '',
        message: '确定要删除这条评论吗?'
      })
        .then(async () => {
          const res = await $api.general.delsComment({
            keyId: data.id,
            type: data.type,
            areaId: data.user.areaId,
            ids: item.id
          })
          if (res.errcode === 200) {
            Toast('删除成功')
            list.splice(index, 1)
            context.emit('freshState', true)
          }
        })
        .catch(() => {
          // on cancel
        })
    }

    onMounted(() => {
      // const commentData = props.commentData
      data.id = props.id
      data.type = props.type
      onRefresh()
    })
    const previewImg = (imgs, _index) => {
      var img = []
      imgs.forEach(element => {
        img.push(element.url)
      })
      ImagePreview({ images: img, startPosition: _index, closeable: true })
    }

    return { ...toRefs(data), moment, $general, search, onClickLeft, onRefresh, onLoad, msgImgTap, deleteItem, openMoreComment, fullText, downLike, previewImg }
  }
}
</script>

<style lang="less" scoped>
html,
body {
  background: rgba(0, 0, 0, 0.3);
}
.commentList {
  width: 100%;
  background: #fff;
  .n_details_comment_box {
    background: #fff;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }
  /*评论相关样式*/
  .spacer {
    width: 100%;
    height: 7px;
    background: #efefef;
    position: relative;
  }
  .spacer:after {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    content: "";
    width: 200%;
    height: 200%;
    border-top: 1px solid #dedede;
    border-bottom: 1px solid #dedede;
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    box-sizing: border-box;
  }
  .comment_title {
    padding: 10px;
    color: #000;
  }
  .n_details_comment {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
  .n_details_comment_li {
    position: relative;
    padding: 10px 0;
  }
  .n_details_comment_li:after {
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    content: "";
    width: 200%;
    height: 1px;
    background-color: #dedede;
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    box-sizing: border-box;
  }
  .comment_userImg {
    margin-right: 5px;
  }
  .comment_userName {
    color: #5a9adb;
    padding: 0 5px;
  }
  .comment_content {
    padding: 5px;
  }
  .comment_content * {
    font-size: inherit;
    font-family: inherit;
  }

  .comment_file_box {
    li {
      width: 33.33%;
      position: relative;
      padding: 0 3px;
      box-sizing: border-box;
      margin-bottom: 5px;
      div {
        width: 100%;
        height: 86px;
        background-size: cover;
        -webkit-background-size: cover;
        background-position: 50%;
        margin-top: 5px;
      }
    }
  }
  .comment_time {
    color: #999999;
  }

  .like_box div {
    margin: 0 5px 0 5px;
    color: #333;
  }
  .comment_btn {
    margin-right: 3px;
  }
  .delete_btn {
    padding: 2px 5px;
  }
  .like_box .select_ok {
    color: #174b92;
  }
  .comment_reply_box {
    width: 100%;
    padding: 0 5px;
    box-sizing: border-box;
    margin: 5px 0;
    position: relative;
  }
  .comment_reply_li_box {
    position: relative;
    padding-top: 5px;
    box-sizing: border-box;
  }

  .n_details_other {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
  .n_details_other div {
    padding: 2px 0;
  }

  .reply_box {
    padding: 10px;
    box-sizing: border-box;
    background: #fff;
    border-top: 1px solid #eee;
  }
  .reply_box .comment_content {
    margin-top: 9px;
    padding: 20px;
    background: #fafafc;
    color: #323232;
    line-height: 1.4;
  }
  .qunw {
    color: #2286f5;
    padding: 0 0 5px 5px;
  }

  #app,
  .T-flexbox-vertical {
    height: 100%;
  }
  .header_box {
    background: #fff;
    width: 100%;
  }
  .header_btn {
    color: #555;
    padding: 9px 10px;
  }
}
</style>
