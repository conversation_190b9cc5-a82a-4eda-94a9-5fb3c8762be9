<template>
  <div class="libraryDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <img class="store_hint_img"
             :src="url"
             alt="" />
        <div class="store_details_box"></div>
        <div v-if="switchs.data.length > 1"
             class="flex_placeholder"
             style="background:#fff;"
             :class="switchs.data.length < 4?'flex_box':''">
          <van-tabs v-model:active="switchs.value"
                    @click="tabClick"
                    swipe-threshold="1"
                    sticky
                    :color="appTheme"
                    :title-active-color="appTheme"
                    :ellipsis="false">
            <van-tab v-for="(item) in switchs.data"
                     :key="item.value"
                     :title="item.label"
                     :name="item.value"></van-tab>
          </van-tabs>
        </div>
        <div class="itemSex_box flex_box T-flex-flow-row-wrap">
          <div @click="openBookDetails(nItem)"
               v-for="(nItem) in listData"
               :key="nItem.id"
               class="itemSex_item click">
            <div :style="'width:100px;height:135px;margin:auto;position: relative;'">
              <img v-if="nItem.txt.bookType == '2'"
                   class="item_Sound"
                   :src="icon_hasSound" />
              <img v-if="nItem.txt.isAvailable == '0'"
                   class="item_overdue"
                   src="../../../assets/img/overdue.png" />
              <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                   :src="nItem.img.url" />
            </div>
            <div v-if="nItem.name"
                 class="itemSex_name text_one2"
                 :style="'font-size:14px'"
                 v-html="nItem.name"></div>
            <div v-if="nItem.author"
                 class="itemSex_author text_one2"
                 :style="'font-size:12px'"
                 v-html="nItem.author"></div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'bookDetail',
  components: {
    [NavBar.name]: NavBar, [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const onClickLeft = () => history.back()
    var title = route.query.title
    if (title) { document.title = title }
    const data = reactive({
      id: route.query.id,
      title: route.query.title,
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      listData: [
        // { url: '../../../images/img_test2.png', name: '论语', author: '张素玲' }, { url: '../../../images/img_test3.png', name: '韩非子' }, { url: '../../../images/img_test4.png', name: '论美国的民主' }, { url: '../../../images/img_test5.png', name: '论资本' }
      ], // 列表数据

      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮

      switchs: { value: '', data: [] },
      url: ''

    })
    onMounted(() => {
      onRefresh()
    })
    const getTypeList = async () => {
      var { data: getTypeList } = await $api.bookAcademy.getTypeList({
        id: data.id
      })
      data.url = getTypeList.coverImgUrl || data.icon_no_data
    }
    const getSyTypeTree = async () => {
      var { data: getSyTypeTree } = await $api.bookAcademy.getSyTypeTree({
        parentId: data.id
      })
      if (getSyTypeTree && getSyTypeTree.length !== 0) {
        var getSyTypeTreeData = getSyTypeTree[0].children || []
        var syTypeTreeDataNew = []
        data.switchs.data = [{ label: '所有', value: '' }]
        getSyTypeTreeData.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          syTypeTreeDataNew.push({ label: _eItem.name, value: _eItem.id })
        })
        data.switchs.data = data.switchs.data.concat(syTypeTreeDataNew)
      }
      if (!$general.getItemForKey(data.switchs.value, data.switchs.data, 'value')) {
        data.switchs.value = ''
        getList(true)
      }
    }
    const getBookList = async () => {
      var param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize
      }
      if (!data.switchs.value) {
        param.bookTypeFirstId = data.id
      } else {
        param.bookTypeSecondId = data.switchs.value
      }
      var { data: list } = await $api.bookAcademy.getBookList(param)
      var dataListNew = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '' } }
        item.id = _eItem.id || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
        dataListNew.push(item)
      })
      data.listData = data.listData.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    // 获取数据
    const getList = async (isGetType) => {
      if (!isGetType) {
        getTypeList()
        getSyTypeTree()
      }
      getBookList()
    }

    // tab切换事件
    const tabClick = (_name, _title) => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getList(true)
    }

    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }

    return { ...toRefs(data), onClickLeft, openSearch, openBookDetails, getList, onRefresh, onLoad, tabClick }
  }
}
</script>
<style lang="less" scoped>
@import "./libraryDetails.less";
</style>
