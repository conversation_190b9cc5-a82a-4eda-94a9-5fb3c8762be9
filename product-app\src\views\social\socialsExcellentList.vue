<template>
  <div class="socialsExcellentList">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;display: flex;align-items: center;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box"
           style="flex: 5;">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
      <van-dropdown-menu :active-color="appTheme"
                         :style="$general.loadConfiguration(-1)"
                         style="flex:1.5">
        <van-dropdown-item v-model="year.value"
                           @change="getData"
                           :options="year.data"></van-dropdown-item>
      </van-dropdown-menu>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh"
                      style="min-height: 600px;">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist3_box">
          <div v-for="(item,index) in ExcellentListData"
               :key="index"
               class="vue_newslist3_warp">
            <van-cell clickable
                      class="vue_newslist3_item "
                      @click="details(item,'Excellent')">
              <div class="flex_box">
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two"
                       :style="$general.loadConfiguration(1)">
                    <span v-if="item.isTop == '1'"
                          class="vue_newslist_top"
                          :style="$general.loadConfiguration(-4)">
                      <van-tag plain
                               :color="appTheme">置顶</van-tag>
                    </span>
                    <span class="inherit"
                          v-html="item.titile"></span>
                  </div>
                  <div class="flex_box flex_align_center">
                    <div class="vue_newslist_time"
                         :style="$general.loadConfiguration(-2)+'font-family: Source Han Serif SC;'">{{item.createDate}}</div>
                    <div class="flex_placeholder"></div>
                    <span v-if="item.currentProcessStatus"
                          class="social_status"
                          :style="$general.loadConfiguration(-3)+'margin-left:24px;color:'+getStatusColor(item.currentProcessStatus,1)">
                      <van-tag :color="getStatusColor(item.currentProcessStatus,0)">{{item.currentProcessStatus}}</van-tag>
                    </span>
                  </div>
                </div>
              </div>
            </van-cell>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
                   showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Dialog } from 'vant'
import moment from 'moment'

export default {
  name: 'socialsExcellentList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      seachText: '',
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      module: '1',
      ExcellentListData: [],
      year: { value: '', data: [] }
    })
    var nowYear = new Date().getFullYear()
    var i = {}
    for (i = 2018; i <= nowYear; i++) {
      data.year.data.push({ text: i, value: i })
    }
    data.year.value = ''
    data.year.data.push({ text: '选择年', value: '' })
    const search = () => {
      data.pageNo = 1
      data.ExcellentListData = []
      data.loading = true
      data.finished = false
      ExcellentListData()
    }
    onMounted(() => {
      ExcellentListData()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.ExcellentListData = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      ExcellentListData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    // 获取我的列表
    const ExcellentListData = async () => {
      const res = await $api.social.getSocialList({
        pageNo: 1,
        pageSize: 99,
        keyword: data.seachText,
        years: data.year.value,
        reflecterType: 1,
        isBest: 1
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.createDate = item.createDate ? moment(item.createDate).format('YYYY-MM-DD') : '' // 时间
      })
      data.ExcellentListData = data.ExcellentListData.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.ExcellentListData.length >= total) {
        data.finished = true
      }
    }
    // 获取各状态颜色   0背景 1颜色
    const getStatusColor = (_status, _type) => {
      var colors = {
        采用: ['#E8F7FF', '#3E9DFF'],
        留存: ['#DAFFDB', '#11C845'],
        无效: ['#FFE5E5', '#FF7B7B'],
        待处理: ['#FFF6D0', '#F6AE31']
      }
      try {
        return colors[_status][_type]
      } catch (e) {
        return _type === 0 ? '#eee' : '#999'
      }
    }
    // 详情
    const details = (row, type) => {
      router.push({ name: 'socialsDetails', query: { id: row.id, type: type } })
    }
    // 年份事件
    const getData = async (value) => {
      console.log('value===', value)
      data.year.value = value
      data.ExcellentListData = []
      ExcellentListData()
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, details, search, getStatusColor, getData }
  }
}
</script>
<style lang="less" scoped>
.socialsExcellentList {
  background: #f8f8f8;
  ::v-deep .van-dropdown-item {
    top: 58px !important;
  }
  .social_status .van-tag {
    border-radius: 3px;
    padding: 2px 8px;
    color: inherit;
  }
}
</style>
