<template>
  <div class="MemberInfo">
    <!-- 委员数量 -->
    <div class="members_num_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">委员数量</span>
        </div>
      </div>
      <div class="members_num_statistics">
        <div class="statistics_card card_blue">
          <div class="card_content">
            <div class="card_number">{{ cppccMemberNum }}</div>
            <div class="card_label">政协委员(人)</div>
          </div>
        </div>
        <div class="statistics_card card_yellow">
          <div class="card_content">
            <div class="card_number">{{ standingCommitteeNum }}</div>
            <div class="card_label">政协常委(人)</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 性别占比 -->
    <div class="gender_ratio_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">性别占比</span>
        </div>
      </div>
      <div class="gender_ratio_list">
        <GenderRatioChart id="genderRatio" :male-count="maleCount" :female-count="femaleCount" />
      </div>
    </div>
    <!-- 年龄占比 -->
    <div class="age_proportion_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">年龄占比</span>
        </div>
      </div>
      <div class="age_proportion_list">
        <PieChart id="ageProportion" :chart-data="ageData" />
      </div>
    </div>
    <!-- 学历分析 -->
    <div class="educational_analysis_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">学历分析</span>
        </div>
      </div>
      <div class="educational_analysis_list">
        <RadarChart id="educationRadar" :chart-data="educationData" />
      </div>
    </div>
    <!-- 党派分布 -->
    <div class="party_distribution_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">党派分布</span>
        </div>
      </div>
      <div class="party_distribution_list">
        <div class="party_distribution_table">
          <div class="party_distribution_table_header">
            <span class="party-column">党派</span>
            <span class="count-column">人数</span>
          </div>
          <div class="party_distribution_table_row" v-for="(item, idx) in partyDistribution" :key="item.name"
            :class="{ 'row-alt': idx % 2 === 1 }">
            <span class="party-column">{{ item.name }}</span>
            <span class="count-column">{{ item.meeting }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 界别分布 -->
    <div class="circles_distribution_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">界别分布</span>
        </div>
      </div>
      <div class="circles_distribution_list">
        <horizontalBarEcharts id="circles" :barList="barList" colorStart="#FFFFFF" colorEnd="#EF817C"
          style="height: 280px;" />
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
import GenderRatioChart from './echartsComponent/GenderRatioChart.vue'
import PieChart from './echartsComponent/PieChart.vue'
import RadarChart from './echartsComponent/RadarChart.vue'
import horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'

export default {
  name: 'MemberInfo',
  components: {
    GenderRatioChart,
    PieChart,
    RadarChart,
    horizontalBarEcharts
  },
  setup () {
    const data = reactive({
      cppccMemberNum: '418',
      standingCommitteeNum: '42',
      maleCount: 314,
      femaleCount: 108,
      ageData: [
        { name: '40岁-49岁', value: 168, percentage: '40%', color: '#4A90E2' },
        { name: '30岁-39岁', value: 125, percentage: '30%', color: '#4CD9C0' },
        { name: '50岁-59岁', value: 85, percentage: '20%', color: '#F56A6A' },
        { name: '29岁以下', value: 20, percentage: '5%', color: '#F8E71C' },
        { name: '60岁以上', value: 20, percentage: '5%', color: '#F5A623' }
      ],
      educationData: [
        { name: '本科生', value: 165 },
        { name: '初中', value: 100 },
        { name: '高中', value: 42 },
        { name: '大专', value: 84 },
        { name: '研究生', value: 84 }
      ],
      partyDistribution: [
        { name: '中国共产党', meeting: 20 },
        { name: '中国国民党革命委员会', meeting: 20 },
        { name: '台湾民主自治同盟', meeting: 20 },
        { name: '九三学社中央委员会', meeting: 20 },
        { name: '全国工商联', meeting: 20 },
        { name: '中国致公党', meeting: 20 },
        { name: '中国农工民主党', meeting: 20 },
        { name: '中国民主促进会', meeting: 20 },
        { name: '中国民主建国会', meeting: 20 },
        { name: '中国民主同盟', meeting: 20 },
        { name: '民革中央委员会', meeting: 20 },
        { name: '青岛工商联', meeting: 20 },
        { name: '群众', meeting: 20 }
      ],
      barList: [
        { name: '教育界', value: 35 },
        { name: '医药卫生界', value: 15 },
        { name: '经济界', value: 14 },
        { name: '工商联界', value: 21 },
        { name: '民革界', value: 15 },
        { name: '特邀界', value: 21 },
        { name: '妇联界', value: 8 },
        { name: '工会界', value: 8 },
        { name: '社会福利与社会保障界', value: 14 }
      ]
    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less" scoped>
.MemberInfo {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .members_num_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .members_num_statistics {
      display: flex;
      gap: 15px;
      padding: 20px 15px 10px 15px;

      .statistics_card {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5faff;
        position: relative;
        height: 86px;

        .card_content {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin-left: 55px;
          margin-top: 5px;

          .card_number {
            font-size: 20px;
            color: #4AA3FF;
          }

          .card_label {
            font-size: 14px;
            color: #666;
            margin-top: 2px;
          }
        }
      }

      .card_blue {
        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');
        background-size: 100% 100%;
        background-position: center;
      }

      .card_yellow {
        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');
        background-size: 100% 100%;
        background-position: center;

        .card_number {
          color: #E6B800 !important;
        }
      }
    }
  }

  .gender_ratio_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .gender_ratio_list {
      padding-left: 24px;
      padding-right: 18px;
    }
  }

  .age_proportion_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .age_proportion_list {
      padding: 10px;
      height: 310px;
    }
  }

  .educational_analysis_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .educational_analysis_list {
      padding: 10px;
      height: 330px;
    }
  }

  .party_distribution_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .party_distribution_list {
      margin-top: 15px;

      .party_distribution_table {
        width: 100%;
        background: #fff;

        .party_distribution_table_header,
        .party_distribution_table_row {
          display: flex;
          align-items: center;
          padding: 12px 15px;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .party-column {
            flex: 1;
            text-align: left;
            font-size: 14px;
            color: #333;
          }

          .count-column {
            flex: 1;
            text-align: right;
            font-size: 14px;
            color: #333;
          }
        }

        .party_distribution_table_header {
          background: #F1F8FF;
          font-weight: 600;
          color: #222;
          font-size: 14px;
        }

        .party_distribution_table_row {
          background: #fff;
          color: #333;
          font-size: 14px;

          &.row-alt {
            background: #F1F8FF;
          }
        }
      }
    }
  }

  .circles_distribution_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .circles_distribution_list {}
  }
}
</style>
