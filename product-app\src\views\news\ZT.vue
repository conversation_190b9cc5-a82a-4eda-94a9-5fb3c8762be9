<template>
  <div class="ZT">
    <van-sticky>
      <van-nav-bar v-if="isShowHead" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
      <div>
        <van-search v-model="keyword" @search="search" @clear="search" placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>

    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <!--数据列表-->
        <div v-for="item in dataList" :key="item.id" @click="details(item)">
          <div class="item-box">
            <div class="item-img" :style="'background-image:url(' + item.themeImg + ')'"></div>
            <!-- <img :src="item.coverImg"
                 style="width:100%;"
                 alt=""
                 srcset=""> -->
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton" class="notText">
          <van-skeleton v-for="(item, index) in 3" :key="index" title :row="3"></van-skeleton>
        </div>
      </van-list>
    </van-pull-refresh>

  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'ZT',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      carouselList: [],
      dataList: [],
      switchs: {
        value: '',
        data: []
      }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getList()
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      console.log(newName, oldName)
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 列表请求
    const getList = async () => {
      var { data: list, total } = await $api.news.getSpecialsubjectList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        auditingFlag: 1,
        isAppShow: 1
      })
      list.forEach(item => {
        item.module = data.module
      })
      data.dataList = data.dataList.concat(list)
      console.log(data.dataList)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    const onClickLeft = () => history.back()
    const details = (row) => {
      console.log(row)
      router.push({ name: 'newsZTList', query: { id: row.id, title: row.title } })
    }
    return { ...toRefs(data), search, onClickLeft, onRefresh, onLoad, details }
  }
}
</script>

<style lang="less" scoped>
.ZT {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .item-box {
    width: calc(100% - 20px; );
    margin-left: 10px;
    margin-bottom: 20px;
  }

  .item-img {
    width: 100%;
    height: 170px;
    border-radius: 5px;
    // background: url() no-repeat;
    background-size: 100%;
    background-position: center;
  }

  .item-title {
    font-size: 16px;
    margin: 10px 0;
    text-align: justify;
  }
}
</style>
