<template>
  <div class="searchBook">
    <van-sticky>
      <form action="/">
        <van-search v-model="seachText"
                    ref="searchBox"
                    show-action
                    placeholder="请输入搜索关键词"
                    @update:model-value="onSearchChange"
                    @search="onSearch"
                    @cancel="onCancel" />
      </form>
    </van-sticky>
    <!-- <div style="height:70px;"></div> -->
    <template v-if="searchPage">
      <div v-if="history.length != 0"
           class="history_box">
        <div v-for="(item,index) in history"
             :key="item.id">
          <div @click="upSearch(item)"
               v-if="(seachText?item.indexOf(seachText) != -1:(allSearch?true:index<8))"
               class="history_item flex_box flex_align_center">
            <van-icon style="margin-left: 24px;"
                      :size="20"
                      :color="'#A5A5A5'"
                      name="clock-o"></van-icon>
            <div class="flex_placeholder text_one2"
                 :style="'font-size:14px;margin-left:10px;'">{{item}}</div>
            <div @click.stop="historyClean(item)"
                 style="padding: 10px;">
              <van-icon :size="20"
                        :color="'#A5A5A5'"
                        name="cross"></van-icon>
            </div>
          </div>
        </div>
        <template v-if="!seachText">
          <div v-if="!allSearch && history.length > 8"
               @click="allSearch = true"
               class="history_item flex_box flex_align_center flex_justify_content">
            <van-icon style="margin-right: px;"
                      :size="20"
                      :color="'#A5A5A5'"
                      name="arrow-down"></van-icon>
            <div class=""
                 :style="'font-size:12px;color:#A5A5A5'">全部搜素记录</div>
          </div>
          <div v-else
               @click="historyClean()"
               class="history_item flex_box flex_align_center flex_justify_content">
            <van-icon style="margin-right: 9px;"
                      :size="20"
                      :color="'#A5A5A5'"
                      name="delete-o"></van-icon>
            <div class=""
                 :style="'font-size:12px;color:#A5A5A5'">清空历史搜素</div>
          </div>
        </template>
      </div>
    </template>

    <template v-else>
      <van-pull-refresh v-model="refreshing"
                        @refresh="onRefresh">
        <van-list v-model:loading="loading"
                  :finished="finished"
                  finished-text="已展示全部搜索结果"
                  offset="52"
                  @load="onLoad"
                  :immediate-check="false">
          <!--数据列表-->
          <ul v-if="listData.length != 0"
              class="search_list_box">
            <li @click="openBookDetails(item)"
                v-for="(item) in listData"
                :key="item.id"
                class="search_item flex_box click">
              <div :style="'width:60px;height:81px;margin-right:10px;position: relative;'">
                <img v-if="item.txt.bookType == '2'"
                     class="item_Sound"
                     :style="'font-size:19px;'"
                     :src="icon_hasSound" />
                <img v-if="item.txt.isAvailable == '0'"
                     class="item_overdue"
                     src="../../../assets/img/overdue.png" />
                <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                     :src="item.img.url" />
              </div>
              <div class="flex_placeholder">
                <div class="text_one2"
                     :style="'margin-top:7px;'"
                     v-html="item.name"></div>
                <div class="text_one2"
                     :style="'font-size:14px;margin-top:8px;line-height: 1;color: #5E646D;'"
                     v-html="item.author"></div>
                <div class="text_one2"
                     :style="'font-size:12px;margin-top:6pxline-height: 1.2;color: #5E646D;'"
                     v-html="item.summary"></div>
              </div>
            </li>
          </ul>
          <!--加载中提示 首次为骨架屏-->
          <div v-if="showSkeleton">
            <van-skeleton round
                          v-for="(item) in 3"
                          :key="item"
                          title
                          :row="3"></van-skeleton>
          </div>
          <van-empty v-else-if="listData.length == 0"
                     :style="'font-size:14px;'"
                     :image="icon_no_data"
                     :description="'暂无数据'"></van-empty>
        </van-list>
      </van-pull-refresh>

    </template>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { Toast, Sticky } from 'vant'
export default {
  components: {
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    console.log($api)
    console.log(Toast)
    const data = reactive({
      appTheme: '#3A77FF',
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      // userId: JSON.parse(sessionStorage.getItem('user')).id,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      searchBox: null, // 搜索框对象
      listData: [
        // { url: '../../../images/img_test8.png', name: '论语', author: '作者：张素玲', summary: '探秘妇女解放思想的启蒙' },
        // { url: '../../../images/img_test9.png', name: '韩非子', author: '作者：陈培永', summary: '无论你是否承认,社会主义,这个词,它都在你心中,占有重要的一席之地。' },
        // { url: '../../../images/img_test10.png', name: '论美国的民主', author: '作者：吴官正', summary: '吴书记的人生哲理和为政之道' }
      ], // 列表数据

      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮

      searchPage: true, // 是否为搜索页 否则为
      history: [],
      allSearch: false, // 是否点击展开全部搜索
      popular: [{ name: '热门搜索', data: ['三字经', '社会主义的哲思', '习近平', '履职', '斗'] }],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png')

    })
    onMounted(() => {
      getHistory()
      data.searchBox.focus()
    })
    const getList = async () => {
      if (data.searchPage) {
        return
      }
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText
      }
      var { data: list } = await $api.bookAcademy.getBookList(datas)
      var dataListNew = []
      if (list) {
        data.showSkeleton = false
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.coverImgUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '' } }
          item.id = _eItem.id || ''// 书本id
          item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          dataListNew.push(item)
        })
        data.listData = data.listData.concat(dataListNew)
        console.log('data.listData===>', data.listData)
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (list.length < data.pageSize) {
          data.finished = true
        }
      }
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    // 获取 历史搜索
    const getHistory = () => {
      var historyData = sessionStorage.getItem('booKHistoryData' + data.userId) || ''
      data.history = []
      if (historyData) {
        data.history = historyData.split('|,|')
      }
    }
    // 设置历史 搜索
    const setHistory = () => {
      sessionStorage.setItem('booKHistoryData' + data.userId, data.history.join('|,|'))
    }
    // 清空某一个消息 或全部
    const historyClean = (_item) => {
      if (_item) {
        console.log(_item)
        data.history = data.history.filter(item => item !== _item)// 删除之前有的
      } else {
        data.history = []
      }
      setHistory()
    }
    // 是否显示 全部清除
    const showClean = () => {
      if (data.seachText) {
        var showClean = false
        data.history.forEach(function (_eItem, _eIndex, _eArr) {
          if (_eItem.indexOf(data.seachText) !== -1) {
            showClean = true
          }
        })
        return showClean
      } else {
        return true
      }
    }
    // 点击 某一个上去搜索
    const upSearch = (_item) => {
      data.seachText = _item
      data.searchPage = false
      onRefresh()
    }
    const onSearchChange = (val) => {
      data.seachText = val
      data.searchPage = true
      // getHistory()
    }
    const onSearch = (val) => {
      if (!data.seachText) {
        Toast('请输入搜索内容')
      } else {
        data.pageNo = 1
        data.listData = []
        data.seachText = val
        data.searchPage = false
        console.log('seachText==>', data.seachText)
        data.history = data.history.filter(item => item !== data.seachText)// 删除之前有的
        data.history.unshift(data.seachText)
        setHistory()
        getList()
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }

    const onCancel = () => { router.go(-1) }
    return {
      ...toRefs(data),
      onRefresh,
      onLoad,
      onSearch,
      onSearchChange,
      onCancel,
      upSearch,
      showClean,
      historyClean,
      openBookDetails
    }
  }
}
</script>
<style lang="less" scoped>
@import "./searchBook.less";
</style>
