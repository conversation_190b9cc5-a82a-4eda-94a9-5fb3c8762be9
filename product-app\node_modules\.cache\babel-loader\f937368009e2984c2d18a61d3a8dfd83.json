{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\api\\http.js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\api\\http.js", "mtime": 1755079703758}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"version": 3, "names": ["axios", "Qs", "router", "CryptoJs", "loginUc", "baseURL", "process", "env", "STAGE", "timeout", "defaults", "headers", "post", "result", "window", "location", "origin", "isOutSideNet", "interceptors", "request", "use", "config", "url", "indexOf", "sessionStorage", "getItem", "token", "Authorization", "certId", "areaId", "JSON", "parse", "method", "Object", "prototype", "toString", "call", "data", "stringify", "appSecret", "clientId", "timestamp", "Date", "signature", "getSignature", "error", "Promise", "reject", "HTTPMethod", "toUpperCase", "URI", "param", "split", "e", "console", "message", "Message", "getPara<PERSON>", "hash", "HmacSHA256", "_httpMethod", "_param", "j<PERSON><PERSON><PERSON>", "signStr", "i", "s", "key", "value", "undefined", "paramIntValue", "k", "length", "charCodeAt", "response", "<PERSON><PERSON><PERSON>", "resolve", "_Toast", "fail", "errmsg", "push", "name", "includes", "HTTP", "header", "_request", "filter", "then", "res", "catch", "err", "file", "type", "_file", "params", "responseType"], "sources": ["D:/zy/xm/h5/qdzx_h5/product-app/src/api/http.js"], "sourcesContent": ["import axios from 'axios'\r\nimport Qs from 'qs'\r\nimport router from '../router'\r\nimport CryptoJs from 'crypto-js/crypto-js'\r\nimport {\r\n  Toast\r\n} from 'vant'\r\n\r\nvar loginUc = 'http://123.206.209.136:20198/server'\r\nvar baseURL = 'http://123.206.209.136:20199/lzt' // 南京政协书院平台版\r\nif (process.env.STAGE == 'test') { // eslint-disable-line  \r\n  loginUc = 'http://212.64.89.218:20131/server'\r\n  baseURL = 'http://212.64.89.218:20102/lzt'\r\n} else if (process.env.STAGE == 'qdzx_h5_test') { // eslint-disable-line\r\n  loginUc = 'http://test.dc.cszysoft.com:21429/server'\r\n  baseURL = 'http://test.dc.cszysoft.com:21408/lzt'\r\n} else if (process.env.STAGE == 'qdzx_h5_prod') { // eslint-disable-line\r\n  // loginUc = 'http://test.dc.cszysoft.com:21429/server'\r\n  // baseURL = 'http://test.dc.cszysoft.com:21408/lzt'\r\n  loginUc = 'http://**************/server'\r\n  baseURL = 'http://**************/lzt'\r\n  // loginUc = 'http://**************:81/server'\r\n  // baseURL = 'http://**************:81/lzt'\r\n  // loginUc = 'http://*************:80/server'\r\n  // baseURL = 'http://*************:80/lzt'\r\n  // loginUc = `${window.location.protocol}//${window.location.hostname}/server`\r\n  // baseURL = `${window.location.protocol}//${window.location.hostname}/lzt`\r\n}\r\nvar timeout = 160000\r\naxios.defaults.baseURL = baseURL\r\n\r\n// 请求超时时间\r\naxios.defaults.timeout = timeout\r\n// 设置post请求头\r\naxios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'\r\nconst result = window.location.origin === 'http://qdzhzx.qingdao.gov.cn' ? true : window.location.origin === 'http://**************' ? true : window.location.origin === 'http://*************' ? 'sdt' : 'test'\r\naxios.defaults.headers.isOutSideNet = result\r\n// 请求拦截器\r\naxios.interceptors.request.use(\r\n  config => {\r\n    if (config.url.indexOf('push/rongCloud') >= 0) {\r\n      config.baseURL = sessionStorage.getItem('tomcatAddress')\r\n      // } else if (config.url.indexOf('www.yozodcs.com') >= 0) {\r\n    } else if (config.url.indexOf('rest/oagxh/personal_getdetail_v7') >= 0) {\r\n      const token = sessionStorage.getItem('otherToken') || ''\r\n      config.headers.Authorization = token\r\n      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'\r\n    } else {\r\n      config.headers.certId = sessionStorage.getItem('useridN')\r\n      // 自定义请求头参数\r\n      const token = sessionStorage.getItem('token') || ''\r\n      const areaId = sessionStorage.getItem('areaId') || '370200'\r\n      config.headers.Authorization = token ? JSON.parse(token) : 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n      if (config.url.indexOf('/server/calogin') >= 0) {\r\n        config.headers.Authorization = 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n      }\r\n      config.headers['u-login-areaId'] = areaId\r\n      if (config.method === 'post') {\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n            config.data = Qs.stringify(config.data)\r\n          }\r\n        }\r\n      }\r\n      var appSecret = '3d5ef53424f24852a8fc1d15a1072b42'\r\n      var clientId = '816cc044c46247d987bbf611ced1cf5e'\r\n      var timestamp = Date.parse(new Date()) / 1000 // 时间戳，精确到秒\r\n      config.headers.timestamp = timestamp\r\n      config.headers.clientId = clientId\r\n      config.headers.signature = getSignature(config.url, config.method, timestamp, clientId, appSecret)\r\n      config.headers['u-app-user'] = true\r\n    }\r\n    return config\r\n  }, error => {\r\n    // 对请求错误做些什么\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nfunction getSignature (url, method, timestamp, clientId, appSecret) {\r\n  var HTTPMethod = method ? method.toUpperCase() : 'GET'\r\n  var URI = ''\r\n  var param = ''\r\n  url = baseURL + url\r\n  try {\r\n    if (url.indexOf('?') >= 0) {\r\n      URI = url.split('?')[0].split(url.split('/')[3])[1]\r\n      param = HTTPMethod === 'POST' ? '' : (url.split('?')[1])\r\n    } else {\r\n      URI = url.split(url.split('/')[3])[1]\r\n    }\r\n  } catch (e) {\r\n    console.error('语句异常：' + e.message)\r\n  }\r\n  var Message = timestamp + '-' + HTTPMethod + '-' + URI + '-' + getParam(HTTPMethod, param)\r\n  var hash = '' + CryptoJs.HmacSHA256(Message, appSecret)\r\n  return hash\r\n}\r\n/**\r\n * _httpMethod:请求方法（必须大写）\r\n * _param参数字符串\r\n * 将所有GET参数值的 char 值加和生成一个整数 POST方法返回0\r\n */\r\nfunction getParam (_httpMethod, _param) {\r\n  var jsonarr = _param.split('&')\r\n  var signStr = ''\r\n  for (var i in jsonarr) {\r\n    var s = '' + jsonarr[i]\r\n    var key = s.split('=')[0]\r\n    var value = s.split('=')[1]\r\n    // eslint-disable-next-line eqeqeq\r\n    if (key == 'timestamp' || key == 'clientId' || key == 'signatrue') {\r\n      continue\r\n    }\r\n    // eslint-disable-next-line eqeqeq\r\n    if (!value || value == '' || value == undefined || value == null) {\r\n      continue\r\n    }\r\n    signStr += key\r\n  }\r\n  // eslint-disable-next-line eqeqeq\r\n  if (signStr == '') {\r\n    return 0\r\n  }\r\n  var paramIntValue = 0\r\n  for (var k = 0; k < signStr.length; k++) {\r\n    paramIntValue += signStr.charCodeAt(k)\r\n  }\r\n  // eslint-disable-next-line eqeqeq\r\n  if (_httpMethod == 'POST') {\r\n    paramIntValue = 0\r\n  }\r\n  return paramIntValue\r\n}\r\n\r\n// 响应拦截器\r\naxios.interceptors.response.use(\r\n  response => {\r\n    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据\r\n    if (response.data.errcode === 200) {\r\n      return Promise.resolve(response)\r\n    } else if (response.data.errcode === 302) {\r\n      Toast.fail(response.data.errmsg || response.data.message)\r\n      // sessionStorage.clear()\r\n      router.push({\r\n        name: 'login'\r\n      })\r\n      return Promise.reject(response)\r\n    } else if (response.data.errcode === undefined) { // undefind 为文件下载接口\r\n      return Promise.resolve(response)\r\n    } else {\r\n      if (response.data.errmsg) {\r\n        Toast.fail(response.data.errmsg)\r\n      }\r\n      return Promise.reject(response)\r\n    }\r\n  }, error => {\r\n    if (error.message.includes('timeout')) {\r\n      Toast.fail('请求超时，请稍后重试！')\r\n      return Promise.reject(error)\r\n    }\r\n    if (error && error.response.data.errmsg) {\r\n      Toast.fail(error.response.data.errmsg)\r\n      return Promise.reject(error)\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\nclass HTTP {\r\n  request ({\r\n    url,\r\n    data = {},\r\n    method = 'post',\r\n    header = {}\r\n  }) {\r\n    return new Promise((resolve, reject) => {\r\n      this._request(url, this.filter(data), method, header, resolve, reject)\r\n    })\r\n  }\r\n\r\n  _request (url, data = {}, method, header, resolve, reject) {\r\n    axios({\r\n      url: url,\r\n      data: data,\r\n      method: method,\r\n      headers: header\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  }\r\n\r\n  filter (param) { // 参数过滤\r\n    var data = param\r\n    for (var key in data) {\r\n      if (data[key] === null) {\r\n        delete data[key]\r\n      }\r\n    }\r\n    return data\r\n  }\r\n\r\n  file ({\r\n    url,\r\n    data = {},\r\n    type = 'blob',\r\n    method = 'post',\r\n    header = {}\r\n  }) {\r\n    return new Promise((resolve, reject) => {\r\n      this._file(url, this.filter(data), method, header, type, resolve, reject)\r\n    })\r\n  }\r\n\r\n  _file (url, params, method, header, type, resolve, reject) {\r\n    axios({\r\n      url: url,\r\n      data: params,\r\n      method: method,\r\n      header: header,\r\n      responseType: type\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  }\r\n}\r\n\r\nexport {\r\n  HTTP,\r\n  loginUc\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,EAAE,MAAM,IAAI;AACnB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,QAAQ,MAAM,qBAAqB;AAK1C,IAAIC,OAAO,GAAG,qCAAqC;AACnD,IAAIC,OAAO,GAAG,kCAAkC,EAAC;AACjD,IAAIC,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,MAAM,EAAE;EAAE;EACjCJ,OAAO,GAAG,mCAAmC;EAC7CC,OAAO,GAAG,gCAAgC;AAC5C,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,cAAc,EAAE;EAAE;EAChDJ,OAAO,GAAG,0CAA0C;EACpDC,OAAO,GAAG,uCAAuC;AACnD,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,cAAc,EAAE;EAAE;EAChD;EACA;EACAJ,OAAO,GAAG,8BAA8B;EACxCC,OAAO,GAAG,2BAA2B;EACrC;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAII,OAAO,GAAG,MAAM;AACpBT,KAAK,CAACU,QAAQ,CAACL,OAAO,GAAGA,OAAO;;AAEhC;AACAL,KAAK,CAACU,QAAQ,CAACD,OAAO,GAAGA,OAAO;AAChC;AACAT,KAAK,CAACU,QAAQ,CAACC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC,GAAG,iDAAiD;AAC/F,MAAMC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,KAAK,8BAA8B,GAAG,IAAI,GAAGF,MAAM,CAACC,QAAQ,CAACC,MAAM,KAAK,uBAAuB,GAAG,IAAI,GAAGF,MAAM,CAACC,QAAQ,CAACC,MAAM,KAAK,sBAAsB,GAAG,KAAK,GAAG,MAAM;AAChNhB,KAAK,CAACU,QAAQ,CAACC,OAAO,CAACM,YAAY,GAAGJ,MAAM;AAC5C;AACAb,KAAK,CAACkB,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;EACR,IAAIA,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;IAC7CF,MAAM,CAAChB,OAAO,GAAGmB,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD;EACF,CAAC,MAAM,IAAIJ,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;IACtE,MAAMG,KAAK,GAAGF,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;IACxDJ,MAAM,CAACV,OAAO,CAACgB,aAAa,GAAGD,KAAK;IACpCL,MAAM,CAACV,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC;EACtE,CAAC,MAAM;IACLU,MAAM,CAACV,OAAO,CAACiB,MAAM,GAAGJ,cAAc,CAACC,OAAO,CAAC,SAAS,CAAC;IACzD;IACA,MAAMC,KAAK,GAAGF,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;IACnD,MAAMI,MAAM,GAAGL,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ;IAC3DJ,MAAM,CAACV,OAAO,CAACgB,aAAa,GAAGD,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC,GAAG,gCAAgC;IAC3F,IAAIL,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;MAC9CF,MAAM,CAACV,OAAO,CAACgB,aAAa,GAAG,gCAAgC;IACjE;IACAN,MAAM,CAACV,OAAO,CAAC,gBAAgB,CAAC,GAAGkB,MAAM;IACzC,IAAIR,MAAM,CAACW,MAAM,KAAK,MAAM,EAAE;MAC5B,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC,IAAI,mBAAmB,EAAE;QAAE;QACxE,IAAIhB,MAAM,CAACV,OAAO,CAAC,cAAc,CAAC,KAAK,gCAAgC,EAAE;UACvEU,MAAM,CAACgB,IAAI,GAAGpC,EAAE,CAACqC,SAAS,CAACjB,MAAM,CAACgB,IAAI,CAAC;QACzC;MACF;IACF;IACA,IAAIE,SAAS,GAAG,kCAAkC;IAClD,IAAIC,QAAQ,GAAG,kCAAkC;IACjD,IAAIC,SAAS,GAAGC,IAAI,CAACX,KAAK,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC;IAC9CrB,MAAM,CAACV,OAAO,CAAC8B,SAAS,GAAGA,SAAS;IACpCpB,MAAM,CAACV,OAAO,CAAC6B,QAAQ,GAAGA,QAAQ;IAClCnB,MAAM,CAACV,OAAO,CAACgC,SAAS,GAAGC,YAAY,CAACvB,MAAM,CAACC,GAAG,EAAED,MAAM,CAACW,MAAM,EAAES,SAAS,EAAED,QAAQ,EAAED,SAAS,CAAC;IAClGlB,MAAM,CAACV,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;EACrC;EACA,OAAOU,MAAM;AACf,CAAC,EAAEwB,KAAK,IAAI;EACV;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,SAASD,YAAYA,CAAEtB,GAAG,EAAEU,MAAM,EAAES,SAAS,EAAED,QAAQ,EAAED,SAAS,EAAE;EAClE,IAAIS,UAAU,GAAGhB,MAAM,GAAGA,MAAM,CAACiB,WAAW,CAAC,CAAC,GAAG,KAAK;EACtD,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,KAAK,GAAG,EAAE;EACd7B,GAAG,GAAGjB,OAAO,GAAGiB,GAAG;EACnB,IAAI;IACF,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACzB2B,GAAG,GAAG5B,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC9B,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDD,KAAK,GAAGH,UAAU,KAAK,MAAM,GAAG,EAAE,GAAI1B,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;IAC1D,CAAC,MAAM;MACLF,GAAG,GAAG5B,GAAG,CAAC8B,KAAK,CAAC9B,GAAG,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVC,OAAO,CAACT,KAAK,CAAC,OAAO,GAAGQ,CAAC,CAACE,OAAO,CAAC;EACpC;EACA,IAAIC,OAAO,GAAGf,SAAS,GAAG,GAAG,GAAGO,UAAU,GAAG,GAAG,GAAGE,GAAG,GAAG,GAAG,GAAGO,QAAQ,CAACT,UAAU,EAAEG,KAAK,CAAC;EAC1F,IAAIO,IAAI,GAAG,EAAE,GAAGvD,QAAQ,CAACwD,UAAU,CAACH,OAAO,EAAEjB,SAAS,CAAC;EACvD,OAAOmB,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,QAAQA,CAAEG,WAAW,EAAEC,MAAM,EAAE;EACtC,IAAIC,OAAO,GAAGD,MAAM,CAACT,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIW,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,IAAIF,OAAO,EAAE;IACrB,IAAIG,CAAC,GAAG,EAAE,GAAGH,OAAO,CAACE,CAAC,CAAC;IACvB,IAAIE,GAAG,GAAGD,CAAC,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,IAAIe,KAAK,GAAGF,CAAC,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,IAAIc,GAAG,IAAI,WAAW,IAAIA,GAAG,IAAI,UAAU,IAAIA,GAAG,IAAI,WAAW,EAAE;MACjE;IACF;IACA;IACA,IAAI,CAACC,KAAK,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,IAAIC,SAAS,IAAID,KAAK,IAAI,IAAI,EAAE;MAChE;IACF;IACAJ,OAAO,IAAIG,GAAG;EAChB;EACA;EACA,IAAIH,OAAO,IAAI,EAAE,EAAE;IACjB,OAAO,CAAC;EACV;EACA,IAAIM,aAAa,GAAG,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCD,aAAa,IAAIN,OAAO,CAACS,UAAU,CAACF,CAAC,CAAC;EACxC;EACA;EACA,IAAIV,WAAW,IAAI,MAAM,EAAE;IACzBS,aAAa,GAAG,CAAC;EACnB;EACA,OAAOA,aAAa;AACtB;;AAEA;AACArE,KAAK,CAACkB,YAAY,CAACuD,QAAQ,CAACrD,GAAG,CAC7BqD,QAAQ,IAAI;EACV;EACA,IAAIA,QAAQ,CAACpC,IAAI,CAACqC,OAAO,KAAK,GAAG,EAAE;IACjC,OAAO5B,OAAO,CAAC6B,OAAO,CAACF,QAAQ,CAAC;EAClC,CAAC,MAAM,IAAIA,QAAQ,CAACpC,IAAI,CAACqC,OAAO,KAAK,GAAG,EAAE;IACxCE,MAAA,CAAMC,IAAI,CAACJ,QAAQ,CAACpC,IAAI,CAACyC,MAAM,IAAIL,QAAQ,CAACpC,IAAI,CAACkB,OAAO,CAAC;IACzD;IACArD,MAAM,CAAC6E,IAAI,CAAC;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOlC,OAAO,CAACC,MAAM,CAAC0B,QAAQ,CAAC;EACjC,CAAC,MAAM,IAAIA,QAAQ,CAACpC,IAAI,CAACqC,OAAO,KAAKN,SAAS,EAAE;IAAE;IAChD,OAAOtB,OAAO,CAAC6B,OAAO,CAACF,QAAQ,CAAC;EAClC,CAAC,MAAM;IACL,IAAIA,QAAQ,CAACpC,IAAI,CAACyC,MAAM,EAAE;MACxBF,MAAA,CAAMC,IAAI,CAACJ,QAAQ,CAACpC,IAAI,CAACyC,MAAM,CAAC;IAClC;IACA,OAAOhC,OAAO,CAACC,MAAM,CAAC0B,QAAQ,CAAC;EACjC;AACF,CAAC,EAAE5B,KAAK,IAAI;EACV,IAAIA,KAAK,CAACU,OAAO,CAAC0B,QAAQ,CAAC,SAAS,CAAC,EAAE;IACrCL,MAAA,CAAMC,IAAI,CAAC,aAAa,CAAC;IACzB,OAAO/B,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B;EACA,IAAIA,KAAK,IAAIA,KAAK,CAAC4B,QAAQ,CAACpC,IAAI,CAACyC,MAAM,EAAE;IACvCF,MAAA,CAAMC,IAAI,CAAChC,KAAK,CAAC4B,QAAQ,CAACpC,IAAI,CAACyC,MAAM,CAAC;IACtC,OAAOhC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AACD,MAAMqC,IAAI,CAAC;EACT/D,OAAOA,CAAE;IACPG,GAAG;IACHe,IAAI,GAAG,CAAC,CAAC;IACTL,MAAM,GAAG,MAAM;IACfmD,MAAM,GAAG,CAAC;EACZ,CAAC,EAAE;IACD,OAAO,IAAIrC,OAAO,CAAC,CAAC6B,OAAO,EAAE5B,MAAM,KAAK;MACtC,IAAI,CAACqC,QAAQ,CAAC9D,GAAG,EAAE,IAAI,CAAC+D,MAAM,CAAChD,IAAI,CAAC,EAAEL,MAAM,EAAEmD,MAAM,EAAER,OAAO,EAAE5B,MAAM,CAAC;IACxE,CAAC,CAAC;EACJ;EAEAqC,QAAQA,CAAE9D,GAAG,EAAEe,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,EAAEmD,MAAM,EAAER,OAAO,EAAE5B,MAAM,EAAE;IACzD/C,KAAK,CAAC;MACJsB,GAAG,EAAEA,GAAG;MACRe,IAAI,EAAEA,IAAI;MACVL,MAAM,EAAEA,MAAM;MACdrB,OAAO,EAAEwE;IACX,CAAC,CAAC,CAACG,IAAI,CAACC,GAAG,IAAI;MACbZ,OAAO,CAACY,GAAG,CAAClD,IAAI,CAAC;IACnB,CAAC,CAAC,CAACmD,KAAK,CAACC,GAAG,IAAI;MACd1C,MAAM,CAAC0C,GAAG,CAAC;IACb,CAAC,CAAC;EACJ;EAEAJ,MAAMA,CAAElC,KAAK,EAAE;IAAE;IACf,IAAId,IAAI,GAAGc,KAAK;IAChB,KAAK,IAAIe,GAAG,IAAI7B,IAAI,EAAE;MACpB,IAAIA,IAAI,CAAC6B,GAAG,CAAC,KAAK,IAAI,EAAE;QACtB,OAAO7B,IAAI,CAAC6B,GAAG,CAAC;MAClB;IACF;IACA,OAAO7B,IAAI;EACb;EAEAqD,IAAIA,CAAE;IACJpE,GAAG;IACHe,IAAI,GAAG,CAAC,CAAC;IACTsD,IAAI,GAAG,MAAM;IACb3D,MAAM,GAAG,MAAM;IACfmD,MAAM,GAAG,CAAC;EACZ,CAAC,EAAE;IACD,OAAO,IAAIrC,OAAO,CAAC,CAAC6B,OAAO,EAAE5B,MAAM,KAAK;MACtC,IAAI,CAAC6C,KAAK,CAACtE,GAAG,EAAE,IAAI,CAAC+D,MAAM,CAAChD,IAAI,CAAC,EAAEL,MAAM,EAAEmD,MAAM,EAAEQ,IAAI,EAAEhB,OAAO,EAAE5B,MAAM,CAAC;IAC3E,CAAC,CAAC;EACJ;EAEA6C,KAAKA,CAAEtE,GAAG,EAAEuE,MAAM,EAAE7D,MAAM,EAAEmD,MAAM,EAAEQ,IAAI,EAAEhB,OAAO,EAAE5B,MAAM,EAAE;IACzD/C,KAAK,CAAC;MACJsB,GAAG,EAAEA,GAAG;MACRe,IAAI,EAAEwD,MAAM;MACZ7D,MAAM,EAAEA,MAAM;MACdmD,MAAM,EAAEA,MAAM;MACdW,YAAY,EAAEH;IAChB,CAAC,CAAC,CAACL,IAAI,CAACC,GAAG,IAAI;MACbZ,OAAO,CAACY,GAAG,CAAClD,IAAI,CAAC;IACnB,CAAC,CAAC,CAACmD,KAAK,CAACC,GAAG,IAAI;MACd1C,MAAM,CAAC0C,GAAG,CAAC;IACb,CAAC,CAAC;EACJ;AACF;AAEA,SACEP,IAAI,EACJ9E,OAAO", "ignoreList": []}]}