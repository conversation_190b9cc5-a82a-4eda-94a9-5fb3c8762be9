// 通知公告列表
const noticeList = () => import('@/views/notice/noticeList')
// 通知公告详情
const noticeDetails = () => import('@/views/notice/noticeDetails')
// 已读人员
const readUserList = () => import('@/views/notice/readUserList')

const notice = [
  {
    path: '/noticeList',
    name: 'noticeList',
    component: noticeList,
    meta: {
      title: '通知公告',
      keepAlive: true
    }
  },
  {
    path: '/noticeDetails',
    name: 'noticeDetails',
    component: noticeDetails,
    meta: {
      title: '通知公告详情',
      keepAlive: false
    }
  },
  {
    path: '/readUserList',
    name: 'readUserList',
    component: readUserList,
    meta: {
      title: '已读人员',
      keepAlive: false
    }
  }
]
export default notice
