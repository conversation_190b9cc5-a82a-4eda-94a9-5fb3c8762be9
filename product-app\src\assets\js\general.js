/* eslint-disable no-tabs */
import waterMark from './waterMark'
// import {
//   inject
// } from 'vue'
export default {
  data: {
    appFont: sessionStorage.getItem('appFont') || 'simplified',
    appFontSize: Number((sessionStorage.getItem('appFontSize') || '16')),
    // appTheme: sessionStorage.getItem('appTheme') || (inject('$ifzx') ?  : '#C61414'),
    appTheme: sessionStorage.getItem('appTheme') || '#3088FE',
    headTheme: sessionStorage.getItem('headTheme') || '#fff'
  },
  // 全局配置
  // 是否全局设置水印
  setWaterMark: function () {
    var setWaterMark = sessionStorage.getItem('setWaterMark') || '0'
    // eslint-disable-next-line eqeqeq
    if (JSON.parse(sessionStorage.getItem('user')) && setWaterMark == 1) {
      waterMark.set(JSON.parse(sessionStorage.getItem('user')).userName + JSON.parse(sessionStorage.getItem('user')).mobile.substr(JSON.parse(sessionStorage.getItem('user')).mobile.length - 4))
    }
  },
  // 是否全局置灰
  appGrayscale: function () {
    var appGrayscale = sessionStorage.getItem('appGrayscale') || '0'
    // eslint-disable-next-line eqeqeq
    if (appGrayscale == 1) {
      this.css(this.dom('html'), '-webkit-filter: grayscale(100%);-moz-filter: grayscale(100%);-ms-filter: grayscale(100%);-o-filter: grayscale(100%);filter: grayscale(100%);filter: gray;')
    } else {
      this.css(this.dom('html'), 'filter: none;')
    }
  },
  delHtmlTag: function (str) {
    return str.replace(/<[^>]+>/g, '')
  },
  changeConfiguration: function () {
    this.data.appFont = sessionStorage.getItem('appFont') || 'simplified'
    this.data.appFontSize = Number((sessionStorage.getItem('appFontSize') || '16'))
    this.data.appTheme = sessionStorage.getItem('appTheme') || '#3A77FF'
  },

  // 样式加载配置  字体不能改  大小传过来 +几    主题色不能改
  loadConfiguration: function (_changeSize, _max) {
    var changeSize = _changeSize || 0
    return 'font-size:' + ((this.data.appFontSize > _max ? _max : this.data.appFontSize + changeSize)) + 'px;font-family:' + this.data.appFont + ';'
  },
  // 根据配置大小  设置按钮 图片等宽高 +-N
  loadConfigurationSize: function (_changeSize, _who) {
    var changeSize = _changeSize || 0
    var returnCss = ''
    var cssWidth, cssHeight
    if (this.isArray(_changeSize)) {
      cssWidth = 'width:' + ((this.data.appFontSize + (_changeSize[0] || 0))) + 'px;'
      cssHeight = 'height:' + ((this.data.appFontSize + (_changeSize[1] || 0))) + 'px;'
    } else {
      cssWidth = 'width:' + ((this.data.appFontSize + changeSize)) + 'px;'
      cssHeight = 'height:' + ((this.data.appFontSize + changeSize)) + 'px;'
    }
    if (!_who) {
      returnCss = cssWidth + cssHeight
    } else {
      returnCss = _who === 'w' ? cssWidth : cssHeight
    }
    return returnCss
  },
  // ================================================================================
  // 获取item  只有一层级的时候 会返回 当前index  _i
  getItemForKey: function (_value, _list, _key) {
    for (var i = 0; i < _list.length; i++) {
      if (this.isArray(_list[i])) {
        var result = this.getItemForKey(_value, _list[i], _key)
        if (result) {
          return result
        }
        // for (var j = 0; j < _list[i].length; j++) {
        //   if (_list[i][j][_key || 'key'] === _value) {
        //     return _list[i][j]
        //   }
        // }
      } else {
        if (this.isString(_list[i]) ? _list[i] : _list[i][_key || 'key'] === _value) {
          if (!this.isString(_list[i])) {
            _list[i]._i = i
          }
          return _list[i]
        }
      }
    }
    return false
  },
  // 在集合中删除第一个参数obj和index都可以或对比字符串  第二个传入集合  第三个为对比key
  delItemForKey: function (_obj, _list, _key) {
    if (!(_obj + '')) {
      return
    }
    if (this.isTargetType(_obj, 'number')) {
      _list.splice(_obj, 1) // 删除起始角标   /  几个数量
    } else {
      var contrastObj = this.isString(_obj) ? _obj : _obj[_key || 'url']
      for (var i = 0; i < _list.length; i++) {
        if (_list[i][_key || 'url'] === contrastObj) {
          _list.splice(i, 1)
        }
      }
    }
  },
  /**
     * 取系统当前时间
  */
  getTime: function (_data) {
    var that = this
    var nowTime = new Date()
    if (_data) {
      if (that.isString(_data)) {
        nowTime = new Date(_data)
      } else {
        nowTime = _data
      }
    }
    var year = nowTime.getFullYear()
    var month = nowTime.getMonth() + 1// 月份从0开始的
    var day = nowTime.getDate()
    var hours = nowTime.getHours()
    var minutes = nowTime.getMinutes()
    var seconds = nowTime.getSeconds()
    month = (month < 10) ? '0' + month : month
    day = (day < 10) ? '0' + day : day
    hours = (hours < 10) ? '0' + hours : hours
    minutes = (minutes < 10) ? '0' + minutes : minutes
    seconds = (seconds < 10) ? '0' + seconds : seconds
    return year + '/' + month + '/' + day + ' ' + hours + ':' + minutes + ':' + seconds
  },
  // 文字共用方法集合 ================================================================================
  // 处理正文中的原生格式问题 转换成html格式
  dealWithCon: function (_content) {
    window.dealWithCon = true
    // 抓到的数据 可能没得标签
    var expText = _content || ''
    // 转换格式之后还正文还没得 标签 先加一个
    var expLength = expText.match(/<.*?>/g) || []
    if (expLength.length < 2) {
      expText = '<p>' + expText + '</p>'
    }
    //		//再匹配标签中的正文
    // eslint-disable-next-line no-useless-escape
    var strRegex = '[^\>?]+[\<]'
    var regex = new RegExp(strRegex, 'gi') // 匹配所有非标签中的字符	g不停止	 i忽略大小写
    if (regex.test(expText)) { // 如果有标签内容	(肯定有)
      expText = expText.replace(regex, function (m) {
        return m.replace(/&amp;/g, '&').replace(/ /g, '&nbsp;').replace(/\n/g, '<br />') // 将标签中的	\n换成br 空格 抱成 nbsp
      })
    }
    // 默认去除所有标签
    // 文件资料里见鬼	有style 先删掉
    expText = expText.replace(/<style(.*?)<\/style>/gi, '')
    expText = expText.replace(/<link(.*?)<\/link>/gi, '') // 引入link删除
    expText = expText.replace(/<script(.*?)<\/script>/gi, '') // 引入script删除
    // eslint-disable-next-line no-useless-escape
    expText = expText.replace(/style\s*=(['\"\s]?)[^'\"]*?\1/gi, '').replace(/&nbsp;/g, '') // 删除所有行内样式	还有一些多余的空格
    // eslint-disable-next-line no-irregular-whitespace
    expText = expText.replace(/　/g, '') // 删除所有行内样式	还有一些多余的空格
    expText = expText.replace(/<(?!\/?(p|video|br|a|img)\b)[^<>]*>/gi, '')
    console.log('expText==================', expText)
    // expText = expText.replace(/<(p).*?>/gi, '<p style="text-indent:2em;line-height:1.8;color:#444;font-size:' + ((this.data.appFontSize + 2)) + 'px;">') // 修改p默认样式 padding-bottom:15px;
    expText = expText.replace(/<(p).*?>(.*?)<\/p>/gi, function (match, pTag, content) {
      if (/<img/i.test(content)) {
        return '<p style="line-height:1.8;color:#444;font-size:16px;">' + content + '</p>' // 返回原始的匹配内容
      } else if (/<video/i.test(content)) {
        var updatedContent = content.replace(/<video([^>]*)>/gi, '<video$1 style="width: 100%; height: 100%;">')
        return '<p style="line-height:1.8;color:#444;font-size:16px;">' + updatedContent + '</p>' // 返回原始的匹配内容
      } else {
        return '<p style="text-indent:2em;line-height:1.8;color:#444;font-size:16px;">' + content + '</p>'
      }
    })
    return expText
  },
  // 处理原生格式为html格式	一般用于提交时
  conversionRichText: function (value) {
    if (!value || !this.isString(value)) return value
    var textList = value.split('\n')
    var str = ''
    for (var i = 0; i < textList.length; i++) {
      var addText = textList[i].replace(/&amp;/g, '&').replace(/ /g, '&nbsp;')
      if (addText) {
        str = str + '<p>' + addText + '</p>'
      }
    }
    return str
  },
  // 输入框中回显 html编辑 去除富文本标签格式，返回文本	参数一：文本，参数二：是否保留空格，参数三：是否保留换行， 参数四：是否段落缩进（当参数三为真时有效），参数五：去除标签是否换行
  clearRichText: function (value, space, wrap, indent, tab) {
    if (!this.isString(value) || !value) return value
    value = value.replace(/&amp;/g, '&') // 坑爹的后台管理了 & 符号
    // 空格处理
    if (space) {
      value = value.replace(/(&nbsp;)/g, ' ')
    } else {
      value = value.replace(/(&nbsp;)/g, '')
      value = value.replace(/ /g, '')
    }
    // 换行处理
    if (wrap) {
      if (indent) {
        value = value.replace(/<br\/?[^>]*>/g, '\n			')
      } else {
        value = value.replace(/<br\/?[^>]*>/g, '\n')
      }
    } else {
      value = value.replace(/(\n)/g, '') // 换行
      value = value.replace(/(\t)/g, '') // tab水平制表
      value = value.replace(/(\r)/g, '') // 回车
    }
    if (!space && !wrap) value = value.replace(/\s*/g, '') // 空白和换行
    if (tab) value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, '\n			')
    value = value.replace(/<\/?[^>]*>/g, '')
    // value = value.replace(/(&lt;)\/?[^(&gt;)]*(&gt)/g, "")
    if (wrap && indent) value = '			' + value
    return value
  },
  // 获取 文件类型的各种属性
  getFileTypeAttr: function (_type) {
    var type = _type || ''
    type = type.toLocaleLowerCase()
    var iconInfo = {
      name: 'icon_unknown.png',
      type: 'unknown'
    }
    console.log('type====>>', type)
    try {
      if (type.indexOf('.') !== -1) type = type.split('.')[type.split('.').length - 1]
      switch (type) {
        case 'xlsx':
        case 'xlsm':
        case 'xlsb':
        case 'xltx':
        case 'xltm':
        case 'xls':
        case 'xlt':
        case 'et':
        case 'csv':
        case 'uos': // excel格式
          iconInfo.name = 'icon_excel.png'
          iconInfo.type = 'excel'
          iconInfo.convertType = '61'
          break
        case 'doc':
        case 'docx':
        case 'docm':
        case 'dotx':
        case 'dotm':
        case 'dot':
        case 'xps':
        case 'rtf':
        case 'wps':
        case 'wpt':
        case 'uot': // word格式
          iconInfo.name = 'icon_word.png'
          iconInfo.type = 'word'
          iconInfo.convertType = '61'
          break
        case 'pdf': // pdf格式
          iconInfo.name = 'icon_pdf.png'
          iconInfo.type = 'pdf'
          iconInfo.convertType = '20'
          break
        case 'ppt':
        case 'pptx':
        case 'pps':
        case 'pot':
        case 'pptm':
        case 'potx':
        case 'potm':
        case 'ppsx':
        case 'ppsm':
        case 'ppa':
        case 'ppam':
        case 'dps':
        case 'dpt':
        case 'uop': // ppt
          iconInfo.name = 'icon_ppt.png'
          iconInfo.type = 'ppt'
          iconInfo.convertType = '61'
          break
        case 'bmp':
        case 'gif':
        case 'jpg':
        case 'pic':
        case 'png':
        case 'tif':
        case 'jpeg':
        case 'jpe':
        case 'icon':
        case 'jfif':
        case 'dib': // 图片格式
          iconInfo.name = 'icon_pic.png'
          iconInfo.type = 'image'
          iconInfo.convertType = '23'
          break
        case 'txt': // 文本
          iconInfo.name = 'icon_txt.png'
          iconInfo.type = 'txt'
          iconInfo.convertType = '61'
          break
        case 'rar':
        case 'zip':
        case '7z':
        case 'tar':
        case 'gz':
        case 'jar':
        case 'ios': // 压缩格式
          iconInfo.name = 'icon_zip.png'
          iconInfo.type = 'compression'
          iconInfo.convertType = '19'
          break
        case 'mp4':
        case 'avi':
        case 'flv':
        case 'f4v':
        case 'webm':
        case 'm4v':
        case 'mov':
        case '3gp':
        case 'rm':
        case 'rmvb':
        case 'mkv': // 视频格式
          iconInfo.name = 'icon_mp4.png'
          iconInfo.type = 'video'
          break
        case 'mp3':
        case 'amr':
        case 'pcm':
        case 'wav':
        case 'aiff':
        case 'aac':
        case 'ogg':
        case 'wma':
        case 'flac':
        case 'alac':
        case 'cda': // 音频格式
          iconInfo.name = 'icon_mp3.png'
          iconInfo.type = 'voice'
          break
      }
    } catch (e) {
      console.error(e.message)
    }
    return iconInfo
  },
  // 获取文件大小
  getFileSize: function (_fileSize) {
    if (!_fileSize && _fileSize !== 0) return ''
    try {
      var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1))
      var size2 = parseFloat((_fileSize / 1024).toFixed(1))
      if (size1 >= 1) {
        return size1 + 'MB'
      } else if (size2 >= 1) {
        return size2 + 'KB'
      } else {
        return parseInt(_fileSize) + 'B'
      }
    } catch (e) {
      return _fileSize
    }
  },
  // 判断是否是WebView
  isWebView: function () {
    var host = window.location.host
    var path = window.location.href
    // eslint-disable-next-line eqeqeq, no-mixed-operators
    if (host == '' && ((path.toLowerCase().indexOf('file:///storage') > -1)) || ((path.toLowerCase().indexOf('file:///android_asset') > -1)) || ((path.toLowerCase().indexOf('file:///data') > -1)) || (path.toLowerCase().indexOf('file:///var/') > -1) || (path.toLowerCase().indexOf('contents:///') > -1) || (path.toLowerCase().indexOf('file:///private/') > -1)) {
      return true
    } else {
      return false
    }
  },
  // 判断是否是APICloud
  isAPICloud: function () {
    // eslint-disable-next-line no-undef
    if (typeof api !== 'undefined' && typeof api.openWin !== 'undefined') {
      return true
    } else {
      return false
    }
  },
  // 对象是否为  某种类型
  isTargetType: function (obj, typeString) {
    // eslint-disable-next-line valid-typeof
    return typeof obj === typeString
  },
  // 是否为方法
  isFunction: function (obj) {
    return this.isTargetType(obj, 'function')
  },
  // 是否为字符串
  isString: function (obj) {
    return this.isTargetType(obj, 'string') && obj != null && obj !== undefined
  },
  // 是否为 正整数 数字
  isPositiveInteger: function (_int) {
    var reg = /^[\d]+$/ // ^-?[0-9]\d*$   正则数字
    return reg.test(_int) && _int != null && _int !== undefined
  },
  // 是否为数组
  isArray: function (arr) {
    return (toString.apply(arr) === '[object Array]') || arr instanceof NodeList
  },
  // 是否为对象
  isObject: function (obj) {
    return this.isParameters(obj) && this.isTargetType(obj, 'object')
  },
  // 是否为参数
  isParameters: function (obj) {
    return obj != null && obj !== undefined
  },
  /**
     * 一个旧json 和一个新json 合成一个新的json 默认替换 param【旧对象，新对象，传true或1 就不替换】
     */
  setNewJSON: function (obj, newobj, _ifReplace) {
    // 重写 不然一个页面写2次 比如framegroup的时候	就会重复的问题 只能这样 json里有方法
    var returnObj = {}
    for (var key in obj) {
      returnObj[key] = obj[key]
    }
    // eslint-disable-next-line no-redeclare
    for (var key in newobj) {
      if (_ifReplace) {
        // eslint-disable-next-line no-prototype-builtins
        if (returnObj.hasOwnProperty(key)) { // 加上就不替换	不加就替换
          continue
        }
      }
      returnObj[key] = newobj[key]
    }
    return returnObj
  },
  /**
   *	发送事件监听
   */
  sendEvent: function (_param) {
    if (this.isAPICloud()) {
      // eslint-disable-next-line no-undef
      api.sendEvent(_param)
    }
  },
  /**
   *	设置事件监听-----name名字, callback回调方法
   */
  addEventListener: function (name, callback) {
    if (this.isAPICloud()) {
      // eslint-disable-next-line no-undef
      api.addEventListener({ name: name, extra: { threshold: 50 } }, function (ret, err) {
        if (this.isFunction(callback)) {
          callback(ret, err)
        }
      })
    }
  },
  /**
   *	打开frame(有特殊大小的frame请自行调用api.openFrame处理)-----name页面名字,url页面地址,pageParam传值参数,reload重新载入时是否刷新,allowEdit是否可长按复制,marginTop头部距离顶部多少,marginBottom底下距离多少,animation动画划动边 top right bottom left,progress传page时为进度条
   */
  openFrame: function (name, url, pageParam, reload, allowEdit, marginTop, marginBottom, animation, progress) {
    var anima = animation ? { type: 'push', subType: 'from_' + animation, duration: 300 } : { type: 'none' }
    var o = {
      name: name,
      url: url,
      rect: {
        x: 0,
        y: 0,
        w: 'auto',
        h: 'auto',
        marginLeft: 0,
        marginTop: marginTop ? marginTop - 1 : 0,
        marginBottom: marginBottom || 0,
        marginRight: 0
      },
      pageParam: pageParam || {},
      bounces: false,
      vScrollBarEnabled: true,
      hScrollBarEnabled: true,
      scaleEnabled: true,
      progress: {
        type: progress || 'default',
        title: '加载中',
        text: '请稍候...',
        color: '#45C01A'
      },
      reload: reload,
      allowEdit: true, // 去除设置 默认都可以复制粘贴
      animation: anima,
      overScrollMode: 'scrolls',
      defaultRefreshHeader: 'swipe'
    }
    // 适应新写法
    if (this.isObject(name)) {
      o = this.setNewJSON(o, name)
    }
    o.pageParam.areaId = o.pageParam.areaId || ''
    if (this.isAPICloud()) {
      // eslint-disable-next-line no-undef
      api.openFrame(o)
    } else {

    }
  },
  formatDate: function (date, fmt) {
    date = new Date(date)
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds()
    }
    for (const k in o) {
      if (new RegExp(`(${k})`).test(fmt)) {
        const str = o[k] + ''
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str))
      }
    }
    return fmt
  },

  getColorStatus: function (_state, _scenes) {
    var that = this
    var nItem = {}
    switch (_state) {
      case '报名中': case '未开始': case '预约会议': nItem = { t: '#00D089' }; break
      case '签到中': nItem = { t: '#007BFF' }; break
      case '会议中': case '征集中': case '进行中': case '活动中': nItem = { t: that.data.appTheme }; break
      default: nItem = { t: '#cccccc' }; break
    }
    if (_scenes === 'bg') {
      return 'background: ' + that.colorRgba(nItem.t, '0.42') + ';'
    } else if (_scenes === 'angle') {
      return 'border-top: ' + ((that.appFontSize - 2) * 0.01) + 'rem solid ' + that.colorRgba(nItem.t, '1') + ';border-right: ' + ((that.appFontSize - 2) * 0.01) + 'rem solid transparent;'
    } else if (_scenes === 'text') {
      return 'color: ' + that.colorRgba(nItem.t, '1') + ';'
    } else if (_scenes === 'color') {
      return nItem.t
    }
  },
  isColorDarkOrLight: function (hexcolor) {
    try {
      var colorrgb = this.colorRgb(hexcolor)
      var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)
      var red = colors[1]
      var green = colors[2]
      var blue = colors[3]
      var brightness
      brightness = (red * 299) + (green * 587) + (blue * 114)
      brightness = brightness / 255000
      if (brightness >= 0.5) {
        return 'light'
      } else {
        return 'dark'
      }
    } catch (e) {
      return ''
    }
  },
  /** 16进制颜色 转换成rgba颜色	可设置透明 */
  colorRgba: function (_color, _alpha) {
    if (!_color) return
    // 16进制颜色值的正则
    var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
    // 把颜色值变成小写
    var color = _color.toLowerCase()
    if (reg.test(color)) {
      // 如果只有三位的值，需变成六位，如：#fff => #ffffff
      if (color.length === 4) {
        var colorNew = '#'
        for (var i = 1; i < 4; i += 1) {
          colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1))
        }
        color = colorNew
      }
      // 处理六位的颜色值，转为RGB
      var colorChange = []
      // eslint-disable-next-line no-redeclare
      for (var i = 1; i < 7; i += 2) {
        colorChange.push(parseInt('0x' + color.slice(i, i + 2)))
      }
      // return 'rgba(' + colorChange.join(',') + ',' + ('1') + ')'
      return 'rgba(' + colorChange.join(',') + ',' + (this.isParameters(_alpha) ? _alpha : '1') + ')'
    } else {
      return color
    }
  },
  formatSeconds: function (value, _specific) {
    if (value < 0) return ''
    var theTime = parseInt(value) // 需要转换的时间秒
    var theTime1 = 0 // 分
    var theTime2 = 0 // 小时
    var theTime3 = 0 // 天
    if (theTime > 60) {
      theTime1 = parseInt(theTime / 60)
      theTime = parseInt(theTime % 60)
      if (theTime1 > 60) {
        theTime2 = parseInt(theTime1 / 60)
        theTime1 = parseInt(theTime1 % 60)
        if (theTime2 > 24) { // 大于24小时
          theTime3 = parseInt(theTime2 / 24)
          theTime2 = parseInt(theTime2 % 24)
        }
      }
    }
    var result = ''
    if (theTime > 0) {
      result = '' + parseInt(theTime) + '秒'
    }
    if (theTime1 > 0) {
      result = '' + parseInt(theTime1) + '分' + (_specific ? result : '')
    }
    if (theTime2 > 0) {
      result = '' + parseInt(theTime2) + '小时' + (_specific ? result : '')
    }
    if (theTime3 > 0) {
      result = '' + parseInt(theTime3) + '天' + (_specific ? result : '')
    }
    return result
  },
  // 获取当前主题色相对应前景色
  getHeadThemeRelatively: function (isTheme) {
    var that = this
    if (isTheme) {
      return '#ffffff'
    } else {
      if (this.data.headTheme) {
        // eslint-disable-next-line eqeqeq
        return this.isColorDarkOrLight(this.data.headTheme) == 'light' ? '#333' : that.appTheme
      }
    }
  },
  /**
   *  常用js方法
   * */
  uzStorage: function () {
    var ls = window.localStorage
    return ls
  },
  trim: function (str) {
    if (String.prototype.trim) {
      return str == null ? '' : String.prototype.trim.call(str)
    } else {
      return str.replace(/(^\s*)|(\s*$)/g, '')
    }
  },
  trimAll: function (str) {
    return str.replace(/\s*/g, '')
  },
  isElement: function (obj) {
    // eslint-disable-next-line eqeqeq
    return !!(obj && obj.nodeType == 1)
  },
  dom: function (el, selector) {
    if (arguments.length === 1 && typeof arguments[0] === 'string') {
      if (document.querySelector) {
        return document.querySelector(arguments[0])
      }
    } else if (arguments.length === 2) {
      if (el.querySelector) {
        return el.querySelector(selector)
      }
    }
  },
  domAll: function (el, selector) {
    if (arguments.length === 1 && typeof arguments[0] === 'string') {
      if (document.querySelectorAll) {
        return document.querySelectorAll(arguments[0])
      }
    } else if (arguments.length === 2) {
      if (el.querySelectorAll) {
        return el.querySelectorAll(selector)
      }
    }
  },
  byId: function (id) {
    return document.getElementById(id)
  },
  remove: function (el) {
    if (el && el.parentNode) {
      el.parentNode.removeChild(el)
    }
  },
  attr: function (el, name, value) {
    if (!this.isElement(el)) {
      console.warn('$api.attr Function need el param, el param must be DOM Element')
      return
    }
    // eslint-disable-next-line eqeqeq
    if (arguments.length == 2) {
      return el.getAttribute(name)
      // eslint-disable-next-line eqeqeq
    } else if (arguments.length == 3) {
      el.setAttribute(name, value)
      return el
    }
  },
  hasCls: function (el, cls) {
    if (!this.isElement(el)) {
      console.warn('$api.hasCls Function need el param, el param must be DOM Element')
      return
    }
    if (el.className.indexOf(cls) > -1) {
      return true
    } else {
      return false
    }
  },
  addCls: function (el, cls) {
    if (!this.isElement(el)) {
      console.warn('$api.addCls Function need el param, el param must be DOM Element')
      return
    }
    if ('classList' in el) {
      el.classList.add(cls)
    } else {
      var preCls = el.className
      var newCls = preCls + ' ' + cls
      el.className = newCls
    }
    return el
  },
  removeCls: function (el, cls) {
    if (!this.isElement(el)) {
      console.warn('$api.removeCls Function need el param, el param must be DOM Element')
      return
    }
    if ('classList' in el) {
      el.classList.remove(cls)
    } else {
      var preCls = el.className
      var newCls = preCls.replace(cls, '')
      el.className = newCls
    }
    return el
  },
  toggleCls: function (el, cls) {
    if (!this.isElement(el)) {
      console.warn('$api.toggleCls Function need el param, el param must be DOM Element')
      return
    }
    if ('classList' in el) {
      el.classList.toggle(cls)
    } else {
      if (this.hasCls(el, cls)) {
        this.removeCls(el, cls)
      } else {
        this.addCls(el, cls)
      }
    }
    return el
  },
  val: function (el, val) {
    if (!this.isElement(el)) {
      console.warn('$api.val Function need el param, el param must be DOM Element')
      return
    }
    if (arguments.length === 1) {
      switch (el.tagName) {
        case 'SELECT':
          var value = el.options[el.selectedIndex].value
          return value
        case 'INPUT':
          return el.value
        case 'TEXTAREA':
          return el.value
      }
    }
    if (arguments.length === 2) {
      switch (el.tagName) {
        case 'SELECT':
          el.options[el.selectedIndex].value = val
          return el
        case 'INPUT':
          el.value = val
          return el
        case 'TEXTAREA':
          el.value = val
          return el
      }
    }
  },
  html: function (el, html) {
    if (!this.isElement(el)) {
      console.warn('$api.html Function need el param, el param must be DOM Element')
      return
    }
    if (arguments.length === 1) {
      return el.innerHTML
    } else if (arguments.length === 2) {
      el.innerHTML = html
      return el
    }
  },
  offset: function (el) {
    if (!this.isElement(el)) {
      console.warn('$api.offset Function need el param, el param must be DOM Element')
      return
    }
    var sl = Math.max(document.documentElement.scrollLeft, document.body.scrollLeft)
    var st = Math.max(document.documentElement.scrollTop, document.body.scrollTop)

    var rect = el.getBoundingClientRect()
    return {
      l: rect.left + sl,
      t: rect.top + st,
      w: el.offsetWidth,
      h: el.offsetHeight
    }
  },
  css: function (el, css) {
    if (!this.isElement(el)) {
      console.warn('$api.css Function need el param, el param must be DOM Element')
      return
    }
    if (typeof css === 'string' && css.indexOf(':') > 0) {
      el.style && (el.style.cssText += ';' + css)
    }
  },
  cssVal: function (el, prop) {
    if (!this.isElement(el)) {
      console.warn('$api.cssVal Function need el param, el param must be DOM Element')
      return
    }
    if (arguments.length === 2) {
      var computedStyle = window.getComputedStyle(el, null)
      return computedStyle.getPropertyValue(prop)
    }
  },
  jsonToStr: function (json) {
    if (typeof json === 'object') {
      return JSON && JSON.stringify(json)
    }
  },
  strToJson: function (str) {
    if (typeof str === 'string') {
      return JSON && JSON.parse(str)
    }
  },
  setStorage: function (key, value) {
    if (arguments.length === 2) {
      var v = value
      if (typeof v === 'object') {
        v = JSON.stringify(v)
        v = 'obj-' + v
      } else {
        v = 'str-' + v
      }
      var ls = this.uzStorage()
      if (ls) {
        ls.setItem(key, v)
      }
    }
  },
  getStorage: function (key) {
    var ls = this.uzStorage()
    if (ls) {
      var v = ls.getItem(key)
      if (!v) {
        return
      }
      if (v.indexOf('obj-') === 0) {
        v = v.slice(4)
        return JSON.parse(v)
      } else if (v.indexOf('str-') === 0) {
        return v.slice(4)
      }
    }
  },
  rmStorage: function (key) {
    var ls = this.uzStorage()
    if (ls && key) {
      ls.removeItem(key)
    }
  },
  clearStorage: function () {
    var ls = this.uzStorage()
    if (ls) {
      ls.clear()
    }
  },
  padLeftZero: function (str) {
    return ('00' + str).substr(str.length)
  }
}
