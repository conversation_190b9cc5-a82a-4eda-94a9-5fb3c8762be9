<template>
  <div :id="chartId" class="chart"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Pie<PERSON><PERSON>',
  props: {
    id: {
      type: String,
      default: () => ''
    },
    chartData: {
      type: Array,
      default: () => []
    },
    radius: {
      type: Array,
      default: () => ['26%', '42%']
    },
    center: {
      type: Array,
      default: () => ['50%', '35%']
    },
    startAngle: {
      type: Number,
      default: 0
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    legendPosition: {
      type: String,
      default: 'bottom'
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null
    // 初始化图表
    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
            confine: true,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'transparent',
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            extraCssText: 'border-radius: 4px; padding: 8px 12px;'
          },
          legend: {
            show: props.showLegend,
            orient: 'horizontal',
            bottom: 8,
            top: props.legendPosition === 'top' ? 10 : 'auto',
            // left: 30,
            // right: 30,
            textStyle: {
              fontSize: 12,
              color: '#999'
            },
            itemWidth: 18,
            itemHeight: 8,
            itemGap: 22,
            formatter: function (name) {
              const item = props.chartData.find(data => data.name === name)
              if (item) {
                return `${name} ${item.value}人 ${item.percentage}`
              }
              return name
            }
          },
          series: [
            {
              type: 'pie',
              // minAngle: 20,
              radius: props.radius,
              center: props.center,
              startAngle: props.startAngle,
              avoidLabelOverlap: true,
              label: {
                show: props.showLabel,
                position: 'outside',
                formatter: function (params) {
                  return params.name + '\n' + '{c|' + params.value + '人}'
                },
                rich: {
                  c: {
                    color: '#666',
                    fontSize: 14
                  }
                },
                fontSize: 11,
                color: '#999',
                lineHeight: 18
              },
              labelLine: {
                show: props.showLabel,
                length: 10,
                length2: 20,
                smooth: false
              },
              data: props.chartData.map(item => ({
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: item.color
                }
              }))
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    // 监听窗口大小变化
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
