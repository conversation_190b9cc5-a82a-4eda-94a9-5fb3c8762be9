<template>
  <div class="newsList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>
    <div v-if="conversationType == 3"
         :style="'font-size:17px;'">
      <van-tabs v-model:active="switchs.value"
                @click="tabClick"
                :color="appTheme"
                :title-active-color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :title="item.label"
                 :name="item.value"></van-tab>
      </van-tabs>
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <ul class="vue_newslist_box">
          <van-checkbox-group v-model="sendFile">
            <van-swipe-cell v-for="(item,index) in dataList"
                            :key="index"
                            class="van-hairline--bottom">
              <van-cell clickable
                        class="vue_newslist_item ">
                <template v-if="send"
                          #right-icon>
                  <van-checkbox @click.stop
                                :name="item.id"
                                ref="checkboxs"
                                :checked-color="appTheme"
                                :icon-size="24"></van-checkbox>
                </template>
                <div class="flex_box"
                     @click="toggle(item,index)">
                  <img class="vue_newslist_img"
                       v-if="item.url"
                       :src="item.url" />
                  <div class="flex_placeholder vue_newslist_warp T-flexbox-vertical">
                    <div class="vue_newslist_title text_two">
                      <span v-if="item.isPublic == '1' && switchs.value == '1'"
                            class="vue_newslist_top"
                            :style="'font-size:12px;'">
                        <van-tag plain
                                 :color="appTheme">已公开</van-tag>
                      </span>
                      {{item.name}}
                    </div>
                    <div class="T-flex-item"></div>
                    <div class="">
                      <div class="vue_newslist_time"
                           :style="'font-size:13px;margin-bottom:9px;'">{{moment(item.createTime).format('YYYY-M-D HH:mm')}}</div>
                      <div class="flex_box flex_align_center vue_newslist_more">
                        <div class="vue_newslist_time"
                             :style="'font-size:13px;'">{{item.fileSizeName}}</div>
                        <div class="vue_newslist_source"
                             :style="'font-size:13px;'">{{item.owner}}</div>
                        <div class="flex_placeholder"></div>
                      </div>
                      <div style="clear: both;"></div>
                    </div>
                  </div>
                </div>
              </van-cell>
              <!--不能换行 会有多的空间-->
              <template v-slot:right>
                <van-button square
                            color="#bbb"
                            @click="annexClick(item.attach)"
                            :text="'打开'"></van-button>
                <van-button v-if="switchs.value == '1'"
                            color="#ff9c00"
                            square
                            type="danger"
                            @click="editFile(item)"
                            text="编辑"></van-button>
                <van-button v-if="switchs.value == '1'"
                            square
                            type="danger"
                            @click="deleteFile(item,index)"
                            text="删除"></van-button>
              </template>
            </van-swipe-cell>
          </van-checkbox-group>
        </ul>
        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton"
             class="notText">
          <van-skeleton v-for="(item,index) in 3"
                        :key="index"
                        title
                        :row="3"></van-skeleton>
        </div>
      </van-list>
    </van-pull-refresh>
    <footer class="footerBtn">
      <div class="footerBtnBox flex_box">
        <van-button @click="uploadFile"
                    type="primary"
                    block>上传文件</van-button>
        <van-button v-if="sendFile.length != 0"
                    @click="send"
                    type="primary"
                    block>发送</van-button>
      </div>
    </footer>
    <van-popup v-model:show="showpopup"
               position="top"
               @close="nowEditItem=''"
               round
               :style="{ height: '70%' }">
      <div class="uploadBox">
        <!--普通的输入框-->
        <template v-if="nowEditItem">
          <div class="list_item">
            <van-field size="large"
                       label="标题"
                       v-model="name"
                       :required="true"
                       clearable
                       :placeholder="'标题'"></van-field>
          </div>
        </template>
        <div class="list_item setting_item">
          <van-cell :required="true"
                    :data="getSelData()"
                    center>
            <div class="flex_box flex_align_center"
                 :style="$general.loadConfiguration(1)">
              <span class="text_one2 inherit">{{'文件归类'}}</span>
              <div class="flex_placeholder"></div>
              <div class="flex_box inherit">
                <van-dropdown-menu class="add_select"
                                   :active-color="appTheme">
                  <van-dropdown-item style="width:100%;"
                                     @change="selChange"
                                     v-model="dropdownvalue"
                                     :options="dropdownoption"></van-dropdown-item>
                </van-dropdown-menu>
              </div>
            </div>
          </van-cell>
        </div>
        <van-uploader v-if="!nowEditItem"
                      v-model="fileList"
                      :max-count='1'
                      result-type="file"
                      accept="image/*,.doc,.docx,.pdf,.pptx,.ppt,.xlsx,.xls"
                      :after-read="afterRead">
          <van-button icon="plus"
                      type="primary">上传文件</van-button>

        </van-uploader>
      </div>
      <footer class="footerBtn"
              style="bottom:30%">
        <div class="footerBtnBox flex_box">
          <van-button @click="addFileinfo"
                      type="primary"
                      block>保存</van-button>
        </div>
      </footer>
    </van-popup>
  </div>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import * as RongIMLib from '@rongcloud/imlib-next'
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { Toast, NavBar, Sticky, ActionSheet, DropdownMenu, DropdownItem, Icon, Uploader, SwipeCell } from 'vant'
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'newsList',
  components: {
    [SwipeCell.name]: SwipeCell,
    [Uploader.name]: Uploader,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [ActionSheet.name]: ActionSheet,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      send: route.query.send || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      footerBtns: [{ name: '上传文件', type: 'btn', click: 'upload' }], // 底部按钮集合 top为返回顶部btn为按钮
      switchs: { value: '1', data: [{ label: '我的文件', value: '1' }, { label: '群文件', value: '2' }] },
      sendFile: [],
      id: route.query.id || '',
      conversationType: Number(route.query.conversationType), // 聊天 类型 个人还是群
      name: '',
      nowEditItem: '',
      nowItem: '',
      nowIndex: '',
      show: false,
      description: '',
      actions: [
        { name: '', color: '#ee0a24' }
      ],
      showpopup: false,
      dropdownvalue: '',
      dropdownoption: [
        { text: '全部商品', value: 0 },
        { text: '新款商品', value: 1 },
        { text: '活动商品', value: 2 }
      ],
      fileList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      // RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
      // RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
      //   console.log(res)
      //   onRefresh()
      // })
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      console.log(newName, oldName)
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    const tabClick = () => {
      onRefresh()
    }
    // 列表请求
    const getList = async () => {
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        keyWords: data.keyword,
        isMine: data.switchs.value === '1',
        groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      var res = {}
      console.log(data.switchs.value)
      if (data.switchs.value !== '1') {
        res = await $api.rongCloud.getTeamGroupFaildsList(datas)
      } else {
        res = await $api.rongCloud.getFileinfoList(datas)
      }
      var { data: list, total } = res
      list.forEach(item => {
        if (item.fileName) { // 是群文件
          item.id = item.fileId
          item.name = item.fileName
          item.createTime = item.createDate
          item.owner = item.createName
        }
        item.nItemPath = encodeURI(axios.defaults.baseURL + '/' + (item.fileName ? 'talkroup' : 'fileinfo') + '/download/' + item.id + '?loginAreaId=' + data.user.areaId + '&loginToken=' + JSON.parse(sessionStorage.getItem('token'))) // 附件下载地址
        item.fileAttr = $general.getFileTypeAttr(item.fileType)
        item.url = require('../../../assets/img/fileicon/' + item.fileAttr.name)
        if (item.fileAttr.type === 'image') {
          item.url = item.nItemPath
        }
        item.fileSizeName = item.fileName ? item.fileSize : $general.getFileSize(item.fileSize)
        item.attach = { url: item.nItemPath, state: 0, schedule: -1, name: item.name + item.fileType }// 它文件没有后缀 用id取一个名字
      })
      data.dataList = data.dataList.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    const onClickLeft = () => history.back()
    const details = (row) => {
      console.log(row)
      // eslint-disable-next-line eqeqeq
    }
    const getSelData = async () => {
      data.dropdownoption = [{ text: '请选择', value: '' }]
      const { data: selData } = await $api.general.pubkvs({ types: 'file_info_class' })
      var newData = []
      selData.file_info_class.forEach(element => {
        newData.push({ text: element.value, value: element.id })
      })
      data.dropdownoption = data.dropdownoption.concat(newData)
      console.log(data.dropdownoption)
    }
    const selChange = (item) => {
      console.log(item)
    }
    const afterRead = async (file) => {
      console.log(file)
    }
    const addFileinfo = async () => {
      console.log(data.fileList)
      if (!data.dropdownvalue) {
        Toast('请选择文件归类')
        return
      }
      if (data.nowEditItem) {
        var datas = {
          id: data.nowEditItem.id,
          fileClass: data.dropdownvalue,
          name: data.name
        }
        const { data: msg } = await $api.rongCloud.editFileinfo(datas)
        console.log(msg)
        if (msg) {
          data.showpopup = false
          onRefresh()
        }
      } else {
        if (data.fileList.length === 0) {
          Toast('请选择文件')
          return
        }
        const attach = data.fileList[0].file
        const formData = new FormData()
        formData.append('file', attach)
        formData.append('isPublic', '0')
        formData.append('fileClass', data.dropdownvalue)
        console.log(formData)
        const { data: msg } = await $api.rongCloud.addFileinfo(formData)
        console.log(msg)
        if (msg) {
          data.showpopup = false
          onRefresh()
        }
      }
    }
    const editFile = async (item) => {
      data.nowEditItem = item
      const { data: File } = await $api.rongCloud.getFileinfo({ id: item.id })
      if (File) {
        data.name = File.name || ''
        data.dropdownvalue = File.fileClass || ''
        data.showpopup = true
      }
    }
    const uploadFile = () => {
      data.showpopup = true
    }
    const send = () => {
      if (data.sendFile.length === 0) {
        Toast('请选择要发送的文件')
        return
      }
      data.sendFile.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        var nItem = $general.getItemForKey(_eItem, data.dataList, 'id')
        if (nItem) {
          sendRichContentMessage('[文件]', nItem)
        }
      })
    }
    const sendRichContentMessage = (sendType, _eItem) => {
      const conversation = { conversationType: data.conversationType, targetId: data.id }
      // 实例化待发送消息，RongIMLib.TextMessage 为内置文本型消息
      const message = new RongIMLib.RichContentMessage({
        content: sendType + ',' + JSON.stringify(_eItem),
        title: sendType,
        description: sendType + ',' + JSON.stringify(_eItem),
        extra: JSON.stringify({ userInfo: { nickName: data.user.userName.userName, photo: data.user.userName.headImg } })
      })
      // 发送
      RongIMLib.sendMessage(conversation, message).then(res => {
        if (res) {
          Toast('发送成功')
          setTimeout(() => {
            onClickLeft()
          }, 1000)
        } else {
          Toast('发送失败')
        }
      })
    }
    const deleteFile = async (_item, _index) => {
      var res = await $api.rongCloud.delFileinfo({ id: _item.id })
      if (res) {
        Toast(res.errmsg)
        if (res.errcode === 200) {
          data.dataList.splice(_index, 1)
        }
      }
    }
    const annexClick = (item) => {
      router.push({ name: 'superFile', query: item })
    }
    const toggle = (_item, _index) => {
      data.nowItem = _item
      data.nowIndex = _index
      data.description = '操作'
      data.actions = [
        { name: '选中' },
        { name: '打开' }
      ]
      if (data.switchs.value === '1') {
        data.actions = [
          { name: '从我的文件删除', color: '#ee0a24' },
          { name: '选中' },
          { name: '打开' },
          { name: '编辑' }
        ]
      }
      data.show = true
    }
    const onSelect = (item) => {
      switch (item.name) {
        case '从我的文件删除':
          data.description = '是否将【' + data.nowItem.name + '】从我的文件中删除？'
          data.actions = [
            { name: '删除', color: '#ee0a24' }
          ]
          data.show = true
          break
        case '删除':
          deleteFile(data.nowItem, data.nowIndex)
          break
        case '选中':
          var isHave = false
          data.sendFile.forEach((element, index) => {
            if (element === data.nowItem.id) {
              isHave = true
              data.sendFile.splice(index, 1)
            }
          })
          if (!isHave) {
            data.sendFile.push(data.nowItem.id)
          }
          break
        case '打开':
          annexClick(data.nowItem.attach)
          break
        case '编辑':
          editFile(data.nowItem)
          break
      }
    }
    return { ...toRefs(data), $general, moment, search, onClickLeft, onRefresh, onLoad, details, toggle, onSelect, send, uploadFile, editFile, tabClick, annexClick, deleteFile, getSelData, selChange, afterRead, addFileinfo }
  }
}
</script>

<style lang="less" scoped>
.newsList {
  width: 100%;
  background: #fff;
  .footerBtn {
    background: #fff;
    padding: 5px 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    .footerBtnBox {
      width: calc(100% - 20px);
      margin-left: 10px;
      .van-button + .van-button {
        width: calc(100% - 20px);
        margin-left: 10px;
      }
    }
  }
  .uploadBox {
    width: calc(100% - 20px);
    margin-left: 10px;
    .list_item {
      margin-bottom: 14px;
      font-size: inherit;
      font-family: inherit;
    }
    .add_select {
      margin: 0 5px;
      width: 100%;
    }
    .add_select .van-dropdown-menu__item {
      -webkit-justify-content: flex-end;
      justify-content: flex-end;
      -moz-box-pack: flex-end;
      -webkit--moz-box-pack: flex-end;
      box-pack: flex-end;
    }
  }
}
</style>
