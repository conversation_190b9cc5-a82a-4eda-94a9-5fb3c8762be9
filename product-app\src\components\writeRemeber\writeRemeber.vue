<template>
  <div class="writeRemeber">
    <div id="view"
         v-if="shows">
      <img style="width:100%;height:100%;"
           :src="img"
           alt="">
    </div>
    <van-popup v-model:show="shows"
               overlay="true"
               position="bottom"
               :style="{ height: '220px' }">
      <van-field v-model="value"
                 type="textarea"
                 rows="4"
                 autosize
                 required
                 label=""
                 placeholder="请输入笔记内容" />
      <van-cell title="公开笔记"
                value=""
                class="wirteBut">
        <template #value>
          <!-- <van-checkbox style="width:20px;float:right"
                        v-model="checked"></van-checkbox> -->
          <van-switch v-model="checked" />
        </template>
      </van-cell>
      <div class="bottomBut">
        <van-button @click="back"
                    type="warning">取消</van-button>
        <!-- <van-button type="primary">保存到相册</van-button> -->
        <van-button @click="istrue(id)"
                    type="primary">确定</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
// import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Popup, cell, CellGroup, button, checkbox, Toast, Switch } from 'vant'
// import html2canvas from 'html2canvas'
export default {
  name: 'writeRemeber',
  components: {
    [Popup.name]: Popup,
    [cell.name]: cell,
    [CellGroup.name]: CellGroup,
    [button.name]: button,
    [Switch.name]: Switch,
    [checkbox.name]: checkbox
  },
  setup () {
    const data = reactive({
      value: '',
      shows: false,
      checked: false,
      img: '',
      id: ''
    })
    const $api = inject('$api')
    // const { show } = toRefs(props)
    onMounted(() => {
      // data.shows = show.value
      // if (show.value) {
      //   takeScreenshot()
      //   data.shows = show.value
      // }
    })
    // 确定
    const istrue = async (id) => {
      console.log(data.value)
      if (data.value === '') {
        Toast({
          message: '请填写笔记内容',
          position: 'bottom'
        })
      } else {
        // const { data: msg } = await $api.general.uploadFile({ module: 'booknote', siteId: id, attachment: data.img })
        // console.log(id, msg)
        // if (msg) {
        const res = await $api.general.add({ noteContent: data.value, bookId: data.id, screenshot: data.img })
        if (res.errcode === 200) {
          if (data.checked) {
            const { data: msg3 } = await $api.general.delsOpen({ ids: res.data })
            console.log(msg3)
          }
          Toast({
            message: '新增笔记成功',
            position: 'bottom'
          })
          data.value = ''
          setTimeout(() => {
            back()
          }, 1000)
        }
        // }
      }
    }
    // 返回
    const back = () => {
      data.shows = false
      data.value = ''
      data.checked = false
    }
    const takeScreenshot = (index, id) => {
      // html2canvas(document.body).then(function (canvas) {
      //   // document.getElementById('view').appendChild(canvas)
      //   var imageData = canvas.toDataURL('image/jpeg')
      //   var binary = atob(imageData.split(',')[1])
      //   var array = []
      //   for (var i = 0; i < binary.length; i++) {
      //     array.push(binary.charCodeAt(i))
      //   }
      //   var bodata = new Blob([new Uint8Array(array)])
      //   console.log(bodata)
      //   data.img = imageData
      // })
      data.img = index
      data.id = id
      console.log(data.id)
      setTimeout(() => {
        data.shows = true
      }, 100)
    }
    return { ...toRefs(data), istrue, back, takeScreenshot, Toast }
  }
}
</script>
<style lang="less" scoped>
.writeRemeber {
  width: 100%;
  position: relative;
  #view {
    width: 200px;
    height: 300px;
    position: fixed;
    top: 10%;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 99;
  }
  .van-button {
    margin-left: 10px;
  }
  .bottomBut {
    position: absolute;
    right: 20px;
    bottom: 10px;
  }
  .wirteBut {
    position: absolute;
    right: 0;
    bottom: 60px;
  }
}
</style>
